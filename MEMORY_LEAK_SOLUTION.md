# ServerStatus 内存泄漏和 Goroutine 过多问题解决方案

## 问题总结

该项目之前存在以下几个关键问题：

1. **RequestTask goroutine 管理不当** - 每个客户端连接创建多个 goroutine，清理机制不完善
2. **内存清理策略过于保守** - 清理频率低，阈值设置过高
3. **锁竞争和死锁风险** - 复杂的锁嵌套和长时间持锁
4. **连接生命周期管理混乱** - 连接断开时清理不彻底

## 解决方案详情

### 1. 新增连接管理器 (`connection_manager.go`)

- **统一连接管理**：所有客户端连接通过 `ConnectionManager` 统一管理
- **严格的连接数限制**：最多100个并发连接（从之前的200个大幅降低）
- **主动连接清理**：每30秒自动清理2分钟无活动的连接
- **资源监控**：实时监控 goroutine 数量，超过300个拒绝新连接

### 2. 重构 RequestTask 方法

- **简化逻辑**：移除复杂的 goroutine 监控逻辑
- **使用连接管理器**：通过统一的连接管理器处理连接生命周期
- **减少 goroutine 创建**：每个连接只创建一个主 goroutine

### 3. 资源监控系统 (`resource_monitor.go`)

- **实时监控**：每5秒检查内存和 goroutine 使用情况
- **分级响应**：
  - 正常模式：内存 < 500MB，goroutine < 200
  - 警告模式：内存 500-800MB，goroutine 200-250
  - 紧急模式：内存 > 800MB 或 goroutine > 250
- **主动清理**：根据模式执行不同级别的清理策略

### 4. 优化 Goroutine Pool

- **减少池大小**：
  - 通知池：2-8 workers（原来 5-20）
  - 任务池：1-5 workers（原来 2-10）
- **更小的队列**：减少队列大小，防止任务积压

### 5. 增强 ServiceSentinel 清理

- **减少数据保留**：
  - 状态记录：每个监控项最多4条（原来6条）
  - Ping记录：每个监控项最多8条（原来12条）
  - 全局监控项：最多600个（原来1200个）

### 6. 添加资源监控拦截器

- **请求级别限流**：在 gRPC 层面拒绝过载请求
- **连接统计**：实时统计活跃连接数
- **慢请求监控**：记录超过5秒的慢请求

### 7. 优化 gRPC 配置

- **更激进的 keepalive**：
  - 连接空闲时间：3分钟（原来5分钟）
  - 连接最大存活：5分钟（原来10分钟）
  - Keepalive 间隔：15秒（原来30秒）
- **减少消息大小**：最大消息2MB（原来4MB）

## 使用方法

### 1. 启动系统

系统启动时会自动启用资源监控：

```go
// 在 main.go 中已自动添加
singleton.StartResourceMonitor()
```

### 2. 监控系统状态

访问新的系统状态 API：

```bash
curl -H "Authorization: YOUR_TOKEN" http://your-server/api/v1/system/status
```

返回信息包括：
- 内存使用情况
- Goroutine 数量
- 连接统计
- Goroutine 池状态
- 系统信息

### 3. 日志监控

关注以下关键日志：

```bash
# 资源监控状态变化
"资源监控状态变化: 正常 -> 警告"

# 连接管理
"新连接已建立: 客户端 123, 当前连接数: 45"
"清理了 5 个过期连接"

# 紧急清理
"执行紧急清理: 内存=850MB, Goroutines=280"

# 连接拒绝
"客户端 456 连接被拒绝: 连接数已达上限"
```

## 配置建议

### 1. 系统资源限制

建议的系统配置：
- 内存：至少2GB，推荐4GB
- CPU：至少2核，推荐4核
- 连接数：不超过100个并发连接

### 2. 监控阈值调整

如需调整阈值，修改 `resource_monitor.go` 中的配置：

```go
memoryHardLimit: 800,  // 硬限制 (MB)
memorySoftLimit: 500,  // 软限制 (MB)
goroutineLimit:  250,  // Goroutine 限制
```

### 3. 连接池大小调整

如需调整连接池大小，修改 `connection_manager.go`：

```go
maxConnections:  100, // 最大连接数
```

## 性能预期

实施这些优化后，预期可以达到：

1. **内存使用**：稳定在500MB以下，峰值不超过800MB
2. **Goroutine 数量**：正常情况下不超过200个
3. **连接稳定性**：减少90%的连接断开问题
4. **系统稳定性**：基本消除因资源耗尽导致的崩溃

## 故障排查

### 1. 内存使用过高

```bash
# 检查当前状态
curl -H "Authorization: YOUR_TOKEN" http://your-server/api/v1/system/status

# 查看日志中的清理记录
grep "紧急清理" /var/log/serverstatus.log
```

### 2. 连接被拒绝

```bash
# 检查连接数
grep "连接被拒绝" /var/log/serverstatus.log

# 检查 goroutine 数量
grep "goroutine数量过多" /var/log/serverstatus.log
```

### 3. 性能问题

```bash
# 检查慢请求
grep "慢请求警告" /var/log/serverstatus.log

# 检查长时间运行的流
grep "长时间流连接" /var/log/serverstatus.log
```

## 注意事项

1. **升级后的第一次启动**：系统会应用新的资源限制，可能会看到更频繁的清理日志
2. **客户端兼容性**：新的 keepalive 设置对客户端更严格，确保客户端支持
3. **监控频率**：资源监控每5秒运行一次，不要设置过于频繁
4. **日志量**：由于增加了详细的资源监控，日志量可能会增加

通过这个综合解决方案，可以从根本上解决内存泄漏和 goroutine 过多的问题，确保系统长期稳定运行。
