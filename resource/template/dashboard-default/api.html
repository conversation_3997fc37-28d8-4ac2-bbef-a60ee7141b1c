{{define "dashboard-default/api"}}
{{template "common/header" .}}
{{template "common/menu" .}}
<div class="nb-container">
    <div class="ui container">
        <div class="ui grid">
            <div class="right floated right aligned twelve wide column">
                <button class="ui right labeled server-primary-btn icon button" onclick="issueNewApiToken()"><i class="add icon"></i>
                    {{tr "IssueNewApiToken"}}
                </button>
            </div>
        </div>
        <table class="ui very basic table">
            <thead>
            <tr>
                <th>{{tr "APIToken"}}</th>
                <th>{{tr "Note"}}</th>
                <th>{{tr "Administration"}}</th>
            </tr>
            </thead>
            <tbody>
            {{range $token := .Tokens}}
            <tr>
                <td>{{$token.Token}}</td>
                <td>{{$token.Note}}</td>
                <td>
                    <div class="ui mini icon buttons">
                        <button class="ui icon mini button" data-clipboard-text="{{$token.Token}}">
                            <i class="copy icon"></i>
                        </button>
                        {{if $token.Token}}
                        <button class="ui button"
                                onclick="showConfirm('{{tr "DeleteToken"}}'+'[{{$token.Note}}]','{{tr "ConfirmToDeleteThisToken"}}',deleteRequest,'/api/token/{{$token.Token}}')">
                        <i class="trash alternate outline icon"></i>
                        </button>
                        {{else}}
                        <button class="ui button disabled" title="无效的令牌">
                        <i class="trash alternate outline icon"></i>
                        </button>
                        {{end}}
                    </div>
                </td>
            </tr>
            {{end}}
            </tbody>
        </table>
    </div>
</div>
{{template "component/api"}}
{{template "common/footer" .}}
<script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/clipboard.js/2.0.10/clipboard.min.js"></script>
<script>
    // 初始化clipboard.js
    var clipboard = new ClipboardJS('.ui.icon.mini.button');

    // 添加复制成功提示
    clipboard.on('success', function(e) {
        $.suiAlert({
            title: "复制成功",
            type: "success",
            description: "API Token已复制到剪贴板",
            time: "2",
            position: "top-center",
        });
        e.clearSelection();
    });

    // 添加复制失败提示
    clipboard.on('error', function(e) {
        $.suiAlert({
            title: "复制失败",
            type: "error",
            description: "请手动复制API Token",
            time: "3",
            position: "top-center",
        });
    });
</script>
{{end}}
