{{define "dashboard-default/monitor"}} {{template "common/header" .}} {{template
  "common/menu" .}}
  <div class="nb-container">
    <div class="ui container">
      <div class="ui grid">
        <div class="right floated right aligned twelve wide column">
          <button class="ui right labeled server-primary-btn icon button" onclick="addOrEditMonitor()">
            <i class="add icon"></i> {{tr "AddMonitor"}}
          </button>
        </div>
      </div>
      <table class="ui very basic table">
        <thead>
          <tr>
            <th>ID</th>
            <th>{{tr "Name"}}</th>
            <th>{{tr "Target"}}</th>
            <th>{{tr "Coverage"}}</th>
            <th>{{tr "SpecificServers"}}</th>
            <th>{{tr "Type"}}</th>
            <th>{{tr "Duration"}}</th>
            <th>{{tr "NotificationMethodGroup"}}</th>
            <th>{{tr "FailureNotification"}}</th>
            <th>{{tr "LatencyNotification"}}</th>
            <th>{{tr "EnableTriggerTask"}}</th>
            <th>{{tr "FailTriggerTasks"}}</th>
            <th>{{tr "RecoverTriggerTasks"}}</th>
            <th>{{tr "Administration"}}</th>
          </tr>
        </thead>
        <tbody>
          {{range $monitor := .Monitors}}
          <tr>
            <td>{{$monitor.ID}}</td>
            <td>{{$monitor.Name}}</td>
            <td>{{$monitor.Target}}</td>
            <td>{{if eq $monitor.Cover 0}}{{tr "CoverAll"}}{{else}}{{tr "IgnoreAll"}}{{end}}</td>
            <td>{{$monitor.SkipServersRaw}}</td>
            <td>
            {{if eq $monitor.Type 2}}ICMP Ping {{else if eq $monitor.Type 3}} {{tr "TCPPort"}} {{end}}
            </td>
            <td>{{$monitor.Duration}} {{tr "Seconds"}}</td>
            <td>{{$monitor.NotificationTag}}</td>
            <td>{{$monitor.Notify}}</td>
            <td>{{$monitor.LatencyNotify}}</td>
            <td>{{$monitor.EnableTriggerTask}}</td>
            <td>{{$monitor.FailTriggerTasksRaw}}</td>
            <td>{{$monitor.RecoverTriggerTasksRaw}}</td>
            <td>
              <div class="ui mini icon buttons">
                <button class="ui button" onclick="addOrEditMonitor({{$monitor}})">
                  <i class="edit icon"></i>
                </button>
                <button class="ui button" onclick="showConfirm('{{tr "DeleteService"}}'+'[{{$monitor.Name}}]','{{tr "ConfirmToDeleteThisService"}}',deleteRequest,'/api/monitor/'+{{$monitor.ID}})">
                  <i class="trash alternate outline icon"></i>
                </button>
              </div>
            </td>
          </tr>
          {{end}}
        </tbody>
      </table>
    </div>
  </div>
  {{template "component/monitor"}} {{template "common/footer" .}}
  <script>
    $(".checkbox").checkbox();
  </script>
  {{end}}
