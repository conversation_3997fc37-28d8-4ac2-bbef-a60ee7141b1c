{{define "dashboard-default/nat"}}
{{template "common/header" .}}
{{template "common/menu" .}}
<div class="nb-container">
    <div class="ui container">
        <div class="ui grid">
            <div class="right floated right aligned twelve wide column">
                <button class="ui right labeled server-primary-btn icon button" onclick="addOrEditNAT()"><i
                        class="add icon"></i> {{tr "Add"}}
                </button>
            </div>
        </div>
        <table class="ui basic table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>{{tr "Name"}}</th>
                    <th>Agent ID</th>
                    <th>{{tr "LocalService"}}</th>
                    <th>{{tr "BindHostname"}}</th>
                    <th>{{tr "Administration"}}</th>
                </tr>
            </thead>
            <tbody>
                {{range $item := .NAT}}
                <tr>
                    <td>{{$item.ID}}</td>
                    <td>{{$item.Name}}</td>
                    <td>{{$item.ServerID}}</td>
                    <td>{{$item.Host}}</td>
                    <td>{{$item.Domain}}</td>
                    <td>
                        <div class="ui mini icon buttons">
                            <button class="ui button" onclick="addOrEditNAT({{$item}})">
                                <i class="edit icon"></i>
                            </button>
                            <button class="ui button"
                                onclick="showConfirm('确定删除NAT隧道？','确认删除',deleteRequest,'/api/nat/'+{{$item.ID}})">
                                <i class="trash alternate outline icon"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                {{end}}
            </tbody>
        </table>
    </div>
</div>
{{template "component/nat"}}
{{template "common/footer" .}}
<script>
    $('.checkbox').checkbox()
</script>
{{end}}