{{define "dashboard-default/setting"}}
{{template "common/header" .}}
{{template "common/menu" .}}
<div class="nb-container">
    <div class="ui container">
        <form id="settingForm" class="ui large form" onsubmit="return false;">
            <div class="field">
                <label>{{tr "SiteTitle"}}</label>
                <input type="text" name="Title" placeholder="{{tr "ServerMonitoring"}}" value="{{.Conf.Site.Brand}}">
            </div>
            <div class="field">
                <label>{{tr "AdministratorList"}}</label>
                <input type="text" name="Admin" placeholder="1010,2020" value="{{.Conf.Oauth2.Admin}}">
            </div>
            <div class="field">
                <label>{{tr "Theme"}}</label>
                <select name="Theme">
                    {{range $k,$v := .Themes}}
                    <option value="{{$k}}" {{if eq $.Conf.Site.Theme $k }} selected="selected" {{end}}>{{$v}}
                    {{end}}
                </select>
            </div>
            <div class="field">
                <label>{{tr "DashboardTheme"}}</label>
                <select name="DashboardTheme">
                    {{range $k,$v := .DashboardThemes}}
                    <option value="{{$k}}" {{if eq $.Conf.Site.DashboardTheme $k }} selected="selected" {{end}}>{{$v}}
                        {{end}}
                </select>
            </div>
            <div class="field">
                <label>Language</label>
                <select name="Language">
                    {{range $k,$v := .Languages}}
                    <option value="{{$k}}" {{if eq $.Conf.Language $k }} selected="selected" {{end}}>
                        {{$v}}</option>
                    {{end}}
                </select>
            </div>
            <div class="field">
                <label>{{tr "CustomCodes"}}</label>
                <textarea name="CustomCode">{{.Conf.Site.CustomCode}}</textarea>
            </div>
            <div class="field">
                <label>{{tr "AccessPassword"}}</label>
                <input type="text" name="ViewPassword" placeholder="" value="{{.Conf.Site.ViewPassword}}">
            </div>
            <div class="field">
                <label>{{tr "AgentConfig"}}</label>
            </div>
            <div class="ui segment">
                <div class="field">
                    <label>{{tr "AgentDomainOrIP"}}</label>
                    <input type="text" name="GRPCHost" placeholder="" value="{{.Conf.GRPCHost}}">
                </div>
                <div class="field">
                    <label>{{tr "AgentPort"}}</label>
                    <input type="number" name="GRPCPort" placeholder="" value="{{.Conf.GRPCPort}}">
                </div>
            </div>
            <div class="field">
                <label>{{tr "IPChangeAlert"}}</label>
            </div>
            <div class="ui segment">
                <div class="field">
                    <label>{{tr "Coverage"}}</label>
                    <select name="Cover" class="ui fluid dropdown">
                        <option value=0>{{tr "AllIncludedOnlySpecificServersAreNotAlerted"}}</option>
                        <option value=1>{{tr "IgnoreAllOnlyAlertSpecificServers"}}</option>
                    </select>
                </div>
                <div class="field">
                    <label>{{tr "SpecificServers"}}</label>
                    <input type="text" name="IgnoredIPNotification" placeholder="{{tr "ServerIDSeparatedByCommas"}} 1001,1002,1003"
                        value="{{.Conf.IgnoredIPNotification}}">
                </div>
                <div class="field">
                    <label>{{tr "IPChangeNotificationTag"}}</label>
                    <input type="text" name="IPChangeNotificationTag" placeholder="{{tr "Default"}}" value="{{.Conf.IPChangeNotificationTag}}">
                </div>
                <div class="field">
                    <div class="ui checkbox ip-change">
                        <input name="EnableIPChangeNotification" type="checkbox" tabindex="0" class="hidden">
                        <label>{{tr "Enable"}}</label>
                    </div>
                </div>
            </div>
            <div class="field">
                <div class="ui checkbox plain-ip">
                    <input name="EnablePlainIPInNotification" type="checkbox" tabindex="0" class="hidden">
                    <label>{{tr "NotificationMessagesDoNotHideIP"}}</label>
                </div>
            </div>
            <div class="field">
                <div class="ui checkbox disable-switch-template">
                    <input name="DisableSwitchTemplateInFrontend" type="checkbox" tabindex="0" class="hidden">
                    <label>{{tr "DisableSwitchTemplateInFrontend"}}</label>
                </div>
            </div>
            <button class="ui button" type="submit">{{tr "Save"}}</button>
        </form>
    </div>
</div>
{{template "common/footer" .}}
<script>
    $('#settingForm').submit(function () {
        $.post('/api/setting', $('#settingForm').serialize())
            .then((resp) => {
                if (resp.code == 200) {
                    $.suiAlert({
                        title: '',
                        description: '{{tr "ModifiedSuccessfully"}}',
                        type: 'success',
                        time: '3',
                        position: 'top-center',
                    });
                    window.location.reload()
                } else {
                    $.suiAlert({
                        title: '',
                        description: resp.message,
                        type: 'error',
                        time: '3',
                        position: 'top-center',
                    });
                }
            }).catch(err => {
                $.suiAlert({
                    title: '',
                    description: err,
                    type: 'error',
                    time: '3',
                    position: 'top-center',
                });
            })
        return false;
    })
    $('.checkbox').checkbox()
    $('#settingForm').find("select[name=Cover]").val({{.Conf.Cover }});
    {{if .Conf.EnableIPChangeNotification}}
    $('.checkbox.ip-change').checkbox('set checked')
    {{ end }}
    {{if .Conf.EnablePlainIPInNotification}}
    $('.checkbox.plain-ip').checkbox('set checked')
    {{ end }}
    {{if .Conf.DisableSwitchTemplateInFrontend }}
    $('.checkbox.disable-switch-template').checkbox('set checked')
    {{ end }}
</script>
{{end}}
