{{define "dashboard-default/terminal"}}
<!DOCTYPE html>
<html lang="{{.Conf.Language}}">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta http-equiv="Cache-Control" content="no-transform">
	<meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
	<meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no,viewport-fit=cover">
	<meta name="renderer" content="webkit">
	<meta http-equiv="Cache-Control" content="no-transform">
	<meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
	<meta name="theme-color" content="#0F7D00">
	<meta property="og:image" content="static/logo.png">
	<meta name="msapplication-TileColor" content="#0F7D00">
    <title>TTY@{{.ServerName}} - {{.Title}}</title>
    <link rel="shortcut icon" type="image/png" href="static/favicon.ico" />
    <link type="text/css" rel="stylesheet" href="https://unpkg.com/xterm@5.3.0/css/xterm.css" />
</head>

<style>
    html,
    body,
    #terminal-container {
        padding: unset;
        margin: unset;
        width: 100vw;
        height: 100vh;
    }

    body {
        background-color: black;
    }

    #file-list-iframe {
        position: absolute;
        top: 0;
        right: 0;
        width: 30%;
        height: 100%;
        border: none;
        background-color: white;
        display: none;
        z-index: 10;
    }

    #folder-button {
        position: absolute;
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 25px;
        font-size: 24px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        z-index: 20;
    }
</style>

<body onresize="onResize()">
    <div id="terminal-container"></div>
    <iframe id="file-list-iframe" src=""></iframe>
    <button id="folder-button">📁</button>

    <script src="https://unpkg.com/xterm@5.3.0/lib/xterm.js"></script>
    <script src="https://unpkg.com/@xterm/addon-fit@0.10.0/lib/addon-fit.js"></script>
    <script src="https://unpkg.com/@xterm/addon-web-links@0.11.0/lib/addon-web-links.js"></script>
    <script src="https://unpkg.com/@xterm/addon-attach@0.11.0/lib/addon-attach.js"></script>
    <script>
        let sendResizing = false;

        function doResize() {
            fitAddon.fit()

            const w = fitAddon.proposeDimensions();
            const prefix = new Int8Array([1]);
            const resizeMessage = new TextEncoder().encode(JSON.stringify({
                Rows: w.rows,
                Cols: w.cols,
            }));

            var msg = new Int8Array(prefix.length + resizeMessage.length);
            msg.set(prefix);
            msg.set(resizeMessage, prefix.length);

            socket.send(msg)
        }

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        async function onResize() {
            if (sendResizing) return;
            sendResizing = true;
            try {
                await sleep(1500);
                doResize();
            } catch (error) {
                console.log('resize', error);
            } finally {
                sendResizing = false
            }
        }

        const term = new Terminal({
            screenKeys: true,
            useStyle: true,
            cursorBlink: true,
        });
        const socket = new WebSocket((window.location.protocol == 'https:' ? 'wss' : 'ws') + '://' + window.location.host + '/terminal/' + '{{.SessionID}}');

        const attachAddon = new AttachAddon.AttachAddon(socket);
        term.loadAddon(attachAddon);

        const fitAddon = new FitAddon.FitAddon();
        term.loadAddon(fitAddon);

        term.open(document.getElementById('terminal-container'));

        socket.onopen = () => {
            onResize()
        }

        socket.onclose = () => {
            alert('{{tr "TerminalConnectionTimeOutOrSessionEnded"}}')
            window.close()
        }

        socket.onerror = () => {
            alert('{{tr "TerminalConnectionFailed"}}')
        }

        // 处理文件夹按钮点击事件
        const folderButton = document.getElementById('folder-button');
        const fileListIframe = document.getElementById('file-list-iframe');
        let fileListVisible = false;

        folderButton.addEventListener('click', () => {
            if (!fileListVisible) {
                // 显示文件列表
                const params = new URLSearchParams({
                    id: "{{.ServerID}}"
                }).toString();
                fileListIframe.src = `/file?${params}`;
                fileListIframe.style.display = 'block';
                fileListVisible = true;
            } else {
                // 隐藏文件列表
                fileListIframe.style.display = 'none';
                fileListVisible = false;
            }
        });
    </script>
</body>

</html>
{{end}}
