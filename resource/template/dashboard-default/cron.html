{{define "dashboard-default/cron"}}
{{template "common/header" .}}
{{template "common/menu" .}}
<div class="nb-container">
    <div class="ui container">
        <div class="ui grid">
            <div class="right floated right aligned twelve wide column">
                <button class="ui right labeled server-primary-btn icon button" onclick="addOrEditCron()"><i class="add icon"></i>
                    {{tr "AddScheduledTasks"}}
                </button>
            </div>
        </div>
        <table class="ui very basic table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>{{tr "Name"}}</th>
                    <th>{{tr "TaskType"}}</th>
                    <th>{{tr "Scheduler"}}</th>
                    <th>{{tr "Command"}}</th>
                    <th>{{tr "NotificationMethodGroup"}}</th>
                    <th>{{tr "PushSuccessfully"}}</th>
                    <th>{{tr "Coverage"}}</th>
                    <th>{{tr "SpecificServers"}}</th>
                    <th>{{tr "LastExecution"}}</th>
                    <th>{{tr "LastResult"}}</th>
                    <th>{{tr "Administration"}}</th>
                </tr>
            </thead>
            <tbody>
                {{range $cron := .Crons}}
                <tr>
                    <td>{{$cron.ID}}</td>
                    <td>{{$cron.Name}}</td>
                    <td>{{if eq $cron.TaskType 0}}{{tr "CronTask"}}{{else}}{{tr "TriggerTask"}}{{end}}</td>
                    <td>{{$cron.Scheduler}}</td>
                    <td>{{$cron.Command}}</td>
                    <td>{{$cron.NotificationTag}}</td>
                    <td>{{$cron.PushSuccessful}}</td>
                    <td>{{if eq $cron.Cover 0}}{{tr "IgnoreAll"}}{{else if eq $cron.Cover 1}}{{tr "CoverAll"}}{{else}}{{tr "ByTrigger"}}{{end}}</td>
                    <td>{{$cron.ServersRaw}}</td>
                    <td>{{$cron.LastExecutedAt|tf}}</td>
                    <td>{{$cron.LastResult}}</td>
                    <td>
                        <div class="ui mini icon buttons">
                            <button class="ui button" onclick="manualTrigger(this, {{$cron.ID}})">
                                <i class="play icon"></i>
                            </button>
                            <button class="ui button" onclick="addOrEditCron({{$cron}})">
                                <i class="edit icon"></i>
                            </button>
                            <button class="ui button"
                                onclick="showConfirm('{{tr "DeleteScheduledTask"}}'+'[{{$cron.Name}}]','{{tr "ConfirmToDeleteThisScheduledTask"}}',deleteRequest,'/api/cron/'+{{$cron.ID}})">
                                <i class="trash alternate outline icon"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                {{end}}
            </tbody>
        </table>
    </div>
</div>
{{template "component/cron"}}
{{template "common/footer" .}}
{{end}}