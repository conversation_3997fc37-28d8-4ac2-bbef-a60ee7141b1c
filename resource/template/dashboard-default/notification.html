{{define "dashboard-default/notification"}}
{{template "common/header" .}}
{{template "common/menu" .}}
<div class="nb-container">
    <div class="ui container">
        <div class="ui grid">
            <div class="right floated right aligned twelve wide column">
                <button class="ui right labeled server-primary-btn icon button" onclick="addOrEditNotification()"><i
                        class="add icon"></i> {{tr "AddNotificationMethod"}}
                </button>
            </div>
        </div>
        <table class="ui very basic table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>{{tr "Name"}}</th>
                    <th>{{tr "Tag"}}</th>
                    <th>URL</th>
                    <th>{{tr "VerifySSL"}}</th>
                    <th>{{tr "Administration"}}</th>
                </tr>
            </thead>
            <tbody>
                {{range $notification := .Notifications}}
                <tr>
                    <td>{{$notification.ID}}</td>
                    <td>{{$notification.Name}}</td>
                    <td>{{$notification.Tag}}</td>
                    <td>{{$notification.URL}}</td>
                    <td>{{$notification.VerifySSL}}</td>
                    <td>
                        <div class="ui mini icon buttons">
                            <button class="ui button" onclick="addOrEditNotification({{$notification}})">
                                <i class="edit icon"></i>
                            </button>
                            <button class="ui button"
                                onclick="showConfirm('{{tr "DeleteNotificationMethod"}}'+'[{{$notification.Name}}]','{{tr "ConfirmToDeleteThisNotificationMethod"}}',deleteRequest,'/api/notification/'+{{$notification.ID}})">
                                <i class="trash alternate outline icon"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                {{end}}
            </tbody>
        </table>
        <div class="ui grid">
            <div class="right floated right aligned twelve wide column">
                <button class="ui right labeled server-primary-btn icon button" onclick="addOrEditAlertRule()"><i
                        class="add icon"></i>
                    {{tr "AddNotificationRule"}}
                </button>
            </div>
        </div>
        <table class="ui very basic table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>{{tr "Name"}}</th>
                    <th>{{tr "NotificationMethodGroup"}}</th>
                    <th>{{tr "NotificationTriggerMode"}}</th>
                    <th>{{tr "Rules"}}</th>
                    <th>{{tr "FailTriggerTasks"}}</th>
                    <th>{{tr "RecoverTriggerTasks"}}</th>
                    <th>{{tr "Enable"}}</th>
                    <th>{{tr "Administration"}}</th>
                </tr>
            </thead>
            <tbody>
                {{range $rule := .AlertRules}}
                <tr>
                    <td>{{$rule.ID}}</td>
                    <td>{{$rule.Name}}</td>
                    <td>{{$rule.NotificationTag}}</td>
                    <td>{{if eq $rule.TriggerMode  0}}{{tr "ModeAlwaysTrigger"}}{{else}}{{tr "ModeOnetimeTrigger"}}{{end}}
                    <td>{{$rule.RulesRaw}}</td>
                    <td>{{$rule.FailTriggerTasksRaw}}</td>
                    <td>{{$rule.RecoverTriggerTasksRaw}}</td>
                    <td>{{$rule.Enable}}</td>
                    <td>
                        <div class="ui mini icon buttons">
                            <button class="ui button edit-alert-rule-btn" data-id="{{$rule.ID}}">
                                <i class="edit icon"></i>
                            </button>
                            <button class="ui button"
                                onclick="showConfirm('{{tr "DeleteNotificationRule"}}'+'[{{$rule.Name}}]','{{tr "ConfirmToDeleteThisNotificationRule"}}',deleteRequest,'/api/alert-rule/'+{{$rule.ID}})">
                                <i class="trash alternate outline icon"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                {{end}}
            </tbody>
        </table>
    </div>
</div>
{{template "component/notification"}}
{{template "component/rule"}}
{{template "common/footer" .}}
<script>
    $('.checkbox').checkbox();

    // 为编辑报警规则按钮添加事件监听器
    $(document).on('click', '.edit-alert-rule-btn', function() {
        const btn = $(this);
        const ruleId = btn.data('id');

        if (!ruleId) {
            console.error('告警规则ID不存在');
            return;
        }

        // 显示加载状态
        btn.addClass('loading');

        // 通过AJAX获取告警规则详细数据
        $.ajax({
            url: '/api/alert-rule/' + ruleId,
            type: 'GET',
            timeout: 10000
        })
        .done(function(response) {
            if (response && response.code === 200 && response.result) {
                const rule = response.result;
                console.log('获取到告警规则数据:', rule);
                addOrEditAlertRule(rule);
            } else {
                console.error('获取告警规则数据失败:', response);
                alert('获取告警规则数据失败: ' + (response.message || '未知错误'));
            }
        })
        .fail(function(xhr, status, error) {
            console.error('获取告警规则数据网络错误:', error);
            alert('获取告警规则数据失败: ' + error);
        })
        .always(function() {
            btn.removeClass('loading');
        });
    });
</script>
{{end}}
