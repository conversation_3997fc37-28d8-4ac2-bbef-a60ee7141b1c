{{define "theme-default/menu"}}
<div id="app">
<div class="ui large top fixed menu nb-menu">
    <div class="ui container">
        <a class="item" href="/">
            <img src="/static/logo.svg?v20220602">
        </a>
        <template v-if="isMobile">
            <a href="/network" class="item"><i class="dot circle outline icon"></i>{{tr "NetworkStatus"}}</a> 
            <a href="http://status.nange.cn/" target="_blank" class="item"><i class="server icon"></i>{{tr "Services" }}</a>
        </template>
        <template v-else>
            <a href="/network" class="item"><i class="dot circle outline icon"></i>{{tr "NetworkStatus"}}</a>
            <a href="http://status.nange.cn/" target="_blank" class='item'><i class="server icon"></i>{{tr "Services" }}</a>
        </template>
        <!-- {{ if not .Conf.DisableSwitchTemplateInFrontend }}        
            <div class="item ui simple dropdown">
                <div class="text"><i class="bi bi-incognito icon" style="margin-right:3px;"></i>{{tr "Template" }}<i class="dropdown icon" style="margin-right:0px;"></i></div>
                <div class="menu"> 
                    <a v-for="(item, index) in adaptedTemplates" :key="index" @click="toggleTemplate(item.key)" class="item">
                        <i :class="item.icon + ' icon'"></i>@#item.name#@
                        <i class="check icon" v-if="preferredTemplate === item.key"></i>
                    </a>
                </div>
            </div>
        {{ end }} -->
        {{if .Admin}}
        <div class="right menu">
    <div class="item">
        <div class="ui simple dropdown">
            <div class="text">
                <img class="ui avatar image" src="{{.Admin.AvatarURL}}"> {{.Admin.Name}}
            </div>
            <i class="dropdown icon"></i>
            <div class="menu">
                <a href="/server" class="item"><i class="th icon"></i>{{tr "AdminPanel"}}</a> 
                <button class="item" onclick="showConfirm('{{tr "ConfirmLogout"}}','{{tr "AfterLoggingOutYouHaveToLoginAgain"}}',logout,{{.Admin.ID}})">
                    <i class="logout icon"></i>{{tr "Logout"}}
            </div>
        </div>
    </div>
    </div>
        {{else}}
        <div class="right menu">
            <div class="item">
                <a href="/login" class="ui large positive server-primary-btn button"><i class="sign-in icon"></i>{{tr "Login"}}</a>
            </div>
        </div>
        {{end}}
    </div>
</div>
{{template "component/confirm" .}}
{{end}}
