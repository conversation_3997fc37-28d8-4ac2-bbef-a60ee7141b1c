{{define "theme-default/home"}}
{{template "theme-default/header" .}}

<script>
// 性能优化 - 覆盖默认事件监听器为被动模式
(function() {
    // 保存原始的 addEventListener
    const originalAddEventListener = EventTarget.prototype.addEventListener;
    
    // 需要设置为被动的事件类型
    const passiveEvents = ['touchstart', 'touchmove', 'wheel', 'mousewheel'];
    
    // 重写 addEventListener
    EventTarget.prototype.addEventListener = function(type, listener, options) {
        if (passiveEvents.includes(type) && typeof options !== 'object') {
            options = { passive: true };
        } else if (passiveEvents.includes(type) && typeof options === 'object' && options.passive === undefined) {
            options.passive = true;
        }
        
        return originalAddEventListener.call(this, type, listener, options);
    };
})();
// jQuery 事件优化 - 延迟到jQuery加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 等待jQuery加载完成
    function waitForJQuery() {
        if (typeof $ !== 'undefined' && $.fn && $.fn.on) {
            const originalOn = $.fn.on;
            $.fn.on = function(events, selector, data, handler) {
                if (typeof events === 'string' &&
                    (events.includes('touchstart') || events.includes('touchmove') ||
                     events.includes('wheel') || events.includes('mousewheel'))) {

                    const passiveHandler = function(e) {
                        if (typeof handler === 'function') {
                            return handler.call(this, e);
                        } else if (typeof data === 'function') {
                            return data.call(this, e);
                        }
                    };

                    if (typeof data === 'function') {
                        handler = data;
                        data = undefined;
                    }

                    this.each(function() {
                        if (this.addEventListener) {
                            this.addEventListener(events, passiveHandler, { passive: true });
                        }
                    });

                    return this;
                }

                return originalOn.apply(this, arguments);
            };
        } else {
            // jQuery还没加载完成，100ms后重试
            setTimeout(waitForJQuery, 100);
        }
    }

    waitForJQuery();
});
</script>

{{if ts .CustomCode}} {{.CustomCode|safe}} {{end}}
{{template "theme-default/menu" .}}

<div class="nb-container">
    <div class="ui container" style="padding-bottom: 30px;">
        <div class="tabs-wrapper">
            <div v-if="servers && servers.length > 0" 
                 class="custom-tabs-container" 
                 ref="tabsContainer">
                <div class="tabs-content-area">
                    <div class="custom-tab"
                        :class="{ active: activeTag === '' }"
                        @click="handleTabClick('', $event)"
                        ref="allTab"> {{tr "All"}}
                    </div>
                    <div v-for="(tag, index) in uniqueTags.slice().reverse()"
                        :key="tag"
                        class="custom-tab"
                        :class="{ active: activeTag === tag }"
                        @click="handleTabClick(tag, $event)"
                        :ref="'tagTab' + index"> @#tag#@
                    </div>
                </div>
                <div class="tab-slider" ref="tabSlider"></div>
            </div>
        </div>

        <template v-if="filteredServers && filteredServers.length > 0">
            <div class="ui four stackable status cards">
                <div v-for="server in filteredServers" :key="server.ID" :id="server.ID" class="ui card">
                    <div class="content" v-if="server.Host" style="padding-bottom: 5px">
                        <div class="header">
                            <i :class="'flag-icon flag-icon-'+server.Host.CountryCode"></i>
                            <i v-if='isWindowsPlatform(server.Host.Platform)' class="windows icon"></i>
                            <i v-else-if='getFontLogoClass(server.Host.Platform) == "" && server.State.Uptime > 0' class="fl-tux"></i>
                            <i v-else :class="'fl-' + getFontLogoClass(server.Host.Platform)"></i>
                            @#server.Name + (server.live?'':' [{{tr "Offline"}}]')#@
                            <i class="server-primary-font info circle icon" style="height: 28px" ></i>
                            <div class="ui content popup">
                                {{tr "Platform"}}：@#specialOS(server.Host.Platform)#@ <span
                                    v-if='!isWindowsPlatform(server.Host.Platform)'>@#formatPlatformVersion(server.Host.PlatformVersion)#@
                                </span> @#specialVir(server.Host.Virtualization)#@
                                [@#server.Host.Arch#@]<br />
                                {{tr "MemUsed"}}：@#formatByteSize(server.State.MemUsed)#@ /
                                @#formatByteSize(server.Host.MemTotal)#@<br />
                                <template v-if="server.Host.SwapTotal">
                                {{tr "SwapUsed"}}：@#formatByteSize(server.State.SwapUsed)#@ /
                                        @#formatByteSize(server.Host.SwapTotal)#@<br />
                                </template>
                                {{tr "DiskUsed"}}：@#formatByteSize(server.State.DiskUsed)#@ /
                                @#formatByteSize(server.Host.DiskTotal)#@<br />
                                <template v-if="getTrafficTooltip(server.ID)">
                                {{tr "TrafficTotal"}}：@#getTrafficTooltip(server.ID)#@<br />
                                </template>
                                <template v-if="server.Host.GPU && server.Host.GPU.length > 0 && server.Host.GPU.some(gpu => gpu !== 'unknown')">
                                    {{tr "GPU"}}：@#server.Host.GPU.filter(gpu => gpu !== 'unknown').join(', ')#@<br />
                                </template>
                                <template v-if="hasTemperature(server.State.Temperatures, sensorList)">
                                    {{tr "Temperature"}}：@#getTemperature(server.State.Temperatures, sensorList)#@°C<br />
                                </template>

                                {{tr "Load"}}：@#toFixed2(server.State.Load1)#@ | @#toFixed2(server.State.Load5)#@ | @#toFixed2(server.State.Load15)#@<br />
                                {{tr "ConnCount"}}：TCP @#server.State.TcpConnCount#@ {{tr "Count"}} | UDP @#server.State.UdpConnCount#@ {{tr "Count"}}
                                <br />
                                {{tr "BootTime"}}：@#timeStamp(server.Host.BootTime * 1000)#@<br />
                                {{tr "LastActive"}}：@#timeStamp(server.LastActive)#@<br />
                                {{tr "Version"}}：@#server.Host.Version#@<br />
                            </div>
                            <div class="ui divider"
                                style="margin: .5rem 0 !important; border-bottom: 1px solid rgba(34,36,38,.15) !important;"></div>
                        </div>
                        
                        <div class="header header_info">
                            <i class="fa-solid fa-microchip"></i>
                            <div class="cpuroll" style="white-space: nowrap;overflow: hidden;">
                                <div class="cpucontent rollanimation">
                                    @#clearString(server.Host.CPU).join(" ")#@ &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                </div>
                                <div class="cpucontent rollanimation">
                                    @#clearString(server.Host.CPU).join(" ")#@ &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                </div>
                            </div>
                            <div style="display: flex; align-items: center;">
                                <i class="fa-solid fa-memory"></i>
                                @#getK2Gb(server.Host.MemTotal)#@
                                <span>&nbsp;&nbsp;</span>
                                <i class="fa-solid fa-hard-drive"></i>
                                @#getK2Gb(server.Host.DiskTotal)#@
                            </div>
                        </div>
                        
                        <div class="description">
                            <div class="ui grid">
                                <div class="three wide column">CPU</div>
                                <div class="thirteen wide column">
                                    <div :class="formatPercent(server.live,server.State.CPU, 100).class">
                                        <div class="bar"
                                            :style="formatPercent(server.live,server.State.CPU, 100).style">
                                            <small>@#formatPercent(server.live,server.State.CPU,100).percent#@%</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="three wide column">{{tr "MemUsed"}}</div>
                                <div class="thirteen wide column">
                                    <div
                                        :class="formatPercent(server.live,server.State.MemUsed, server.Host.MemTotal).class">
                                        <div class="bar"
                                            :style="formatPercent(server.live,server.State.MemUsed, server.Host.MemTotal).style">
                                            <small>@#parseInt(server.State&&server.Host.MemTotal?server.State.MemUsed/server.Host.MemTotal*100:0)#@%</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="three wide column">{{tr "DiskUsed"}}</div>
                                <div class="thirteen wide column">
                                    <div
                                        :class="formatPercent(server.live,server.State.DiskUsed, server.Host.DiskTotal).class">
                                        <div class="bar"
                                            :style="formatPercent(server.live,server.State.DiskUsed, server.Host.DiskTotal).style">
                                            <small>@#parseInt(server.State&&server.Host.DiskTotal?server.State.DiskUsed/server.Host.DiskTotal*100:0)#@%</small>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="three wide column">{{tr "TrafficTotal"}}</div>
                                <div class="thirteen wide column">
                                    <div :class="getTrafficProgressClass(server.ID)">
                                        <div class="bar" :style="getTrafficProgressStyle(server.ID)">
                                            <small>@#getTrafficDisplay(server.ID)#@</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="three wide column">{{tr "NetSpeed"}}</div>
                                <div class="thirteen wide column">
                                    <i class="fa-regular fa-down"></i>
                                    @#formatByteSize(server.State.NetInSpeed)#@/s |
                                    <i class="fa-regular fa-up"></i>
                                    @#formatByteSize(server.State.NetOutSpeed)#@/s
                                </div>
                                <div class="three wide column">{{tr "NetTransfer"}}</div>
                                <div class="thirteen wide column">
                                    <i class="fa-solid fa-down"></i>
                                    @#formatByteSize(server.State.NetInTransfer)#@ |
                                    <i class="fa-solid fa-up"></i>
                                    @#formatByteSize(server.State.NetOutTransfer)#@
                                </div>
                                 <div class="three wide column">{{tr "ProcessCount"}}</div>
                                <div class="thirteen wide column">
                                    <i class="fa-regular fa-bars-progress"></i> 
                                    @# server.State.ProcessCount #@ {{tr "Count"}}
                                </div>
                                <div class="three wide column">{{tr "Uptime"}}</div>
                                <div class="thirteen wide column">
                                    <i class="clock icon"></i>@#secondToDate(server.State.Uptime)#@
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="content" v-else> <p>@#server.Name#@</p>
                        <p><i class="fas fa-circle-notch fa-spin"></i> {{tr "Connecting"}}...</p>
                    </div>
                </div>
            </div>
        </template>
        <template v-else-if="servers && servers.length > 0 && filteredServers.length === 0 && activeTag !== ''">
             <div class="ui message info">
                <div class="header">
                    {{tr "NoServersForTagH"}} "@#activeTag#@"
                </div>
                <p>{{tr "NoServersForTagP"}}</p>
            </div>
        </template>
        <template v-else>
            <div class="ui message info">
                <div class="header">
                    {{tr "NoServers"}}
                </div>
                <p>{{tr "NoServersDesc"}}</p>
            </div>
        </template>
    </div>
</div>

<!-- 隐藏的数据容器 -->
<div id="traffic-data" style="display: none;">{{.TrafficData}}</div>

{{template "common/footer" .}}

<script>
// 确保流量提取函数可用
window.trafficFunctionsReady = true;
window.serverTrafficData = window.serverTrafficData || {};
window.lastTrafficUpdateTime = window.lastTrafficUpdateTime || 0;

// 提取流量数据的函数
window.extractTrafficData = function() {
    try {
        const trafficDataElement = document.getElementById('traffic-data');
        let trafficItems = [];
        if (trafficDataElement && trafficDataElement.textContent) {
            try {
                trafficItems = JSON.parse(trafficDataElement.textContent);
            } catch (e) {
                console.warn('DOM数据解析失败:', e);
            }
        }
        if (!trafficItems || trafficItems.length === 0) {
            trafficItems = window.serverTrafficRawData || [];
        }
        if (!trafficItems || trafficItems.length === 0) return;
        
        const newTrafficData = {};
        trafficItems.forEach(item => {
            if (item && item.server_id) {
                const serverId = String(item.server_id);
                const serverName = item.server_name || "Unknown Server";
                
                // 优先使用字节数据作为源数据
                let maxBytes, usedBytes, percent;
                
                if (item.is_bytes_source && typeof item.used_bytes === 'number' && typeof item.max_bytes === 'number') {
                    // 后端提供了字节数据源，直接使用
                    maxBytes = item.max_bytes;
                    usedBytes = item.used_bytes;
                    
                    // 从字节数据计算百分比
                    if (maxBytes > 0) {
                        percent = (usedBytes / maxBytes) * 100;
                        percent = Math.max(0, Math.min(100, percent)); // 限制在0-100范围
                    } else {
                        percent = 0;
                    }
                } else {
                    // 回退到解析格式化字符串
                    const maxTraffic = item.max_formatted || "0B";
                    const usedTraffic = item.used_formatted || "0B";
                    
                    maxBytes = (typeof window.parseTrafficToBytes === 'function') ? window.parseTrafficToBytes(maxTraffic) : 0;
                    usedBytes = (typeof window.parseTrafficToBytes === 'function') ? window.parseTrafficToBytes(usedTraffic) : 0;
                    
                    // 如果有后端计算的百分比，作为备选
                    if (typeof item.used_percent === 'number') {
                        percent = item.used_percent;
                    } else if (maxBytes > 0) {
                        percent = (usedBytes / maxBytes) * 100;
                        percent = Math.max(0, Math.min(100, percent));
                    } else {
                        percent = 0;
                    }
                }
                
                // 格式化显示字符串，使用readableBytes函数
                const standardMax = (typeof window.formatTrafficUnit === 'function') ? 
                    window.formatTrafficUnit(readableBytes(maxBytes)) : readableBytes(maxBytes);
                const standardUsed = (typeof window.formatTrafficUnit === 'function') ? 
                    window.formatTrafficUnit(readableBytes(usedBytes)) : readableBytes(usedBytes);
                
                newTrafficData[serverId] = {
                    max: standardMax,
                    used: standardUsed,
                    percent: Math.round(percent * 100) / 100,
                    maxBytes: maxBytes,
                    usedBytes: usedBytes,
                    serverName: serverName,
                    cycleName: item.cycle_name || "Unknown",
                    isBytesSource: item.is_bytes_source || false
                };
            }
        });
        
        window.serverTrafficData = newTrafficData;
        window.lastTrafficUpdateTime = Date.now();
        
        if (window.statusCards && typeof window.statusCards.updateTrafficData === 'function') {
            window.statusCards.updateTrafficData();
        }
    } catch (e) {
        console.error('提取流量数据时出错:', e);
    }
};

// 立即提取数据
setTimeout(window.extractTrafficData, 1000);

// 定期更新
setInterval(window.extractTrafficData, 30000);

// Vue实例配置
const statusCards = new Vue({
    el: '#app',
    delimiters: ['@#', '#@'],
    data: {
        page: 'index',
        defaultTemplate: '{{.Conf.Site.Theme}}',
        templates: {{.Themes}},
        servers: [],
        cache: [],
        chartDataList: [],
        activePopup: null,
        trafficData: {},
        _trafficVersion: 0,
        sensorList: [
            'TC0D',
            'TC0H',
            'TC0P',
            'k10temp',
            'k10temp_tctl',
            'coretemp_package_id_0',
            'cpu_thermal_zone',
            'cpu-thermal',
            'soc_thermal',
            'cpu_thermal',
            'ACPI\\ThermalZone\\TZ0__0',
            'ACPI\\ThermalZone\\CPUZ_0',
            'ACPI\\ThermalZone\\TZ00_0',
            'ACPI\\ThermalZone\\TZ001_0',
            'ACPI\\ThermalZone\\THM0_0'
        ],
        activeTag: '',
        uniqueTags: [],
        _lastKnownUpdateTime: 0,
        _updateInterval: null
    },
    mixins: [mixinsVue],
    created() {
        try {
            const serverData = JSON.parse('{{.Servers}}');
            if (serverData && serverData.servers && Array.isArray(serverData.servers)) {
                this.servers = serverData.servers;
            } else {
                console.warn('服务器数据格式不正确，使用空数组');
                this.servers = [];
            }
        } catch (e) {
            console.error('解析服务器数据失败:', e);
            this.servers = [];
        }
        
        this.generateUniqueTags();
        this.updateServerLiveStatus();
        
        // 订阅流量数据更新
        if (window.trafficManager) {
            window.trafficManager.subscribe((data, version) => {
                this.trafficData = data;
                this._trafficVersion = version;
                this.updateTrafficData();
            });
        }
        
        // 设置定期更新，降低频率减少跳动
        this._updateInterval = setInterval(() => {
            this.updateServerLiveStatus({ now: Date.now() });
        }, 3000); // 改为3秒更新一次，减少频繁跳动
    },
    mounted() {
        this.$nextTick(() => {
            this.initializePopups();
            this.updateSliderPosition();
            $('.ui.accordion').accordion();
        });
    },
    computed: {
        filteredServers() {
            if (!this.servers || !Array.isArray(this.servers)) {
                return [];
            }
            
            const displayableServers = this.servers.filter(server => server.Host);
            if (this.activeTag === '') {
                return displayableServers;
            }
            return displayableServers.filter(server => server.Tag === this.activeTag);
        }
    },
    methods: {
        checkIsMobile() {
            return window.innerWidth < 768;
        },
        getServerTrafficData(serverId) {
            if (!serverId) return null;
            
            // 首先尝试从 trafficManager 获取数据
            if (window.trafficManager) {
                const trafficData = window.trafficManager.getServerTrafficData(serverId);
                if (trafficData) {
                    return trafficData;
                }
            }
            
            // 如果 trafficManager 没有数据，尝试从全局数据获取
            if (window.serverTrafficData) {
                const serverIdStr = String(serverId).trim();
                if (window.serverTrafficData[serverIdStr]) {
                    return window.serverTrafficData[serverIdStr];
                }
                
                // 尝试数字匹配
                const numericId = parseInt(serverIdStr);
                if (!isNaN(numericId)) {
                    const keys = Object.keys(window.serverTrafficData);
                    for (const key of keys) {
                        if (parseInt(key) === numericId) {
                            return window.serverTrafficData[key];
                        }
                    }
                }
            }
            
            return null;
        },
        formatPlatformVersion(version) {
            if (!version) return '';
            if (/^[\d.]+$/.test(version)) {
                return version;
            }
            let cleanVersion = version.split('/')[0];
            return cleanVersion.split(' ').map(word => {
                if (word.length > 0) {
                    return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                }
                return word;
            }).join(' ');
        },
        toFixed2(f) {
            return f.toFixed(2);
        },
        isWindowsPlatform(str) {
            return str && typeof str === 'string' && str.includes('indows');
        },
        getFontLogoClass(str) {
            const validPlatforms = [
                "almalinux", "alpine", "aosc", "apple", "archlinux", "archlabs",
                "artix", "budgie", "centos", "coreos", "debian", "deepin",
                "devuan", "docker", "elementary", "fedora", "ferris", "flathub",
                "freebsd", "gentoo", "gnu-guix", "illumos", "kali-linux",
                "linuxmint", "mageia", "mandriva", "manjaro", "nixos",
                "openbsd", "opensuse", "pop-os", "raspberry-pi", "redhat",
                "rocky-linux", "sabayon", "slackware", "snappy", "solus",
                "tux", "ubuntu", "void", "zorin"
            ];
            
            if (validPlatforms.includes(str)) {
                return str;
            }
            if (str === 'darwin') {
                return 'apple';
            }
            if (['openwrt', 'linux'].includes(str)) {
                return 'tux';
            }
            if (str === 'amazon') {
                return 'redhat';
            }
            if (str === 'arch') {
                return 'archlinux';
            }
            if (str && typeof str === 'string' && str.toLowerCase().includes('opensuse')) {
                return 'opensuse';
            }
            return '';
        },
        formatPercent(live, used, total) {
            let u = parseFloat(used);
            let t = parseFloat(total);

            if (isNaN(u) || isNaN(t)) {
                live = false;
            }

            let percent;
            if (live && t > 0) {
                percent = parseInt((u / t) * 100);
                if (isNaN(percent) || percent < 0) percent = 0;
                if (percent > 100) percent = 100;
            } else if (live && t === 0 && u === 0) {
                percent = 0;
            } else {
                percent = -1;
            }

            if (!this.cache[percent]) {
                let displayPercentValue = percent;
                let classObject = { ui: true, progress: true };
                let styleObject = {
                    'transition-duration': '300ms', // 增加过渡时间，使变化更平滑
                    'transition-timing-function': 'ease-out',
                    'min-width': 'unset',
                    width: (percent >= 0 ? percent : 0) + '% !important',
                };

                if (percent < 0) {
                    styleObject['background-color'] = 'slategray';
                    classObject.offline = true;
                    displayPercentValue = 0;
                } else if (percent < 60) {
                    styleObject['background-color'] = 'rgb(76,175,80)';
                    classObject.fine = true;
                } else if (percent < 90) {
                    styleObject['background-color'] = '#ff9800';
                    classObject.warning = true;
                } else {
                    styleObject['background-color'] = '#f44336';
                    classObject.error = true;
                }
                this.cache[percent] = {
                    class: classObject,
                    style: styleObject,
                    percent: displayPercentValue,
                };
            }

            return this.cache[percent] || {
                class: { ui: true, progress: true, offline: true },
                style: { width: '0%', 'background-color': 'slategray' },
                percent: 0
            };
        },
        secondToDate(s) {
            var d = Math.floor(s / 3600 / 24);
            if (d > 0) {
                return d + " {{tr "Day"}}";
            }
            var h = Math.floor(s / 3600 % 24);
            var m = Math.floor(s / 60 % 60);
            var s = Math.floor(s % 60);
            return h + ":" + ("0" + m).slice(-2) + ":" + ("0" + s).slice(-2);
        },
        timeStamp(t) {
            return new Date(t).format('yyyy年MM月dd日 HH:mm:ss');
        },
        formatByteSize(bs) {
            const x = this.readableBytes(bs);
            return x != "NaN undefined" ? x : '0B';
        },
        readableBytes(bytes) {
            if (!bytes) {
                return '0B';
            }
            const i = Math.floor(Math.log(bytes) / Math.log(1024));
            const sizes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
            const value = parseFloat((bytes / Math.pow(1024, i)).toFixed(2));
            return `${value}${sizes[i]}`;
        },
        getCoreAndGHz(arr) {
            if ((arr || []).length === 0) {
                return '';
            }
            let totalCores = 0;
            arr.forEach(str => {
                let coreMatch = str.match(/(\d+(\.\d+)?) Physical/g);
                let coreCount = 0;
                if (coreMatch) {
                    coreCount = parseFloat(coreMatch[0]);
                } else {
                    let coreMatch = str.match(/(\d+(\.\d+)?) Virtual/g);
                    coreCount = coreMatch ? parseFloat(coreMatch[0]) : 0;
                }
                totalCores += coreCount;
            });
            return `${totalCores} Cores`;
        },
        getK2Gb(bs) {
            bs = bs / 1024 / 1024 / 1024;
            if (bs >= 1) {
                return Math.ceil(bs.toFixed(2)) + 'GB';
            } else {
                bs = bs * 1024;
                return Math.ceil(bs.toFixed(2)) + 'MB';
            }
        },
        listTipsMouseenter(obj, strs, tipsNum = 1) {
            this.layerIndex = layer.tips(strs, '#' + obj, { tips: [tipsNum, 'rgb(0 0 0 / 85%)'], time: 0 });
            $('#' + obj).attr('layerIndex', this.layerIndex);
        },
        listTipsMouseleave(obj) {
            layer.close(this.layerIndex);
        },
        initializePopups() {
            const triggerSelector = '.server-primary-font.info.circle.icon';
            $(triggerSelector).popup('destroy');
            
            this.$nextTick(() => {
                $(triggerSelector).popup({
                    popup: '.ui.content.popup',
                    on: 'hover',
                    hoverable: true,
                    exclusive: true,
                    delay: { show: 100, hide: 300 },
                    silent: true,
                    performance: true,
                    onShow: function() {
                        const $currentPopup = $(this);
                        $('.ui.popup.visible').not($currentPopup).each(function() {
                            $(this).removeClass('visible transition').addClass('hidden').css('display', 'none');
                        });
                        return true;
                    }
                });
            });
        },
        generateUniqueTags() {
            const tags = new Set();
            if (this.servers) {
                this.servers.forEach(server => {
                    if (server.Tag && server.Tag.trim() !== '') {
                        tags.add(server.Tag.trim());
                    }
                });
            }
            this.uniqueTags = Array.from(tags).sort();
        },
        handleTabClick(tag, event) {
            this.activeTag = tag;
            this.$nextTick(() => {
                const tabEl = event.currentTarget;
                const tabsContainer = this.$refs.tabsContainer;
                if (tabEl && tabsContainer && tabsContainer.contains(tabEl)) {
                    if (tabsContainer.scrollWidth > tabsContainer.clientWidth) {
                        tabEl.scrollIntoView({ behavior: 'smooth', inline: 'center', block: 'nearest' });
                    }
                }
            });
        },
        updateSliderPosition() {
            const slider = this.$refs.tabSlider;
            if (!slider) return;

            let activeTabElement = null;
            if (this.activeTag === '') {
                activeTabElement = this.$refs.allTab;
            } else {
                const reversedTags = this.uniqueTags.slice().reverse();
                const activeIndexInReversed = reversedTags.indexOf(this.activeTag);
                if (activeIndexInReversed !== -1 && this.$refs['tagTab' + activeIndexInReversed]) {
                    activeTabElement = this.$refs['tagTab' + activeIndexInReversed];
                    if (Array.isArray(activeTabElement)) {
                        activeTabElement = activeTabElement[0];
                    }
                }
            }

            if (!activeTabElement && this.$refs.allTab) {
                activeTabElement = this.$refs.allTab;
            }

            if (activeTabElement && this.$refs.tabsContainer) {
                const containerRect = this.$refs.tabsContainer.getBoundingClientRect();
                const tabRect = activeTabElement.getBoundingClientRect();
                const newLeft = tabRect.left - containerRect.left + this.$refs.tabsContainer.scrollLeft;
                const newWidth = tabRect.width;
                slider.style.left = `${newLeft}px`;
                slider.style.width = `${newWidth}px`;
            } else {
                slider.style.width = '0px';
            }
        },
        updateServerLiveStatus(wsData) {
            if (!wsData || !this.servers) return;
            
            const nowMillisFromWS = wsData.now || Date.now();
            let hasUpdates = false;
            
            // 创建服务器ID到数据的映射，提高查找效率
            const serverMap = new Map();
            if (wsData.servers) {
                wsData.servers.forEach(server => {
                    serverMap.set(server.ID, server);
                });
            }
            
            this.servers.forEach(serverItem => {
                if (!serverItem.Host) {
                    if (serverItem.live !== false) {
                        serverItem.live = false;
                        hasUpdates = true;
                    }
                    return;
                }
                
                const wsServer = serverMap.get(serverItem.ID);
                if (wsServer) {
                    // 更新服务器状态
                    if (wsServer.State) {
                        const oldState = serverItem.State;
                        serverItem.State = {
                            ...oldState,
                            ...wsServer.State
                        };
                        hasUpdates = true;
                    }
                    
                    // 更新最后活动时间
                    if (wsServer.LastActive) {
                        serverItem.LastActive = wsServer.LastActive;
                    }
                }
                
                // 计算在线状态 - 增加稳定性，减少频繁跳动
                const lastActiveTimestampMs = new Date(serverItem.LastActive).getTime();
                const diffMs = nowMillisFromWS - lastActiveTimestampMs;

                // 增加容错时间，减少误判：60秒超时 + 10秒容错
                const newLiveStatus = !isNaN(lastActiveTimestampMs) && diffMs <= 60000 && diffMs >= -10000;

                // 添加状态稳定性检查，避免频繁切换
                if (serverItem.live !== newLiveStatus) {
                    // 如果状态要从在线变为离线，需要连续2次确认
                    if (serverItem.live === true && newLiveStatus === false) {
                        if (!serverItem._offlineConfirmCount) {
                            serverItem._offlineConfirmCount = 1;
                            return; // 第一次检测到离线，不立即更新
                        } else {
                            serverItem._offlineConfirmCount = 0; // 重置计数器
                        }
                    } else if (serverItem.live === false && newLiveStatus === true) {
                        // 从离线恢复到在线，立即更新
                        serverItem._offlineConfirmCount = 0;
                    }

                    serverItem.live = newLiveStatus;
                    hasUpdates = true;
                }
            });
            
            // 只有在有实际更新时才强制更新视图，并添加防抖
            if (hasUpdates) {
                // 使用防抖机制，避免频繁更新
                if (this._updateDebounceTimer) {
                    clearTimeout(this._updateDebounceTimer);
                }
                this._updateDebounceTimer = setTimeout(() => {
                    this.$forceUpdate();
                    this._updateDebounceTimer = null;
                }, 500); // 500ms 防抖
            }
        },
        getTrafficProgressClass(serverId) {
            const trafficData = this.getServerTrafficData(serverId);
            if (!trafficData) {
                return 'ui progress offline';
            }
            
            const percent = trafficData.percent || 0;
            let classObject = { ui: true, progress: true };
            
            if (percent < 60) {
                classObject.fine = true;
            } else if (percent < 90) {
                classObject.warning = true;
            } else {
                classObject.error = true;
            }
            
            return Object.keys(classObject).join(' ');
        },
        getTrafficProgressStyle(serverId) {
            const trafficData = this.getServerTrafficData(serverId);
            if (!trafficData) {
                return {
                    'transition-duration': '300ms',
                    'width': '0%'
                };
            }
            
            const percent = trafficData.percent || 0;
            let bgColor = 'rgb(76,175,80)';
            
            if (percent >= 90) {
                bgColor = '#f44336';
            } else if (percent >= 60) {
                bgColor = '#ff9800';
            }
            
            return {
                'transition-duration': '300ms',
                'width': percent + '%',
                'background-color': bgColor
            };
        },
        getTrafficDisplay(serverId) {
            const trafficData = this.getServerTrafficData(serverId);
            if (!trafficData) {
                return '0%';
            }
            
            const percent = trafficData.percent || 0;
            return percent.toFixed(2) + '%';
        },
        getTrafficTooltip(serverId) {
            const trafficData = this.getServerTrafficData(serverId);
            if (!trafficData) {
                return '无数据';
            }
            
            const used = trafficData.used || '0B';
            const max = trafficData.max || '0B';
            return `${used} / ${max}`;
        },
        updateTrafficData() {
            if (!window.serverTrafficData) {
                return;
            }

            let hasUpdates = false;
            const trafficData = window.serverTrafficData;

            this.servers.forEach(server => {
                const trafficInfo = trafficData[server.ID];
                if (trafficInfo) {
                    const oldTraffic = server.traffic;
                    const newTraffic = {
                        used: trafficInfo.used,
                        total: trafficInfo.max,
                        percent: trafficInfo.percent,
                        lastUpdate: trafficInfo.lastUpdate
                    };

                    // 检查是否有实际更新，增加更详细的比较
                    if (!oldTraffic ||
                        oldTraffic.used !== newTraffic.used ||
                        oldTraffic.total !== newTraffic.total ||
                        Math.abs(oldTraffic.percent - newTraffic.percent) > 0.01) { // 百分比变化超过0.01%才更新
                        server.traffic = newTraffic;
                        hasUpdates = true;
                    }
                }
            });

            if (hasUpdates) {
                this.$forceUpdate();
            }
        },
        hasTemperature(temperatureList, sensorList) {
            if (!temperatureList || !Array.isArray(temperatureList) || temperatureList.length === 0) {
                return false;
            }
            return temperatureList.some(item => item.Temperature !== 0);
        },
        getTemperature(temperatureList, sensorList) {
            if (!temperatureList || !Array.isArray(temperatureList) || temperatureList.length === 0) {
                return '';
            }
            
            const lowerCaseSensorList = sensorList.map(sensor => sensor.toLowerCase());
            const filtered = temperatureList.filter(item => 
                item.Temperature !== 0 && 
                item.Name && 
                lowerCaseSensorList.includes(item.Name.toLowerCase())
            );
            
            if (filtered.length > 0) {
                const maxTemp = filtered.reduce((max, current) => 
                    current.Temperature > max ? current.Temperature : max, 
                    filtered[0].Temperature
                );
                return maxTemp.toFixed(1);
            }

            const nonZeroTemps = temperatureList.filter(item => item.Temperature !== 0);
            if (nonZeroTemps.length > 0) {
                const maxTemp = nonZeroTemps.reduce((max, current) => 
                    current.Temperature > max ? current.Temperature : max, 
                    nonZeroTemps[0].Temperature
                );
                return maxTemp.toFixed(1);
            }

            return '';
        },
        initTrafficDataWatcher() {
            this.trafficWatcherInterval = setInterval(() => {
                if (window.serverTrafficData && 
                    Object.keys(window.serverTrafficData).length > 0 && 
                    window.lastTrafficUpdateTime !== this._lastKnownUpdateTime) {
                    
                    this.trafficData = JSON.parse(JSON.stringify(window.serverTrafficData));
                    this._lastKnownUpdateTime = window.lastTrafficUpdateTime;
                    this.$forceUpdate();
                }
            }, 1000);
        }
    },
    watch: {
        activeTag() {
            this.$nextTick(() => {
                this.updateSliderPosition();
            });
        },
        filteredServers: {
            handler() {
                this.$nextTick(() => {
                    this.initializePopups();
                });
            },
            deep: true
        },
        uniqueTags() {
            this.$nextTick(() => {
                this.updateSliderPosition();
            });
        },
        servers() {
            this.generateUniqueTags();
            this.$nextTick(() => {
                this.updateSliderPosition();
            });
        }
    },
    beforeDestroy() {
        if (this._updateInterval) {
            clearInterval(this._updateInterval);
        }
        if (this._updateDebounceTimer) {
            clearTimeout(this._updateDebounceTimer);
        }
        if (window.trafficManager) {
            window.trafficManager.unsubscribe(this.updateTrafficData);
        }
    }
});

// 使Vue实例全局可用
window.statusCards = statusCards;

// 初始化WebSocket连接
function initWebSocket() {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws`;

    const ws = new WebSocket(wsUrl);

    ws.onopen = function() {
        try {
            ws.send(JSON.stringify({ type: 'ping' }));
        } catch (e) {
            console.warn('WebSocket ping failed:', e);
        }
    };

    ws.onmessage = function(event) {
        try {
            const data = JSON.parse(event.data);

            // 处理服务器状态更新
            if (data.servers && Array.isArray(data.servers)) {
                if (window.statusCards) {
                    window.statusCards.updateServerLiveStatus(data);
                }
            }

            // 处理流量数据更新 - 流量数据可能单独发送或与服务器状态一起发送
            if (data.trafficData && Array.isArray(data.trafficData)) {
                window.serverTrafficRawData = data.trafficData;
                if (window.trafficManager) {
                    window.trafficManager.processTrafficData(data.trafficData);
                } else {
                    window.extractTrafficData();
                }
            }
        } catch (e) {
            console.warn('WebSocket message parsing failed:', e);
        }
    };

    ws.onerror = function(error) {
        console.warn('WebSocket error:', error);
    };

    ws.onclose = function(event) {
        // WebSocket closed, reconnecting in 5 seconds
        setTimeout(initWebSocket, 5000);
    };

    return ws;
}

$(document).ready(function() {
    // 初始化WebSocket连接（仅在首页）
    if (window.location.pathname === '/' || window.location.pathname === '/home') {
        window.ws = initWebSocket();

        // 添加页面可见性变化监听
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                if (!window.ws || window.ws.readyState !== WebSocket.OPEN) {
                    window.ws = initWebSocket();
                }
            }
        });
    }

    $('.ui.accordion').accordion({
        exclusive: false,
        silent: true,
        performance: true,
        selector: {
            trigger: '.title'
        },
        duration: 200,
        easing: 'easeOutQuad'
    });

    $(document).off('touchstart.accordion touchmove.accordion');
    $(document).on('touchstart.accordion', '.ui.accordion .title', function(e) {
        // 被动处理，不阻止默认行为
    });
});
</script>
{{end}}
