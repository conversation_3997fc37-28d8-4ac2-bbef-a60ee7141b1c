{{define "theme-default/network"}}
{{template "theme-default/header" .}}
{{if ts .CustomCode}}
{{.CustomCode|safe}}
{{end}}
{{template "theme-default/menu" .}}
<div class="nb-container">
    <div class="ui container">
        <div class="service-status">
            
            <div class="server-buttons-container">
                <button class="ui network-primary-btn icon button"
                        v-for="server in servers"
                        :key="server.ID"
                        style="margin-top: 3px;font-size: 100%;"
                        @click="redirectNetwork(server.ID)">
                         <i :class="'flag-icon flag-icon-'+server.Host.CountryCode"></i>&nbsp;<i
                    v-if='server.Host.Platform == "darwin"' class="apple icon"></i><i
                    v-else-if='isWindowsPlatform(server.Host.Platform)' class="windows icon"></i><i
                    v-else :class="'fl-' + getFontLogoClass(server.Host.Platform)"></i>
                    @#server.Name#@
                </button>
            </div>
        </div>
    </div>
    <div class="ui container">
        <div ref="chartDom" style="margin-top: 10px;height: auto;overflow: hidden"></div>
    </div>
</div>

{{template "common/footer" .}}

<script>
    // 性能优化措施
    if ('requestIdleCallback' in window) {
        // 使用空闲时间预处理数据
        requestIdleCallback(() => {
            // 预加载字体
            if (document.fonts && document.fonts.load) {
                document.fonts.load('1em "Font Awesome 5 Free"').catch(() => {});
                document.fonts.load('1em "Font Awesome 5 Brands"').catch(() => {});
                document.fonts.load('1em "Font Awesome 6 Pro"').catch(() => {});
                document.fonts.load('1em "Font Awesome 6 Brands"').catch(() => {});
                document.fonts.load('1em "Font Awesome 6 Free"').catch(() => {});
            }
        });
    }
    
    // 减少重排提示
    const originalSetTimeout = window.setTimeout;
    let timeoutCount = 0;
    window.setTimeout = function(fn, delay) {
        timeoutCount++;
        if (timeoutCount > 10) {
            // 批量处理过多的setTimeout
            return originalSetTimeout(() => {
                requestAnimationFrame(fn);
            }, delay || 0);
        }
        return originalSetTimeout(fn, delay);
    };

    const monitorInfo =  JSON.parse('{{.MonitorInfos}}');
    const initData = JSON.parse('{{.Servers}}').servers;
    let MaxTCPPingValue = {{.MaxTCPPingValue}};

    // 性能优化：移除同步AJAX，改为异步加载
    let monitorConfigs = {};

    // 异步加载监控配置，不阻塞页面渲染
    function loadMonitorConfigs() {
        return $.ajax({
            url: '/api/v1/monitor/configs',
            method: 'GET',
            timeout: 5000,
            cache: true
        }).done(function(data) {
            if (data && Array.isArray(data)) {
                data.forEach(monitor => {
                    monitorConfigs[monitor.ID] = monitor;
                });
            }
        }).fail(function() {
            // 无法获取监控配置，使用默认名称
        });
    }
    new Vue({
        el: '#app',
        delimiters: ['@#', '#@'],
        data: {
            page: 'network',
            defaultTemplate: {{.Conf.Site.Theme}},
            templates: {{.Themes}},
            servers: initData,
            option: {},
            resizeTimer: null,
            monitorConfigsLoaded: false
        },
        mixins: [mixinsVue],
        created() {
            this.option = {
                tooltip: {
                    trigger: 'axis',
                    position: function (pt) {
                        return [pt[0], '10%'];
                    },
                    formatter: function(params){
                        let result = params[0].axisValueLabel + "<br />";
                        params.forEach(function(item){
                            result += item.marker + item.seriesName + ": " + item.value[1].toFixed(2) + " ms<br />";
                         })
                        return result;
                    },
                    confine: true,
                    transitionDuration: 0,
                    z: 10,
                    zlevel: 1
                },
                title: {
                    left: 'center',
                    text: "",
                    textStyle: {},
                    z: 5,
                    zlevel: 0
                },
                legend: {
                    top: '5%',
                    data: [],
                    textStyle: {
                        fontSize: this.isMobile ? 11 : 14 // 移动端使用更小字体
                    },
                    itemWidth: this.isMobile ? 15 : 25, // 移动端缩小图例图标
                    itemHeight: this.isMobile ? 10 : 14,
                    itemGap: this.isMobile ? 8 : 10, // 移动端减少间距
                    z: 4,
                    zlevel: 0
                },
                grid: {
                    left: this.isMobile ? '12%' : '7%', // 进一步增加左边距
                    right: this.isMobile ? '12%' : '7%', // 进一步增加右边距
                    bottom: this.isMobile ? '13%' : '12%', // PC端略微增加，移动端减少
                    z: 1,
                    zlevel: 0
                },
                backgroundColor: 'rgba(255, 255, 255, 0.6)',
                dataZoom: [
                    {
                        type: 'slider',
                        start: 0,
                        end: 100,
                        bottom: this.isMobile ? '2%' : '1%', // 调整滑动条位置
                        height: this.isMobile ? 20 : 28, // PC端略微再增加高度
                        z: 6,
                        zlevel: 0,
                        labelFormatter: function (value) {
                            // 滑动条两端时间字符串换行显示
                            return echarts.format.formatTime('MM-dd\nhh:mm', value);
                        }
                    }
                ],
                xAxis: {
                    type: 'time',
                    boundaryGap: false,
                    axisLabel: {
                        formatter: function (value) {
                            // PC端不换行，移动端换行
                            if (window.innerWidth <= 768) {
                                return echarts.format.formatTime('MM-dd\nhh:mm', value);
                            }
                            return echarts.format.formatTime('MM-dd hh:mm', value);
                        },
                        interval: 'auto', // 自动计算间隔
                        rotate: 0, // 不旋转
                        fontSize: window.innerWidth <= 768 ? 10 : 12, // 移动端使用更小字体
                        margin: window.innerWidth <= 768 ? 8 : 8, // 增加边距
                        showMaxLabel: true,
                        showMinLabel: true,
                        lineHeight: window.innerWidth <= 768 ? 16 : 14 // 移动端需要更大行高
                    },
                    z: 1,
                    zlevel: 0
                },
                yAxis: {
                    type: 'value',
                    boundaryGap: false,
                    z: 1,
                    zlevel: 0
                },
                series: [],
                animation: false,
                animationDuration: 0
            }
        },
        mounted() {
            // 使用passive事件监听器优化滚轮性能
            window.addEventListener('resize', this.resizeHandle, { passive: true });
            
            // 温和的移动端滚动优化
            if (this.isMobile) {
                // 简单的滚动优化，不强制改变页面结构
                document.body.style.webkitOverflowScrolling = 'touch';
                
                // 防止意外的页面跳转
                let lastScrollTop = 0;
                window.addEventListener('scroll', function() {
                    const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;
                    lastScrollTop = currentScrollTop;
                }, { passive: true });
            }
            
            // 优化初始化序列，先加载监控配置
            this.$nextTick(() => {
                // 先渲染图表
                this.renderChart();

                // 先加载监控配置，然后再处理数据
                loadMonitorConfigs().always(() => {
                    this.monitorConfigsLoaded = true;

                    // 然后加载数据，避免阻塞UI
                    setTimeout(() => {
                        this.parseMonitorInfo(monitorInfo);
                    }, 50);
                });
            });
        },
        destroyed () {
            window.removeEventListener('resize', this.resizeHandle, { passive: true });
        },
        methods: {
            // 极致性能优化：简化数据处理逻辑
            processMonitorHistories(histories) {
                console.log('=== 开始处理监控历史数据 ===');
                console.log('原始数据数量:', histories ? histories.length : 0);
                console.log('原始数据:', histories);

                if (!histories || histories.length === 0) {
                    console.log('没有监控历史数据');
                    return [];
                }

                // 性能优化：使用普通对象而非Map，减少开销
                const groupedData = {};
                const serverNames = {};

                // 预构建服务器名称映射
                if (this.servers) {
                    for (let i = 0; i < this.servers.length; i++) {
                        const server = this.servers[i];
                        serverNames[server.ID] = server.Name;
                    }
                }

                // 极致优化：单次遍历完成所有处理
                for (let i = 0; i < histories.length; i++) {
                    const history = histories[i];
                    const monitorId = history.MonitorID || history.monitor_id || 0;
                    const serverId = history.ServerID || history.server_id || 0;
                    const uniqueKey = `${monitorId}_${serverId}`;

                    console.log(`处理记录 ${i}: MonitorID=${monitorId}, ServerID=${serverId}, UniqueKey=${uniqueKey}`);

                    if (!groupedData[uniqueKey]) {
                        console.log(`创建新的监控点分组: ${uniqueKey}`);
                        groupedData[uniqueKey] = {
                            created_at: [],
                            avg_delay: [],
                            monitor_id: monitorId,
                            server_id: serverId,
                            unique_key: uniqueKey
                        };
                    }

                    const data = groupedData[uniqueKey];

                    // 性能优化：减少日期转换，直接使用字符串解析
                    let timestamp;
                    const timeStr = history.CreatedAt || history.created_at;
                    if (timeStr) {
                        timestamp = Date.parse(timeStr);
                    } else {
                        timestamp = Date.now();
                    }

                    const avgDelay = history.AvgDelay || history.avg_delay || 0;

                    data.created_at.push(timestamp);
                    data.avg_delay.push(avgDelay);
                }

                // 性能优化：转换为图表需要的格式，减少数组操作
                const result = [];

                for (const uniqueKey in groupedData) {
                    const data = groupedData[uniqueKey];
                    const monitorId = data.monitor_id;
                    const serverId = data.server_id;

                    // 性能优化：只在数据量大时才排序，小数据量保持原序
                    if (data.created_at.length > 100) {
                        // 将时间戳和延迟数据配对并排序
                        const paired = [];
                        for (let i = 0; i < data.created_at.length; i++) {
                            paired.push({
                                time: data.created_at[i],
                                delay: data.avg_delay[i]
                            });
                        }
                        paired.sort((a, b) => a.time - b.time);

                        result.push({
                            monitor_id: monitorId,
                            server_id: serverId,
                            unique_key: uniqueKey,
                            monitor_name: this.getMonitorName(monitorId),
                            server_name: serverNames[serverId] || `Server ${serverId}`,
                            created_at: paired.map(p => p.time),
                            avg_delay: paired.map(p => p.delay)
                        });
                    } else {
                        // 小数据量直接使用，避免排序开销
                        result.push({
                            monitor_id: monitorId,
                            server_id: serverId,
                            unique_key: uniqueKey,
                            monitor_name: this.getMonitorName(monitorId),
                            server_name: serverNames[serverId] || `Server ${serverId}`,
                            created_at: data.created_at,
                            avg_delay: data.avg_delay
                        });
                    }
                }


                console.log('=== 数据处理完成 ===');
                console.log('最终监控点数量:', result.length);
                console.log('监控点详情:', result.map(r => ({
                    monitor_id: r.monitor_id,
                    server_id: r.server_id,
                    monitor_name: r.monitor_name,
                    unique_key: r.unique_key,
                    data_count: r.created_at.length
                })));
                console.log('===================');

                return result;
            },
            getFontLogoClass(str) {
                if (["almalinux",
                        "alpine",
                        "aosc",
                        "apple",
                        "archlinux",
                        "archlabs",
                        "artix",
                        "budgie",
                        "centos",
                        "coreos",
                        "debian",
                        "deepin",
                        "devuan",
                        "docker",
                        "elementary",
                        "fedora",
                        "ferris",
                        "flathub",
                        "freebsd",
                        "gentoo",
                        "gnu-guix",
                        "illumos",
                        "kali-linux",
                        "linuxmint",
                        "mageia",
                        "mandriva",
                        "manjaro",
                        "nixos",
                        "openbsd",
                        "opensuse",
                        "pop-os",
                        "raspberry-pi",
                        "redhat",
                        "rocky-linux",
                        "sabayon",
                        "slackware",
                        "snappy",
                        "solus",
                        "tux",
                        "ubuntu",
                        "void",
                        "zorin"].indexOf(str)
                    > -1) {
                    return str;
                }
                if (['openwrt', 'linux', "immortalwrt"].indexOf(str) > -1) {
                    return 'tux';
                }
                if (str == 'amazon') {
                    return 'redhat';
                }
                if (str == 'arch') {
                    return 'archlinux';
                }
                return '';
            },
            redirectNetwork(id) {
                    // 性能优化：显示加载状态，避免用户等待
                    this.option.title.text = "Loading...";
                    if (this.myChart) {
                        this.myChart.setOption(this.option, true);
                    }

                    this.getMonitorHistory(id)
                    .then((monitorInfo) => {
                          this.parseMonitorInfo(monitorInfo);
                    })
                    .catch((error) => {
                        console.error('加载监控数据失败:', error);
                        this.option.title.text = "Failed to load data";
                        if (this.myChart) {
                            this.myChart.setOption(this.option, true);
                        }
                    })
                },
            getMonitorHistory(id) {
                  // 性能优化：添加缓存和超时控制
                  return $.ajax({
                    url: "/api/v1/monitor/"+id,
                    method: "GET",
                    timeout: 60000, // 增加超时时间到60秒
                    cache: true     // 启用缓存
                  });
            },
            getMonitorName(monitorId) {
                // 调试：输出监控配置信息
                console.log('获取监控名称 - ID:', monitorId);
                console.log('监控配置:', monitorConfigs);
                console.log('配置已加载:', this.monitorConfigsLoaded);

                // 从监控配置中获取名称
                if (monitorConfigs[monitorId] && monitorConfigs[monitorId].Name) {
                    console.log('找到监控配置:', monitorConfigs[monitorId].Name);
                    return monitorConfigs[monitorId].Name;
                }
                // 如果没有配置，返回默认名称
                console.log('使用默认名称: Monitor', monitorId);
                return `Monitor ${monitorId}`;
            },
            parseMonitorInfo(monitorInfo) {
                console.log('=== parseMonitorInfo 开始 ===');
                console.log('接收到的monitorInfo:', monitorInfo);
                console.log('monitorInfo类型:', typeof monitorInfo);
                console.log('是否为数组:', Array.isArray(monitorInfo));

                let tSeries = [];
                let tLegendData = [];
                var lcolors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'];

                // 检查数据有效性 - 支持直接数组格式和包装格式
                let dataArray = [];
                if (Array.isArray(monitorInfo)) {
                    // 直接是数组格式
                    console.log('使用直接数组格式');
                    dataArray = monitorInfo;
                } else if (monitorInfo && monitorInfo.result && Array.isArray(monitorInfo.result)) {
                    // 包装在result字段中
                    console.log('使用result字段格式');
                    dataArray = monitorInfo.result;
                } else if (monitorInfo && Array.isArray(monitorInfo)) {
                    // 确保是数组
                    console.log('确保是数组格式');
                    dataArray = monitorInfo;
                }

                console.log('最终dataArray长度:', dataArray.length);

                if (!dataArray || dataArray.length === 0) {
                    this.option.title.text = "No Data Available";
                    // 使用requestAnimationFrame避免强制重排
                    requestAnimationFrame(() => {
                        if (this.myChart && typeof this.myChart.clear === 'function') {
                            this.myChart.clear();
                        }
                        if (this.myChart && typeof this.myChart.setOption === 'function') {
                            this.myChart.setOption(this.option, true);
                        }
                    });
                    return;
                }

                // 将原始监控历史数据转换为图表需要的格式
                const processedData = this.processMonitorHistories(dataArray);
                if (!processedData || processedData.length === 0) {
                    this.option.title.text = "No Valid Data";
                    requestAnimationFrame(() => {
                        if (this.myChart && typeof this.myChart.clear === 'function') {
                            this.myChart.clear();
                        }
                        if (this.myChart && typeof this.myChart.setOption === 'function') {
                            this.myChart.setOption(this.option, true);
                        }
                    });
                    return;
                }
                
                // 性能优化：预计算颜色，避免重复计算
                const precomputedColors = lcolors.map(lcolor => ({
                    marker: 'rgba(' + parseInt(lcolor.slice(1, 3), 16) + ',' + parseInt(lcolor.slice(3, 5), 16) + ',' + parseInt(lcolor.slice(5, 7), 16) + ',0.5)',
                    bar: 'rgba(' + parseInt(lcolor.slice(1, 3), 16) + ',' + parseInt(lcolor.slice(3, 5), 16) + ',' + parseInt(lcolor.slice(5, 7), 16) + ',0.35)'
                }));

                for (let i = 0; i < processedData.length; i++) {
                    const colors = precomputedColors[i % precomputedColors.length];
                    let loss = 0;
                    let data = [];
                    let datal = [];

                    // 性能优化：批量处理数据，减少数组访问
                    const timestamps = processedData[i].created_at;
                    const delays = processedData[i].avg_delay;
                    const dataLength = timestamps.length;

                    // 性能优化：预分配数组大小
                    data = new Array(dataLength);
                    datal = [];
                    let dataIndex = 0;

                    for (let j = 0; j < dataLength; j++) {
                        const timestamp = timestamps[j];
                        const avgDelay = Math.round(delays[j]);

                        // 性能优化：减少类型检查
                        if (avgDelay > 0 && avgDelay < MaxTCPPingValue) {
                            data[dataIndex++] = [timestamp, avgDelay];
                        } else {
                            loss++;
                            // 性能优化：只在必要时创建复杂对象
                            if (loss < 100) { // 限制丢包标记数量，避免性能问题
                                datal.push({
                                    xAxis: timestamp,
                                    label: { show: false },
                                    emphasis: { disabled: true },
                                    lineStyle: {
                                        type: "solid",
                                        color: colors.bar
                                    }
                                });
                            }
                        }
                    }

                    // 性能优化：调整数组大小
                    data.length = dataIndex;
                    lossRate = ((loss / timestamps.length) * 100).toFixed(1);
                    if (lossRate > 99) {
                        datal = [];
                    }
                    // 修复：为每个监控点生成唯一的图例名称，区分不同监控点
                    const monitorName = processedData[i].monitor_name || `Monitor ${processedData[i].monitor_id}`;
                    const serverName = processedData[i].server_name || `Server ${processedData[i].server_id}`;

                    // 如果同一个服务器有多个监控点，显示"服务器名 - 监控点名"
                    // 如果只有一个监控点，只显示监控点名
                    let displayName;
                    if (processedData.length > 1) {
                        // 检查是否有多个不同的监控点
                        const uniqueMonitors = new Set(processedData.map(d => d.monitor_id));
                        if (uniqueMonitors.size > 1) {
                            displayName = `${serverName} - ${monitorName}`;
                        } else {
                            displayName = monitorName;
                        }
                    } else {
                        displayName = monitorName;
                    }

                    legendName = displayName + " " + lossRate + "%";
                    tLegendData.push(legendName);
                    // 性能优化：简化图表配置，减少渲染复杂度
                    const seriesConfig = {
                        name: legendName,
                        type: 'line',
                        smooth: true,
                        symbol: 'none',
                        data: data,
                        z: 1,
                        zlevel: 0
                    };

                    // 性能优化：只在丢包数据不多时才添加markLine
                    if (datal.length > 0 && datal.length < 50) {
                        seriesConfig.markLine = {
                            symbol: "none",
                            symbolSize: 0,
                            data: datal,
                            z: 2,
                            zlevel: 0
                        };
                    }

                    // 性能优化：只在数据量适中时才添加markPoint
                    if (data.length > 5 && data.length < 200) {
                        seriesConfig.markPoint = {
                            data: [
                                { type: 'max', symbol: 'pin', name: 'Max', itemStyle: { color: colors.marker }, symbolSize: 30, label: { fontSize: 8 } },
                                { type: 'min', symbol: 'pin', name: 'Min', itemStyle: { color: colors.marker }, symbolSize: 30, label: { fontSize: 8, offset: [0, 7.5] }, symbolRotate: 180 }
                            ],
                            z: 3,
                            zlevel: 0
                        };
                    }

                    tSeries.push(seriesConfig);
                }
                
                // 设置标题，优先使用第一个有效的服务器名称
                let titleText = "Network Monitor";
                if (processedData.length > 0 && processedData[0].server_name) {
                    titleText = processedData[0].server_name;
                }
                
                // 批量更新配置，减少重排
                const maxLegendsPerRowMobile = parseInt(localStorage.getItem("maxLegendsPerRowMobile") || "2");
                const maxLegendsPerRowPc = parseInt(localStorage.getItem("maxLegendsPerRowPc") || "6");
                const autoIncrement = Math.floor((tLegendData.length - 1) / (this.isMobile ? maxLegendsPerRowMobile : maxLegendsPerRowPc)) * (this.isMobile ? 28 : 34);
                const height = (this.isMobile ? 440 : 480) + autoIncrement; // 减少基础高度
                const gridTop = 60 + autoIncrement;
                
                // 一次性更新所有配置
                this.option.title.text = titleText;
                this.option.series = tSeries;
                this.option.legend.data = tLegendData;
                this.option.grid = {
                    left: this.isMobile ? '12%' : '7%', // 进一步增加左边距
                    right: this.isMobile ? '12%' : '7%', // 进一步增加右边距
                    top: gridTop,
                    bottom: this.isMobile ? '13%' : '12%', // PC端略微增加，移动端减少
                    z: 1,
                    zlevel: 0
                };
                
                // 使用requestAnimationFrame优化渲染
                requestAnimationFrame(() => {
                    if (this.myChart && typeof this.myChart.resize === 'function') {
                        this.myChart.resize({
                            width: 'auto',
                            height: height
                        });
                    }
                    if (this.myChart && typeof this.myChart.clear === 'function') {
                        this.myChart.clear();
                    }
                    if (this.myChart && typeof this.myChart.setOption === 'function') {
                        this.myChart.setOption(this.option, true);
                    }
                });
            },
            isWindowsPlatform(str) {
                return str && typeof str === 'string' && str.includes('Windows')
            },
            renderChart() {
                const htmlTheme = $('html').attr('nz-theme');
                const chartTheme = htmlTheme === "dark" ? "dark" : "";

                // 避免重复初始化
                if (this.myChart && typeof this.myChart.isDisposed === 'function' && !this.myChart.isDisposed()) {
                    this.myChart.dispose();
                }
                
                // 延迟初始化，确保DOM准备就绪
                requestAnimationFrame(() => {
                    this.myChart = echarts.init(this.$refs.chartDom, chartTheme, {
                        renderer: 'canvas',
                        useDirtyRect: true,
                        width: 'auto',
                        height: this.isMobile ? 440 : 480 // 减少初始高度
                    });
                    
                    // 恢复图表正常交互，只做必要的滚动优化
                    if (this.isMobile) {
                        // 设置图表容器允许触摸操作
                        this.$refs.chartDom.style.touchAction = 'manipulation';
                    }
                    
                    this.myChart.setOption(this.option, true);
                });
            },
            resizeHandle() {
                if (this.myChart && typeof this.myChart.isDisposed === 'function' && !this.myChart.isDisposed()) {
                    // 使用防抖优化resize性能，增加延迟时间
                    clearTimeout(this.resizeTimer);
                    this.resizeTimer = setTimeout(() => {
                        // 使用requestAnimationFrame进一步优化
                        requestAnimationFrame(() => {
                            if (this.myChart && typeof this.myChart.isDisposed === 'function' && !this.myChart.isDisposed() && typeof this.myChart.resize === 'function') {
                                this.myChart.resize();
                            }
                        });
                    }, 200);
                }
            },
        },
        beforeDestroy() {
            // 清理定时器
            if (this.resizeTimer) {
                clearTimeout(this.resizeTimer);
            }
            // 清理图表实例
            if (this.myChart && typeof this.myChart.isDisposed === 'function' && !this.myChart.isDisposed()) {
                this.myChart.dispose();
            }
            this.myChart = null;
        },
    });
</script>

{{end}}
