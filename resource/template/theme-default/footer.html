{{define "theme-default/footer"}}
</div>
<div class="ui inverted vertical footer segment">
    <div class="ui center aligned is-size-7 container">
        <b>&copy; 2025 <a style="color:#4caf50;" href="/">{{.Conf.Site.Brand}}</a></b> | <a href="http://www.nange.cn"
                style="color: #4caf50;" target="_blank" >楠格</a>
    </div>
</div>
{{ if not .Conf.DisableSwitchTemplateInFrontend }}
<script>
    var def_bg = document.getElementById('def_background');
    if (def_bg) {
        Wallpaper.init(def_bg);
        Wallpaper.animate(true);
        window.onfocus = function () {Wallpaper.update(); };
    }
</script>
{{ end }}
<script>
</script>
</body>
</html>
{{end}}
