{{define "component/server"}}
<div class="ui tiny server modal transition hidden">
    <div class="header">{{tr "AddServer"}}</div>
    <div class="content">
        <form id="serverForm" class="ui form">
            <input type="hidden" name="id">
            <div class="field">
                <label>{{tr "Name"}}</label>
                <input type="text" name="name" placeholder="{{tr "EinsteinLightspeed1"}}">
            </div>
            <div class="field">
                <label>{{tr "ServerGroup"}}</label>
                <input type="text" name="Tag" placeholder="{{tr "ServerGroup"}}">
            </div>
            <div class="field">
                <label>{{tr "DisplayIndex"}}</label>
                <input type="number" name="DisplayIndex" placeholder="{{tr "TheLargerTheNumberTheHigherThePriority"}}">
            </div>
            <div class="secret field">
                <label>{{tr "Secret"}}</label>
                <input type="text" name="secret">
            </div>
            <div class="field">
                <label>{{tr "DDNSProfiles"}}</label>
                <div class="ui fluid multiple ddns search selection dropdown">
                    <input type="hidden" name="DDNSProfilesRaw">
                    <i class="dropdown icon ddnsProfiles"></i>
                    <div class="default text">{{tr "EnterIdAndNameToSearch"}}</div>
                    <div class="menu"></div>
                </div>
            </div>
            <div class="field">
                <div class="ui enableddns checkbox">
                    <input name="EnableDDNS" type="checkbox" tabindex="0" class="hidden" />
                    <label>{{tr "EnableDDNS"}}</label>
                </div>
            </div>
            <div class="field">
                <div class="ui hideforguest checkbox">
                    <input name="HideForGuest" type="checkbox" tabindex="0" class="hidden" />
                    <label>{{tr "HideForGuest"}}</label>
                </div>
            </div>
            <div class="field">
                <label>{{tr "Note"}}</label>
                <textarea name="Note"></textarea>
            </div>
            <div class="field">
                <label>{{tr "PublicNote"}}</label>
                <textarea name="PublicNote"></textarea>
            </div>
            <div class="command field">
                <label>{{tr "LinuxOneKeyInstall"}}</label>
                <div class="ui message">
                    {{if .Conf.GRPCHost}}
                    <div class="ui top attached tabular menu">
                        <a class="item active" data-tab="global-script">Global (默认)</a>
                        <a class="item" data-tab="china-script">China</a>
                    </div>
                    <div class="ui bottom attached tab segment active" data-tab="global-script">
                        curl -L https://raw.githubusercontent.com/xos/serverstatus/master/script/server-status.sh -o server-status.sh && chmod +x server-status.sh && sudo ./server-status.sh install_agent <code class="command">{{.Conf.GRPCHost}}</code> <code
                            class="command">{{if .Conf.ProxyGRPCPort}}{{.Conf.ProxyGRPCPort}}{{else}}{{.Conf.GRPCPort}}{{end}}</code> <code
                            class="command hostSecret"></code> <code class="command">{{if .Conf.TLS}}--tls{{end}}</code>
                    </div>
                    <div class="ui bottom attached tab segment" data-tab="china-script">
                        curl -L https://gitee.com/ten/ServerStatus/raw/master/script/server-status.sh -o server-status.sh && chmod +x server-status.sh && sudo ./server-status.sh install_agent <code class="command">{{.Conf.GRPCHost}}</code> <code
                            class="command">{{if .Conf.ProxyGRPCPort}}{{.Conf.ProxyGRPCPort}}{{else}}{{.Conf.GRPCPort}}{{end}}</code> <code
                            class="command hostSecret"></code> <code class="command">{{if .Conf.TLS}}--tls{{end}}</code>
                    </div>
                    {{else}}
                    {{tr "NoDomainAlert"}}
                    {{end}}
                </div>
            </div>
        </form>
    </div>
    <div class=" actions">
        <div class="ui negative button">{{tr "Cancel"}}</div>
        <button class="ui positive server-primary-btn right labeled icon button">{{tr "Confirm"}}<i class="checkmark icon"></i>
        </button>
    </div>
</div>
<script>
// 初始化标签页功能
document.addEventListener('DOMContentLoaded', function() {
    // 使用原生JavaScript初始化Semantic UI标签页
    if (typeof $ !== 'undefined') {
        $('.tabular.menu .item').tab();
    } else {
        // 如果jQuery不可用，使用原生JavaScript实现简单的标签页切换
        const tabItems = document.querySelectorAll('.tabular.menu .item');
        const tabSegments = document.querySelectorAll('.tab.segment');

        tabItems.forEach(function(item) {
            item.addEventListener('click', function(e) {
                e.preventDefault();

                // 移除所有active类
                tabItems.forEach(function(tab) {
                    tab.classList.remove('active');
                });
                tabSegments.forEach(function(segment) {
                    segment.classList.remove('active');
                });

                // 添加active类到当前项
                this.classList.add('active');
                const targetTab = this.getAttribute('data-tab');
                const targetSegment = document.querySelector('[data-tab="' + targetTab + '"]');
                if (targetSegment) {
                    targetSegment.classList.add('active');
                }
            });
        });
    }
});
</script>
{{end}}
