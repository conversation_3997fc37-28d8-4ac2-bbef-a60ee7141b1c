{{define "component/nat"}}
<div class="ui tiny nat modal transition hidden">
    <div class="header">Add</div>
    <div class="content">
        <form id="natForm" class="ui form">
            <input type="hidden" name="ID">
            <div class="field">
                <label>{{tr "Name"}}</label>
                <input type="text" name="Name">
            </div>
            <div class="field">
                <label>Agent ID</label>
                <input type="number" name="ServerID" placeholder="1">
            </div>
            <div class="field">
                <label>{{tr "LocalService"}}</label>
                <input type="text" name="Host" placeholder="{{tr "LocalServicePlaceholder"}}">
            </div>
            <div class="field">
                <label>{{tr "BindHostname"}}</label>
                <input type="text" name="Domain" placeholder="router.app.yourdomain.com">
            </div>
        </form>
    </div>
    <div class="actions">
        <div class="ui negative button">{{tr "Cancel"}}</div>
        <button class="ui positive server-primary-btn right labeled icon button">{{tr "Confirm"}}<i class="checkmark icon"></i>
        </button>
    </div>
</div>
{{end}}
