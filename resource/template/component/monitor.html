{{define "component/monitor"}}
<div class="ui tiny monitor modal transition hidden">
  <div class="header">{{tr "AddMonitor"}}</div>
  <div class="content">
    <form id="monitorForm" class="ui form">
      <input type="hidden" name="ID" />
      <div class="field">
        <label>{{tr "Name"}}</label>
        <input type="text" name="Name" placeholder="{{tr "Blog"}}" />
      </div>
      <div class="field">
        <label>{{tr "Target"}}</label>
        <input
          type="text"
          name="Target"
          placeholder="1.1.1.1:80"
        />
      </div>
      <div class="field">
        <label>{{tr "Type"}}</label>
        <select name="Type" class="ui fluid dropdown">
          <option value="2">ICMP-Ping</option>
          <option value="3">TCP-Ping</option>
        </select>
      </div>
      <!--<div class="field">-->
      <!--  <div class="ui nb-show-in-service checkbox">-->
      <!--    <input name="EnableShowInService" type="checkbox" tabindex="0" class="hidden" />-->
      <!--    <label>{{tr "EnableShowInService"}}</label>-->
      <!--  </div>-->
      <!--</div>-->
      <div class="field">
        <label>{{tr "Duration"}}</label>
        <input type="number" name="Duration" placeholder="{{tr "Seconds"}}" />
      </div>
      <div class="field">
        <label>{{tr "Coverage"}}</label>
        <select name="Cover" class="ui fluid dropdown">
          <option value="0">{{tr "AllIncludedOnlySpecificServersAreNotRequest"}}</option>
          <option value="1">{{tr "IgnoreAllRequestOnlyThroughSpecificServers"}}</option>
        </select>
      </div>
      <div class="field">
        <label>{{tr "SpecificServers"}}</label>
        <div class="ui fluid multiple servers search selection dropdown">
          <input type="hidden" name="SkipServersRaw" />
          <i class="dropdown icon specificServer"></i>
          <div class="default text">{{tr "EnterIdAndNameToSearch"}}</div>
          <div class="menu"></div>
        </div>
      </div>
      <div class="field">
        <label>{{tr "NotificationMethodGroup"}}</label>
        <input type="text" name="NotificationTag" placeholder="{{tr "Default"}}" />
      </div>
      <div class="field">
        <div class="ui nb-notify checkbox">
          <input name="Notify" type="checkbox" tabindex="0" class="hidden" />
          <label>{{tr "EnableFailureNotification"}}</label>
        </div>
      </div>
      <div class="field">
        <label>{{tr "MaxLatency"}}</label>
        <input type="number" name="MaxLatency" placeholder="100.88" />
      </div>
      <div class="field">
        <label>{{tr "MinLatency"}}</label>
        <input type="number" name="MinLatency" placeholder="100.88" />
      </div>
      <div class="field">
        <div class="ui nb-lt-notify checkbox">
          <input name="LatencyNotify" type="checkbox" tabindex="0" class="hidden" />
          <label>{{tr "EnableLatencyNotification"}}</label>
        </div>
      </div>

      <div class="field">
        <div class="ui nb-EnableTriggerTask checkbox">
          <input name="EnableTriggerTask" type="checkbox" tabindex="0" class="hidden" />
          <label>{{tr "EnableTriggerTask"}}</label>
        </div>
      </div>

      <div class="field">
        <label>{{tr "FailTriggerTasks"}}</label>
        <div class="ui fluid multiple tasks search selection dropdown">
          <input type="hidden" name="FailTriggerTasksRaw">
          <i class="dropdown icon failTask"></i>
          <div class="default text">{{tr "EnterIdAndNameToSearch"}}</div>
          <div class="menu"></div>
        </div>
      </div>
      <div class="field">
        <label>{{tr "RecoverTriggerTasks"}}</label>
        <div class="ui fluid multiple tasks search selection dropdown">
          <input type="hidden" name="RecoverTriggerTasksRaw">
          <i class="dropdown icon recoverTask"></i>
          <div class="default text">{{tr "EnterIdAndNameToSearch"}}</div>
          <div class="menu"></div>
        </div>
      </div>

    </form>
    <div class="ui warning message">
      <p>
        {{safe (tr "IntroductionOfMonitor")}}
      </p>
    </div>
  </div>
  <div class="actions">
    <div class="ui negative button">{{tr "Cancel"}}</div>
    <button class="ui positive server-primary-btn right labeled icon button">
      {{tr "Confirm"}}<i class="checkmark icon"></i>
    </button>
  </div>
</div>
{{end}}
