{{define "component/notification"}}
<div class="ui tiny notification modal transition hidden">
    <div class="header">{{tr "AddNotificationMethod"}}</div>
    <div class="content">
        <form id="notificationForm" class="ui form">
            <input type="hidden" name="ID">
            <div class="field">
                <label>{{tr "Name"}}</label>
                <input type="text" name="Name">
            </div>
            <div class="field">
                <label>{{tr "Tag"}}</label>
                <input type="text" name="Tag" placeholder="{{tr "Default"}}">
            </div>
            <div class="field">
                <label>URL</label>
                <input type="text" name="URL">
            </div>
            <div class="field">
                <label>{{tr "RequestMethod"}}</label>
                <select name="RequestMethod" class="ui fluid dropdown">
                    <option value="1">GET</option>
                    <option value="2">POST</option>
                </select>
            </div>
            <div class="field">
                <label>{{tr "RequestType"}}</label>
                <select name="RequestType" class="ui fluid dropdown">
                    <option value="1">JSON</option>
                    <option value="2">FORM</option>
                </select>
            </div>
            <div class="secret field">
                <label>Header</label>
                <textarea name="RequestHeader" placeholder='{"User-Agent":"Server-Agent"}'></textarea>
            </div>
            <div class="secret field">
                <label>Body</label>
                <textarea name="RequestBody" placeholder='{&#13;&#10;  "content":"#NG#",&#13;&#10;  "ServerName":"#SERVER.NAME#",&#13;&#10;  "ServerIP":"#SERVER.IP#",&#13;&#10;  "ServerIPV4":"#SERVER.IPV4#",&#13;&#10;  "ServerIPV6":"#SERVER.IPV6#",&#13;&#10;  "CPU":"#SERVER.CPU#",&#13;&#10;  "MEM":"#SERVER.MEM#",&#13;&#10;  "SWAP":"#SERVER.SWAP#",&#13;&#10;  "DISK":"#SERVER.DISK#",&#13;&#10;  "NetInSpeed":"#SERVER.NETINSPEED#",&#13;&#10;  "NetOutSpeed":"#SERVER.NETOUTSPEED#",&#13;&#10;  "TransferIn":"#SERVER.TRANSFERIN#",&#13;&#10;  "TranferOut":"#SERVER.TRANSFEROUT#",&#13;&#10;  "Load1":"#SERVER.LOAD1#",&#13;&#10;  "Load5":"#SERVER.LOAD5#",&#13;&#10;  "Load15":"#SERVER.LOAD15#,"&#13;&#10;  "TCP_CONN_COUNT":"#SERVER.TCPCONNCOUNT",&#13;&#10;  "UDP_CONN_COUNT":"#SERVER.UDPCONNCOUNT"&#13;&#10;}'></textarea>
            </div>
            <div class="field">
                <div class="ui nf-ssl checkbox">
                    <input name="VerifySSL" type="checkbox" tabindex="0" class="hidden">
                    <label>{{tr "VerifySSL"}}</label>
                </div>
            </div>
            <div class="field">
                <div class="ui nf-skip-check checkbox">
                    <input name="SkipCheck" type="checkbox" tabindex="0" class="hidden">
                    <label>{{tr "DoNotSendTestMessages"}}</label>
                </div>
            </div>
        </form>
    </div>
    <div class="actions">
        <div class="ui negative button">{{tr "Cancel"}}</div>
        <button class="ui positive server-primary-btn right labeled icon button">{{tr "Confirm"}}<i class="checkmark icon"></i>
        </button>
    </div>
</div>
{{end}}