{{define "component/cron"}}
<div class="ui tiny cron modal transition hidden">
    <div class="header">{{tr "AddScheduledTasks"}}</div>
    <div class="content">
        <form id="cronForm" class="ui form">
            <input type="hidden" name="ID">
            <div class="field">
                <label>{{tr "Name"}}</label>
                <input type="text" name="Name" placeholder="{{tr "BackUp"}}">
            </div>
            <div class="field">
                <label>{{tr "TaskType"}}</label>
                <select name="TaskType" class="ui fluid dropdown">
                    <option value="0">{{tr "CronTask"}}</option>
                    <option value="1">{{tr "TriggerTask"}}</option>
                </select>
            </div>
            <div class="field">
                <label>{{tr "Scheduler"}}</label>
                <input type="text" name="Scheduler" placeholder="0 0 3 * * *{{tr "3amDaily"}}">
            </div>
            <div class="field">
                <label>{{tr "Command"}}</label>
                <textarea name="Command"></textarea>
            </div>
            <div class="field">
                <label>{{tr "Coverage"}}</label>
                <select name="Cover" class="ui fluid dropdown">
                    <option value="0">{{tr "IgnoreAllAndExecuteOnlyThroughSpecificServers"}}</option>
                    <option value="1">{{tr "AllIncludedOnlySpecificServersAreNotExecuted"}}</option>
                    <option value="2">{{tr "ExecuteByTriggerServer"}}</option>
                </select>
            </div>
            <div class="field">
                <label>{{tr "SpecificServers"}}</label>
                <div class="ui fluid multiple servers search selection dropdown">
                    <input type="hidden" name="ServersRaw">
                    <i class="dropdown icon"></i>
                    <div class="default text">{{tr "EnterIdAndNameToSearch"}}</div>
                    <div class="menu"></div>
                </div>
            </div>
            <div class="field">
                <label>{{tr "NotificationMethodGroup"}}</label>
                <input type="text" name="NotificationTag" placeholder="{{tr "Default"}}">
            </div>
            <div class="field">
                <div class="ui push-successful checkbox">
                    <input name="PushSuccessful" type="checkbox" tabindex="0" class="hidden">
                    <label>{{tr "PushSuccessMessages"}}</label>
                </div>
            </div>
        </form>
        <div class="ui warning message">
            <p>
                {{tr "TheFormaOfTheScheduleIs"}} <code>* * * * * *</code>&nbsp; {{tr "SecondsMinutesHoursDaysMonthsWeeksSeeDetails"}} <a
                    href="https://pkg.go.dev/github.com/robfig/cron/v3#hdr-CRON_Expression_Format"
                    target="_blank">{{tr "ScheduleExpressionFormat"}}</a><br>
                {{safe (tr "IntroductionOfCommands")}}
            </p>
        </div>
    </div>
    <div class=" actions">
        <div class="ui negative button">{{tr "Cancel"}}</div>
        <button class="ui positive server-primary-btn right labeled icon button">{{tr "Confirm"}}<i class="checkmark icon"></i>
        </button>
    </div>
</div>
{{end}}