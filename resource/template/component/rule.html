{{define "component/rule"}}
<div class="ui tiny rule modal transition hidden">
    <div class="header">{{tr "AddNotificationRule"}}</div>
    <div class="content">
        <form id="ruleForm" class="ui form">
            <input type="hidden" name="ID">
            <div class="field">
                <label>{{tr "Name"}}</label>
                <input type="text" name="Name">
            </div>
            <div class="secret field">
                <label>{{tr "Rules"}}</label>
                <textarea name="RulesRaw"></textarea>
            </div>
            <div class="field">
                <label>{{tr "NotificationMethodGroup"}}</label>
                <input type="text" name="NotificationTag" placeholder="{{tr "Default"}}">
            </div>
            <div class="field">
                <label>{{tr "NotificationTriggerMode"}}</label>
                <select name="TriggerMode" class="ui fluid dropdown">
                    <option value="0">{{tr "ModeAlwaysTrigger"}}</option>
                    <option value="1">{{tr "ModeOnetimeTrigger"}}</option>
                </select>
            </div>
            <div class="field">
                <label>{{tr "FailTriggerTasks"}}</label>
                <div class="ui fluid multiple tasks search selection dropdown">
                    <input type="hidden" name="FailTriggerTasksRaw">
                    <i class="dropdown icon 1"></i>
                    <div class="default text">{{tr "EnterIdAndNameToSearch"}}</div>
                    <div class="menu"></div>
                </div>
            </div>
            <div class="field">
                <label>{{tr "RecoverTriggerTasks"}}</label>
                <div class="ui fluid multiple tasks search selection dropdown">
                    <input type="hidden" name="RecoverTriggerTasksRaw">
                    <i class="dropdown icon 2"></i>
                    <div class="default text">{{tr "EnterIdAndNameToSearch"}}</div>
                    <div class="menu"></div>
                </div>
            </div>

            <div class="field">
                <div class="ui rule-enable checkbox">
                    <input name="Enable" type="checkbox" tabindex="0" class="hidden">
                    <label>{{tr "Enable"}}</label>
                </div>
            </div>
        </form>
    </div>
    <div class="actions">
        <div class="ui negative button">{{tr "Cancel"}}</div>
        <button class="ui positive server-primary-btn right labeled icon button">{{tr "Confirm"}}<i class="checkmark icon"></i>
        </button>
    </div>
</div>
{{end}}