{{define "component/ddns"}}
<div class="ui tiny ddns modal transition hidden">
    <div class="header">Add</div>
    <div class="content">
        <form id="ddnsForm" class="ui form">
            <input type="hidden" name="ID">
            <div class="field">
                <label>{{tr "Name"}}</label>
                <input type="text" name="Name">
            </div>
            <div class="field">
                <label>{{tr "DDNSProvider"}}</label>
                <select name="Provider" class="ui fluid dropdown" id="providerSelect" onchange="toggleFields()">
                    {{ range $provider := .ProviderList }}
                    <option value="{{ $provider.ID }}">
                        {{ $provider.Name }}
                    </option>
                    {{ end }}
                </select>
            </div>
            <div class="field">
                <label>{{tr "DDNSDomains"}}</label>
                <input type="text" name="DomainsRaw" placeholder="www.example.com">
            </div>
            <div class="field">
                <label>{{tr "DDNSAccessID"}}</label>
                <input type="text" name="AccessID" placeholder="{{tr "DDNSTokenID"}}">
            </div>
            <div class="field">
                <label>{{tr "DDNSAccessSecret"}}</label>
                <input type="text" name="AccessSecret" placeholder="{{tr "DDNSTokenSecret"}}">
            </div>
            <div class="field">
                <label>{{tr "MaxRetries"}}</label>
                <input type="number" name="MaxRetries" placeholder="3">
            </div>
            <div class="field">
                <label>{{tr "WebhookURL"}}</label>
                <input type="text" name="WebhookURL" placeholder="https://ddns.example.com/?record=#record#">
            </div>
            <div class="field">
                <label>{{tr "WebhookMethod"}}</label>
                <select name="WebhookMethod" class="ui fluid dropdown">
                    <option value="1">GET</option>
                    <option value="2">POST</option>
                    <option value="3">PATCH</option>
                    <option value="4">DELETE</option>
                    <option value="5">PUT</option>
                </select>
            </div>
            <div class="field">
                <label>{{tr "WebhookRequestType"}}</label>
                <select name="WebhookRequestType" class="ui fluid dropdown">
                    <option value="1">JSON</option>
                    <option value="2">Form</option>
                </select>
            </div>
            <div class="field">
                <label>{{tr "WebhookHeaders"}}</label>
                <textarea name="WebhookHeaders" placeholder='{"User-Agent":"ServerStatus"}'></textarea>
            </div>
            <div class="field">
                <label>{{tr "WebhookRequestBody"}}</label>
                <textarea name="WebhookRequestBody" placeholder='{&#13;&#10; "ip": #ip#,&#13;&#10; "domain": "#domain#"&#13;&#10;}'></textarea>
            </div>
            <div class="field">
                <div class="ui enableipv4 checkbox">
                    <input name="EnableIPv4" type="checkbox" tabindex="0" class="hidden">
                    <label>{{tr "EnableIPv4"}}</label>
                </div>
            </div>
            <div class="field">
                <div class="ui enableipv6 checkbox">
                    <input name="EnableIPv6" type="checkbox" tabindex="0" class="hidden">
                    <label>{{tr "EnableIPv6"}}</label>
                </div>
            </div>
        </form>
    </div>
    <div class="actions">
        <div class="ui negative button">{{tr "Cancel"}}</div>
        <button class="ui positive server-primary-btn right labeled icon button">{{tr "Confirm"}}<i class="checkmark icon"></i>
        </button>
    </div>
</div>
{{end}}
