{{define "common/footer"}}
<div class="ui inverted vertical footer segment">
    <div class="ui center aligned is-size-7 container">
        <b>&copy; 2025 <a style="color:#4caf50;" href="/">{{.Conf.Site.Brand}}</a></b> | <a href="http://www.nange.cn"
                style="color: #4caf50;" target="_blank" >楠格</a>
    </div>
</div>
<script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/3.6.0/jquery.min.js"></script>
<script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/semantic-ui/2.4.1/semantic.min.js"></script>
<script src="/static/wallpaper.js?v20220423"></script>
<script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/2.6.14/vue.min.js"></script>
<script src="/static/main.js?v20250526"></script>
<script>
    var def_bg = document.getElementById('def_background');
    if (def_bg) {
        Wallpaper.init(def_bg);
    Wallpaper.animate(true);
    window.onfocus = function () {
      Wallpaper.update(); 
     };
	}
</script>
<script>
    (function () {
        updateLang({{.LANG }});
    })();
</script>
</body>
</html>
{{end}}