{{define "common/menu"}}
<div class="ui large top fixed menu nb-menu">
    <div class="ui container">
        <div class="item">
            <a class="logo{{if eq .MatchedPath " /"}} active{{end}}" href="/"><img src="/static/logo.svg?v20220602" style="display: inline-block; vertical-align: middle; margin: -.3em 0; width: 2.5em;"></a>
        </div>
        {{if .IsAdminPage}}
        <a class='item{{if eq .MatchedPath "/server"}} active{{end}}' href="/server"><i class="server icon"></i>{{tr "Server"}}</a>
        <a class='item{{if eq .MatchedPath "/setting"}} active{{end}}' href="/setting">
            <i class="fa-solid fa-sliders icon"></i>{{tr "Settings"}}
        </a>
        {{else}}
        <a class='item{{if eq .MatchedPath "/network"}} active{{end}}' href="/network"><i class="dot circle outline icon"></i>{{tr "NetworkStatus"}}</a>
        <a class="item" href="http://status.nange.cn/" target="_blank"><i class="server icon"></i>{{tr "Services"}}</a>
        {{end}}
        <div class="right menu">
            <div class="item">
                {{if .Admin}}
                <div class="ui simple dropdown">
                    <div class="text">
                        <img class="ui avatar image" src="{{.Admin.AvatarURL}}"> {{.Admin.Name}}
                    </div>
                    <i class="dropdown icon"></i>
                    <div class="menu">
                        {{if .IsAdminPage}}
                        <a class="item" href="/">
                            <i class="home icon"></i>{{tr "BackToHomepage"}}
                        </a>
                        <a class='item{{if eq .MatchedPath "/cron"}} active{{end}}' href="/cron"><i class="clock icon"></i>{{tr "Task"}}</a>
                        <a class='item{{if eq .MatchedPath "/monitor"}} active{{end}}' href="/monitor"><i class="dot circle outline icon"></i>{{tr "Services"}}</a>
                        <a class='item{{if eq .MatchedPath "/api"}} active{{end}}' href="/api"><i class="chart key icon"></i>{{tr "ApiManagement"}}</a>
						<a class='item{{if eq .MatchedPath "/ddns"}} active{{end}}' href="/ddns"><i class="globe icon"></i>{{tr "DDNS"}}</a>
                        <a class='item{{if eq .MatchedPath "/nat"}} active{{end}}' href="/nat"><i class="exchange icon"></i>{{tr "NAT"}}</a>
                        <a class='item{{if eq .MatchedPath "/notification"}} active{{end}}' href="/notification"><i class="bell icon"></i>{{tr "Notification"}}</a>
                        {{else}}
                        <a class="item" href="/server">
                            <i class="th icon"></i>{{tr "AdminPanel"}}
                        </a>
                        {{end}}
                        <button class="item" onclick="showConfirm('{{tr "ConfirmLogout"}}','{{tr "AfterLoggingOutYouHaveToLoginAgain"}}',logout,{{.Admin.ID}})">
                            <i class="logout icon"></i>{{tr "Logout"}}
                        </button>
                    </div>
                </div>
                {{else}}
                <a href="/login" class="ui large positive server-primary-btn button"><i class="sign-in icon"></i>{{tr "Login"}}</a>
                {{end}}
            </div>
        </div>
    </div>
</div>
{{template "component/confirm" .}}
{{end}}