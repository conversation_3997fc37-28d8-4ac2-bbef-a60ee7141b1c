[ServerMonitoring]
other = "服务器"

[All]
other = "全部"

[ID]
other = "标识号"

[GPU]
other = "显卡"

[Connecting]
other = "连接中"

[NoServers]
other = "没有服务器"

[NoServersDesc]
other = "没有具体服务器描述"

[NoServersForTagH]
other = "没有服务器标签"

[NoServersForTagP]
other = "没有具体服务器标签"

[Server]
other = "主机"

[Services]
other = "服务"

[Task]
other = "任务"

[Notification]
other = "告警"

[Settings]
other = "设置"

[Home]
other = "首页"

[BackToHomepage]
other = "前台"

[AdminPanel]
other = "管理"

[Logout]
other = "注销"

[Login]
other = "登录"

[ConfirmLogout]
other = "确认注销？"

[AfterLoggingOutYouHaveToLoginAgain]
other = "注销后您必须重新登录才能使用"

[Cancel]
other = "取消"

[Confirm]
other = "确认"

[AddScheduledTasks]
other = "添加计划任务"

[Name]
other = "名称"

[Scheduler]
other = "计划"

[BackUp]
other = "备份"

[3amDaily]
other = "(每天3点)"

[Command]
other = "命令"

[Coverage]
other = "覆盖范围"

[IgnoreAllAndExecuteOnlyThroughSpecificServers]
other = "忽略所有，仅通过特定服务器执行"

[AllIncludedOnlySpecificServersAreNotExecuted]
other = "覆盖所有，仅特定服务器不执行"

[ExecuteByTriggerServer]
other = "由触发的服务器执行"

[SpecificServers]
other = "特定服务器"

[EnterIdAndNameToSearch]
other = "输入ID/名称以搜索"

[NotificationMethodGroup]
other = "通知方式组"

[PushSuccessMessages]
other = "推送成功的消息"

[TaskType]
other = "任务类型"

[CronTask]
other = "计划任务"

[TriggerTask]
other = "触发任务"

[TheFormaOfTheScheduleIs]
other = "计划的格式为："

[SecondsMinutesHoursDaysMonthsWeeksSeeDetails]
other = "秒 分 时 天 月 星期，详情见"

[ScheduleExpressionFormat]
other = "计划表达式格式"

[IntroductionOfCommands]
other = "命令说明：编写命令时类似于 shell/bat 脚本。建议不要换行，多个命令可用 <code>&&</code> 或 <code>&</code> 连接，若出现命令无法找到的情况，可能是由于 <code>PATH</code> 环境变量配置问题。在 <code>Linux</code> 服务器上，可在命令开头加入 <code>source ~/.bashrc</code>，或使用命令的绝对路径执行。"

[AddMonitor]
other = "添加监测项"

[Blog]
other = "博客"

[Target]
other = "目标"

[Type]
other = "类型"

[SslExpirationOrChange]
other = "(SSL到期、变更)"

[Duration]
other = "请求间隔"

[Seconds]
other = "秒"

[EnableFailureNotification]
other = "启用故障通知"

[FailureNotification]
other = "故障通知"

[MaxLatency]
other = "最大延迟(ms)"

[MinLatency]
other = "最小延迟(ms)"

[EnableLatencyNotification]
other = "启用延迟通知"

[LatencyNotification]
other = "延迟通知"

[IntroductionOfMonitor]
other = """
类型为 <b>ICMP-Ping</b> 时输入主机名/IP，不带端口；<br>
类型为 <b>TCP-Ping</b> 时输入主机名/IP + 端口号：*******:53"""

[AddNotificationMethod]
other = "添加通知方式"

[Tag]
other = "分组"

[DoNotSendTestMessages]
other = "不发送测试信息"

[RequestMethod]
other = "请求方式"

[RequestType]
other = "请求类型"

[VerifySSL]
other = "验证SSL"

[AddNotificationRule]
other = "添加报警规则"

[Rules]
other = "规则"

[NotificationTriggerMode]
other = "通知触发模式"

[ModeAlwaysTrigger]
other = "始终触发"

[ModeOnetimeTrigger]
other = "单次触发"

[EnableTriggerTask]
other = "启用触发任务"

[FailTriggerTasks]
other = "故障时触发任务"

[RecoverTriggerTasks]
other = "恢复时触发任务"

[Enable]
other = "启用"

[AddServer]
other = "添加服务器"

[BatchEditServerGroup]
other = "批量修改分组"

[BatchDeleteServer]
other = "批量删除服务器"

[InputServerGroupName]
other = "输入分组名称"

[ServerGroup]
other = "分组"

[EinsteinLightspeed1]
other = "北京"

[DisplayIndex]
other = "排序"

[TheLargerTheNumberTheHigherThePriority]
other = "越大越靠前"

[Secret]
other = "密钥"

[CopySecret]
other="复制密钥"

[Note]
other = "备注"

[PublicNote]
other = "公开备注"

[LinuxOneKeyInstall]
other = "Linux 一键安装"

[NoDomainAlert]
other = "请先在设置页面配置 后端探针配置"

[PushSuccessfully]
other = "成功推送"

[LastExecution]
other = "最后执行"

[LastResult]
other = "最后结果"

[Administration]
other = "操作"

[CoverAll]
other = "覆盖所有"

[IgnoreAll]
other = "忽略所有"

[ByTrigger]
other = "触发执行"

[DeleteScheduledTask]
other = "删除计划任务"

[ConfirmToDeleteThisScheduledTask]
other = "确认删除此计划任务？"

[AccessDenied]
other = "访问受限"

[Use]
other = "使用"

[AccountToLogin]
other = "账号登录"

[SignUp]
other = "注册"

[DontHaveAnAccount]
other = "没有账号？"

[SSLCertificate]
other = "HTTP(S)/SSL证书"

[TCPPort]
other = "TCP 端口"

[DeleteService]
other = "删除服务"

[ConfirmToDeleteThisService]
other = "确认删除此服务？"

[DeleteNotificationMethod]
other = "删除通知方式"

[ConfirmToDeleteThisNotificationMethod]
other = "确认删除此通知方式？"

[DeleteNotificationRule]
other = "删除报警规则"

[ConfirmToDeleteThisNotificationRule]
other = "确认删除此报警规则？"

[ForceUpdate]
other = "强制更新"

[SelectAll]
other = "全选"

[VersionNumber]
other = "版本"

[OneKeyInstall]
other = "一键安装"

[ClickToCopy]
other = "点击复制"

[DeleteServer]
other = "删除主机"

[ConfirmToDeleteThisServer]
other = "确认删除此主机？"

[ConfirmToDeleteServer]
other = "确认删除主机？"

[NoServerSelected]
other = "当前没有选中的服务器"

[ExecutionResults]
other = "执行结果"

[SiteTitle]
other = "站点标题"

[AdministratorList]
other = "管理员列表"

[Theme]
other = "主题"

[CustomCodes]
other = "自定义代码(style、script 都可以)"

[CustomCodesDashboard]
other = "自定义代码"

[AccessPassword]
other = "前台查看密码"

[AgentConfig]
other = "后端探针配置"

[AgentDomainOrIP]
other = "后端探针指向的域名或IP"

[AgentPort]
other = "后端探针指向的端口"

[IPChangeAlert]
other = "IP 变更提醒"

[AllIncludedOnlySpecificServersAreNotAlerted]
other = "覆盖所有，仅特定服务器不提醒"

[IgnoreAllOnlyAlertSpecificServers]
other = "忽略所有，仅提醒特定服务器"

[IgnoreAllRequestOnlyThroughSpecificServers]
other = "忽略所有，仅通过特定服务器请求"

[AllIncludedOnlySpecificServersAreNotRequest]
other = "覆盖所有，仅特定服务器不请求"

[ServerIDSeparatedByCommas]
other = "服务器 ID 以逗号隔开"

[IPChangeNotificationTag]
other = "提醒发送至指定的通知分组"

[NotificationMessagesDoNotHideIP]
other = "通知信息 IP 不打码"

[Save]
other = "保存"

[ModifiedSuccessfully]
other = "修改成功"

[TerminalConnectionTimeOutOrSessionEnded]
other = "Terminal 连接超时或会话已结束"

[TerminalConnectionFailed]
other = "Terminal 连接失败，请检查 /terminal/* 的 WebSocket 反代情况"

[Default]
other = "默认"

[Offline]
other = "已离线"

[Platform]
other = "架构"

[DiskUsed]
other = "硬盘"

[MemUsed]
other = "内存"

[CpuUsed]
other = "核心"

[Virtualization]
other = "虚拟化"

[SwapUsed]
other = "交换"

[NetTransfer]
other = "传输"

[Load]
other = "负载"

[ProcessCount]
other = "进程"

[ConnCount]
other = "连接"

[BootTime]
other = "启动"

[LastActive]
other = "活动"

[Version]
other = "版本"

[NetSpeed]
other = "速率"

[Uptime]
other = "在线"

[ServerIsOffline]
other = "节点已离线"

[Day]
other = "天"

[RealtimeChannelEstablished]
other = "实时通道建立"

[GetTheLatestMonitoringDataInRealTime]
other = "可以实时获取最新监控数据啦"

[RealtimeChannelDisconnect]
other = "实时通道断开"

[CanNotGetTheLatestMonitoringDataInRealTime]
other = "无法实时获取最新监控数据咯"

[30DaysOnline]
other = "30天在线率"

[Details]
other = "详情"

[Status]
other = "状态"

[Availability]
other = "在线率"

[AverageLatency]
other = "平均延迟"

[CycleTransferStats]
other = "流量统计"

[From]
other = "起始"

[To]
other = "结束"

[NextCheck]
other = "下一次检测"

[CurrentUsage]
other = "当前用量"

[TrafficTotal]
other = "流量"

[VerifyPassword]
other = "验证查看密码"

[LightMode]
other = "亮色模式"

[DarkMode]
other = "暗色模式"

[FollowSystem]
other = "跟随系统"

[GridLayout]
other = "网格布局"

[ListLayout]
other = "列表布局"

[EnterPassword]
other = "请输入密码"

[Location]
other = "地区"

[Running]
other = "运行中"

[UpNetTransfer]
other = "上行"

[DownNetTransfer]
other = "下行"

[TotalUpNetTransfer]
other = "总上行"

[TotalDownNetTransfer]
other = "总下行"

[WrongPassword]
other = "密码错误"

[AnErrorEccurred]
other = "发生错误"

[SystemError]
other = "系统错误"

[NetworkError]
other = "网络错误"

[ServicesStatus]
other = "服务状态"

[ServersManagement]
other = "服务器管理"

[ServicesManagement]
other = "服务监控"

[ScheduledTasks]
other = "计划任务"

[ApiManagement]
other="API"

[IssueNewApiToken]
other="添加密钥"

[APIToken]
other="API 密钥"

[Token]
other="密钥"

[DeleteToken]
other="删除密钥"

[ConfirmToDeleteThisToken]
other="确认删除密钥？"

[YouAreNotAuthorized]
other = "此页面需要登录"

[WrongAccessPassword]
other = "访问密码错误"

[Add]
other = "添加"

[Edit]
other = "修改"

[AlarmRule]
other = "告警规则"

[NotificationMethod]
other = "通知方式"

[Incident]
other = "事件"

[Resolved]
other = "恢复"

[StatusDown]
other = "故障"

[StatusNoData]
other = "无数据"

[StatusGood]
other = "正常"

[StatusLowAvailability]
other = "低可用"

[ScheduledTaskExecutedSuccessfully]
other = "任务执行成功"

[ScheduledTaskExecutedFailed]
other = "任务执行失败"

[IPAddress]
other = "IP 地址"

[IPChanged]
other = "IP 变更"

[Notify]
other = "探针通知"

[OldIP]
other = "旧 IP："

[NewIP]
other = "新 IP："

[Rule]
other = "规则："

[TaskServer]
other = "服务器："

[TaskLog]
other = "日志："

[NoTrafficData]
other = "无数据"

[Traffic]
other = "流量"

[Transleft]
other = "剩余流量"

[DashboardTheme]
other = "管理后台主题"

[Info]
other = "信息"

[HideForGuest]
other = "仅管理可见"

[Menu]
other = "菜单"

[Detail]
other = "配置"

[NetworkStatus]
other = "网络"


[EnableShowInService]
other = "在服务中显示"

[DDNS]
other = "DDNS"

[DDNSProfiles]
other = "DDNS 配置"

[AddDDNSProfile]
other = "新配置"

[EnableDDNS]
other = "启用 DDNS"

[EnableIPv4]
other = "启用 DDNS IPv4"

[EnableIPv6]
other = "启用 DDNS IPv6"

[DDNSDomain]
other = "DDNS 域名"

[DDNSDomains]
other = "域名（逗号分隔）"

[DDNSProvider]
other = "DDNS 供应商"

[MaxRetries]
other = "最大重试次数"

[DDNSAccessID]
other = "DDNS 凭据 1"

[DDNSAccessSecret]
other = "DDNS 凭据 2"

[DDNSTokenID]
other = "令牌 ID"

[DDNSTokenSecret]
other = "令牌 Secret"

[WebhookURL]
other = "Webhook 地址"

[WebhookMethod]
other = "Webhook 请求方式"

[WebhookRequestType]
other = "Webhook 请求类型"

[WebhookHeaders]
other = "Webhook 请求头"

[WebhookRequestBody]
other = "Webhook 请求体"

[Feature]
other = "功能"

[Template]
other = "主题"

[Stat]
other = "信息"

[Temperature]
other = "温度"

[DisableSwitchTemplateInFrontend]
other = "禁止前台切换模板"

[NoSwap]
other = "无 Swap 分区"

[Count]
other = "个"

[ServersOnWorldMap]
other = "服务器世界分布图"

[NAT]
other = "内网"

[LocalService]
other = "内网服务"

[LocalServicePlaceholder]
other = "***********:80(带端口)"

[BindHostname]
other = "绑定域名"

[NetworkSpiterList]
other = "网络监控"

[Refresh]
other = "刷新"

[CopyPath]
other = "复制路径"

[Goto]
other = "跳往"

[GotoHeadline]
other = "跳转文件夹"

[GotoGo]
other = "确认"

[GotoClose]
other = "取消"

[FMError]
other = "Agent 返回了错误，请查看控制台获取详细信息。要建立新连接，请重新打开 FM。"

[Remaining]
other = "剩余"

[Lifetime]
other = "永续"

[Price]
other = "价格"

[Expired]
other = "已到期"

[Days]
other = "天"

[CustomNameservers]
other = "自定义DDNS使用的公共DNS服务器（逗号分隔）"

[Plan]
other = "套餐"

[TrafficAlert]
other = "流量告警"

[SetupTrafficAlert]
other = "设置流量告警"

[MonthlyTrafficLimit]
other = "月流量限制"

[NotificationGroup]
other = "通知组"

[AlertThresholds]
other = "告警阈值"

[Alert50Percent]
other = "50% 流量告警"

[Alert90Percent]
other = "90% 流量告警"

[Alert100Percent]
other = "100% 流量告警"

[CreateAlert]
other = "创建告警"

[TrafficAlertInfo]
other = "流量告警说明"

[TrafficAlertInfo1]
other = "流量统计周期为自然月（每月1号重置）"

[TrafficAlertInfo2]
other = "可同时启用多个阈值告警"

[TrafficAlertInfo3]
other = "告警将发送到指定的通知组"
