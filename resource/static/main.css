@media only screen and (min-width: 1390px) {
    .ui.container {
        width: 77%;
    }
    .ui.four.cards>.card {
        margin: 0.5em!important;
        width: calc(25% - 1em);!important
    }
    
}

@media only screen and (max-width: 1389px) {
    .cards {
        justify-content: center;
    }
    .ui.four.cards>.card {
        width: 300px!important;
        margin: 0.5em !important;
    }
}

@media only screen and (min-width: 768px) {
    .cpucontent {
        display: inline-block;
    }

    .rollanimation {
        animation: scroll 10s cubic-bezier(.2, 0, .8, 1) infinite;
    }
    /*.ui.four.cards>.card {*/
    /*    width: auto! important;*/
    /*}*/

    @keyframes scroll {
        0% { transform: translateX(0%); }
        30% { transform: translateX(0%); }
        100% { transform: translateX(-100%); }
    }
}


@media only screen and (max-width: 767px) {
    .cards {
        justify-content: center;
    }
    .ui.four.cards>.card {
        width: auto! important;
        margin-bottom: 1em !important;
    }
    .ui.stackable.cards .card:first-child {
        margin-top: 1em!important;
    }
}

.custom-tabs-container {
    overflow-x: auto;
    width: fit-content;
    max-width: 100%;
    white-space: nowrap;
    position: relative;
    background-color: #fbfbfb26;
    border-radius: 22px;
    padding: 5px 3px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.10);
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
}
.custom-tabs-container::-webkit-scrollbar {
    display: none;
}

.tabs-wrapper {
    display: flex;
    justify-content: center;
}

.tabs-content-area {
    display: inline-flex;
    align-items: center;
}

.custom-tab {
    padding: 6px 18px;
    margin: 0 3px;
    cursor: pointer;
    color: #777;
    border-radius: 17px;
    transition: color 0.2s ease;
    font-size: 15px;
    position: relative;
    z-index: 2;
    background-color: transparent;
    font-weight: normal;
    display: inline-block;
    line-height: 1.5;
    -webkit-tap-highlight-color: transparent;
}

.custom-tab.active {
  color: #333;
  font-weight: bold;
}

.custom-tab:not(.active):hover {
    color: #333;
}

.tab-slider {
    position: absolute;
    top: 5px;
    bottom: 5px;
    height: calc(100% - 10px);
    background-color: #fafafaa3;
    border-radius: 17px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    z-index: 1;
    transition: left 0.3s ease, width 0.3s ease;
}

.ui.styled.accordion {
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-top: 20px;
}
.ui.styled.accordion .title {
    color: #333 !important;
    border-bottom: 1px solid #eee;
    padding: 0.75em 1em !important;
}
.ui.styled.accordion .active.title {
    border-bottom: 1px solid #eee;
}
.ui.styled.accordion .title:hover {
    background: rgba(0,0,0,0.03) !important;
}
.ui.styled.accordion .content,
.ui.styled.accordion .active.content {
    padding: 1em !important;
}
.ui.styled.accordion .dropdown.icon {
    color: #555 !important;
}

.ui.card .description .ui.grid,
.ui.card .description .ui.grid > .row {
    display: flex!important;
    align-items: baseline !important;
}

.ui.card .description .ui.grid > .column {
    display: block!important;
}

.ui.card .description .ui.grid > .three.wide.column {
    padding-top: 2px!important;
    line-height: 1.5em!important;
}

.ui.card .description .ui.grid > .row {
    display: flex!important;
    align-items: center!important;
}

.ui.card>.content>.header_info,
.ui.cards>.card>.content>.header_info {
    line-height: 1rem !important;
    padding-right: .38rem !important;
    display: flex;
    color: rgb(0 0 0 / 60%);
    align-items: center;
    padding-bottom: .3em;
    border-bottom: 1px solid rgba(34, 36, 38, 0.15) !important;
    font-size: 12px !important;
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
}

.ui.cards {
    margin: -0.5em -.5em!important;
}

.ui.card>.content>.header+.description,
.ui.cards>.card>.content>.header+.description {
    margin-top: 1em !important;
}

.ui.card>.content>.header:not(.ui),
.ui.cards>.card>.content>.header:not(.ui) {
    /*margin-top: 0.1em !important;*/
    margin-bottom: -0.2em !important;
}

.header_info i {
    margin-right: .2rem;
    margin-left: .2rem;
}

.cpucontent {
    display: inline-block;
}

.rollanimation {
    animation: scroll 10s cubic-bezier(.2, 0, .8, 1) infinite;
}

@keyframes scroll {
    0% { transform: translateX(0%); }
    30% { transform: translateX(0%); }
    100% { transform: translateX(-100%); }
}

td {
    word-wrap: break-word;
    word-break: break-all;
}

/**背景**/
body {
    background: rgb(255 255 255 / 50%);
    font-size: 13px;
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
}

.no_transition,
.no_transition * {
    transition: none !important;
}

.def_background_wrap {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    background: var(--body-bg);
}

.def_background {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
}

.def_background.default {}

.def_background.motion {
    left: -10px;
    top: -10px;
    width: calc(100% + 20px);
    height: calc(100% + 20px);
    animation: bg-motion 10s linear infinite;
}

.def_background_pattern {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    mix-blend-mode: overlay;
    background: center repeat;
    background-size: 420px auto;
    background-image: var(--pattern-url, none);
    opacity: var(--pattern-intensity, 1);
}

.def_background_pattern.default {
    opacity: 0.3;
    background-image: url('bg/pattern.svg');
}

html.def_background,
body.def_background {
    -webkit-mask: center repeat;
    -webkit-mask-size: 420px auto;
    -webkit-mask-image: var(--pattern-url, none);
    opacity: var(--pattern-intensity, 1);
}

html.def_background.default,
body.def_background.default {
    opacity: 0.3;
    -webkit-mask-image: url('bg/pattern.svg');
}

body.bg_light .def_background,
body.bg_light .def_background.default {
    -webkit-mask: none;
    opacity: 1;
}

html.def_background_pattern,
body.def_background_pattern {
    display: none;
}

body.bg_light .def_background_pattern {
    display: block;
}

@-webkit-keyframes bg-motion {
    20% {
        transform: translateX(0px);
    }

    25% {
        transform: translateX(-10px);
    }

    35% {
        transform: translateX(10px);
    }

    40% {
        transform: translateX(0px);
    }
}

@keyframes bg-motion {
    20% {
        transform: translateX(0px);
    }

    25% {
        transform: translateX(-10px);
    }

    35% {
        transform: translateX(10px);
    }

    40% {
        transform: translateX(0px);
    }
}

.content {
    font-size: 13px !important;
}

* {
    border: 0px !important;
}

a {
    color: #4caf50;
    text-decoration: none;
}

.ui.card>:only-child,
.ui.cards>.card>:only-child,
.ui.card>:last-child,
.ui.cards>.card>:last-child,
.ui.card>:first-child,
.ui.cards>.card>:first-child {
    border-radius: 0px !important;
}

/**进度条**/
.ui.fine.progress>.bar,
.ui.progress.fine .bar {
    background-color: #03a9f4 !important;
}
.ui.progress.warning .bar {
    background-color: #ff9800 !important;
}
.ui.progress.error .bar {
    background-color: #e41e10 !important;
}
.ui.progress>.bar,
.ui.progress.offline .bar {
    background-color: slategrey;
    border-radius: 15px;
    line-height: 1.65em;
    min-width: 1.75em !important;
    text-align: right;
    padding-right: 0.4em;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 700;
    max-width: 100% !important;
}

/**未登录提示框**/
.ui.message {
    border: 0px solid transparent;
    border-radius: 0;
    background: rgb(255 255 255 / 18%);
}

/**后台文本域**/
.ui.form select {
    background: rgb(249 250 251 / 47%) !important;
}

.ui.form textarea {
    background: rgb(249 250 251 / 47%) !important;
}

.ui.checkbox input:checked~.box:before,
.ui.checkbox input:checked~label:before {
    background: rgb(249 250 251 / 47%);
    border-color: rgba(34, 36, 38, .35);
}

.ui.checkbox input:checked~.box:after,
.ui.checkbox input:checked~label:after {
    background: rgb(249 250 251 / 47%);
}

.ui.form textarea:focus {
    background: rgb(249 250 251 / 47%) !important;
}

.ui.segment {
    background: rgb(255 255 255 / 55%) !important;
}

.ui.form input:not([type]),
.ui.form input[type=date],
.ui.form input[type=datetime-local],
.ui.form input[type=email],
.ui.form input[type=file],
.ui.form input[type=number],
.ui.form input[type=password],
.ui.form input[type=search],
.ui.form input[type=tel],
.ui.form input[type=text],
.ui.form input[type=time],
.ui.form input[type=url] {
    background: #ffffff45 !important;
}

/**信息卡片透明**/
.ui.four.cards>.card {
    /*margin: 0.5em!important;*/
    position: relative;
    border-radius: 10px;
    display: inline-block;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: #fafafaa3;
    -webkit-transition: all .3s ease;
    transition: all .3s ease;
    -webkit-box-shadow: 0 1px 4px 0 rgb(0 0 0 / 20%);
    box-shadow: 0 1px 4px 0 rgb(0 0 0 / 20%);
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

/**信息弹框透明**/
.status.cards .ui.content.popup {
    border-radius: 5px;
    border: 1px solid transparent;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    inset: 37.1562px auto auto 220.82px;
    background-color: #fafafaeb;
    -webkit-transition: all .3s ease;
    transition: all .3s ease;
    -webkit-box-shadow: 0 1px 4px 0 rgb(0 0 0 / 20%);
    box-shadow: 0 1px 4px 0 rgb(0 0 0 / 20%);
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    min-width: 240px;
    max-width: 90vw;
    padding: 10px;
    z-index: 1000;
    max-height: 80vh;
}

.ui.content.popup.visible {
    display: block !important;
}

/**信息透明弹框边宽**/
.ui.content {
    margin: 0;
    padding: 1em !important;
}

/**信息透明弹框边小箭头位置**/
.ui.right.center.popup {
    margin: -3px 0 0 0.914286em !important;
    -webkit-transform-origin: left 50%;
    transform-origin: left 50%;
}

.ui.bottom.left.popup {
    margin-left: 1px !important;
    margin-top: 3px !important;
}

.ui.top.left.popup {
    margin-left: 0;
    margin-bottom: 10px !important;
    inset: 37.1562px auto auto 220.82px;
}

.ui.top.right.popup {
    margin-right: 0;
    margin-bottom: 8px !important;
}

.ui.left.center.popup {
    margin: -3px .91428571em 0 0 !important;
    -webkit-transform-origin: right 50%;
    transform-origin: right 50%;
}

.ui.green.button,
.ui.green.buttons .button {
    background-color: #21ba45;
    color: #1b1c1d;
}

i.icon {
    width: 1em !important;
}

/**后台文本信息可选可复制**/
.ui.basic.table {
    user-select: text;
}

.ui.dimmer {
    background-color: rgb(34 36 38 / 15%);
}

/**后台服务器编辑区**/
.ui.modal>.content {
    background: #e1dae8b0 !important;
}

.ui.modal>.actions {
    background: #f9fafb78 !important;
}

.ui.modal>.header {
    background: #f9fafb78 !important;
}

.ui.modal {
    background: #ffffffa6 !important;
}

.column {
    margin-top: 2px;
}

.ui.progress {
    border-radius: 15px;
}

.ui.progress .bar {
    border-radius: 15px;
}

/**默认进度条样式**/
.ui.card>.content>.description,
.ui.cards>.card>.content>.description {
    color: #767676;
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
}

.ui.teal.button,
.ui.teal.buttons .button {
    background-color: #ff9800;
}

/**页头样式**/
.ui.large.menu {
    border: 0;
    border-radius: 0px;
    background-color: rgba(255, 255, 255, 55%);
}

/**下拉菜单样式**/
.ui.dropdown .menu {
    border: 0;
    border-radius: 0px;
    background-color: rgba(255, 255, 255, 55%);
}

/**页脚样式**/
.ui.inverted.segment,
.ui.primary.inverted.segment {
    width: 100%;
    color: #8b6cc1f7;
    background-color: rgba(255, 255, 255, 55%);
    position: fixed;
    bottom: 0;
}

.ui.right.center.popup:before,
.ui.left.center.popup:before {
    border: 0px solid #fafafaeb !important;
    background: #fafafaeb !important;
}

.ui.top.popup:before {
    border-color: #fafafaeb transparent transparent;
}

.ui.popup:before {
    border-color: #fafafaeb transparent transparent;
}

.ui.bottom.left.popup:before {
    border-radius: 0px;
    border: 1px solid transparent;
    border-color: #fafafaeb transparent transparent;
    background: #fafafaeb;
    -webkit-box-shadow: 0px 0px 0 0 #fafafaeb;
    box-shadow: 0px 0px 0 0 #fafafaeb;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.ui.bottom.right.popup:before {
    border-radius: 0px;
    border: 1px solid transparent;
    border-color: #fafafaeb transparent transparent;
    background: #fafafaeb;
    -webkit-box-shadow: 0px 0px 0 0 #fafafaeb;
    box-shadow: 0px 0px 0 0 #fafafaeb;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.ui.top.left.popup:before {
    border-radius: 0px;
    border: 1px solid transparent;
    border-color: #fafafaeb transparent transparent;
    background: #fafafaeb;
    -webkit-box-shadow: 0px 0px 0 0 #fafafaeb;
    box-shadow: 0px 0px 0 0 #fafafaeb;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.ui.top.right.popup:before {
    border-radius: 0px;
    border: 1px solid transparent;
    border-color: #fafafaeb transparent transparent;
    background: #fafafaeb;
    -webkit-box-shadow: 0px 0px 0 0 #fafafaeb;
    box-shadow: 0px 0px 0 0 #fafafaeb;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.ui.left.center.popup:before {
    border-radius: 0px;
    border: 1px solid transparent;
    border-color: #fafafaeb transparent transparent;
    background: #fafafaeb;
    -webkit-box-shadow: 0px 0px 0 0 #fafafaeb;
    box-shadow: 0px 0px 0 0 #fafafaeb;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

/**系统图标颜色**/
i.os-centos, i.fl-centos { color: #9c27b0 !important; }
i.os-ubuntu, i.fl-ubuntu { color: #ff5722 !important; }
i.os-debian, i.fl-debian { color: #e91e63 !important; }
i.os-freebsd, i.fl-freebsd { color: #f44336 !important; }
i.os-apple, i.fl-apple { color: #9e9e9e !important; }
i.os-fedora, i.fl-fedora { color: #2196f3 !important; }
i.os-redhat, i.fl-redhat { color: #f44336 !important; }
i.os-deepin, i.fl-deepin { color: #03a9f4 !important; }
i.os-coreos, i.fl-coreos { color: #2185d0 !important; }
i.os-archlinux, i.fl-archlinux { color: #2185d0 !important; }
i.os-alpine, i.fl-alpine { color: #176eaf !important; }
i.os-win10, i.fl-win10, i.windows.icon { color: #03a9f4 !important; }
i.os-tux, i.fl-tux { color: #1b1c1d !important; }
i.os-opensuse, i.fl-opensuse { color: #73ba25 !important; }

/* 合并箭头和 up/down 图标色 */
i.arrow.alternate.circle.down.outline.icon,
i.fa-solid.fa-down,
i.fa-regular.fa-down { color: green; }
i.arrow.alternate.circle.up.outline.icon,
i.fa-solid.fa-up,
i.fa-regular.fa-up { color: #ff5722; }

i.yellow.icon {
    color: #ff9800 !important;
}

i.clock.icon {
    color: #f2711c;
}

i.server.icon {
    color: #079a0d !important;
}

i.rss.icon {
    color: #ffc107 !important;
}

i.bell.icon {
    color: red !important;
}

i.setting.icon {
    color: #2196f3 !important;
}

i.icon.area.chart {
    color: #21ba45 !important;
}

i.logout.icon {
    color: #ef1505 !important;
}

i.copy.icon {
    color: #FF9800 !important;
}

i.edit.icon {
    color: #21ba45 !important;
}

i.sellsy.icon {
    color: #9c27b0 !important;
}

i.heartbeat.icon {
    color: #fa1605 !important;
}

i.globe.icon {
    color: #03a9f4 !important;
}

i.home.icon {
    color: #03a9f4 !important;
}

i.trash.icon {
    color: #f30606 !important;
}

i.add.icon {
    color: #03a9f4 !important;
}

i.trash.alternate.outline.icon {
    color: #f30606 !important;
}

i.terminal.icon {
    color: #03a9f4 !important;
}

i.bullseye.icon {
    color: #9c27b0 !important;
}

i.buromobelexperte.icon {
    color: #03a9f4 !important;
}

i.exchange.icon {
    color: #f2711c !important;
}

i.th.icon {
    color: #03a9f4 !important;
}

i.key.icon {
    color: #f88400 !important;
}

i.fa-solid.fa-microchip {
    color: #03a9f4 !important;
}

i.fa-solid.fa-memory {
    color: #f2711c !important;
}

i.fa-solid.fa-hard-drive {
    color: #9c27b0 !important;
}

i.fa-solid.fa-sliders.icon {
    color: #2196f3 !important;
}

i.fa-regular.fa-bars-progress {
    color: #6435c9 !important;
}

i.fa-solid.fa-circle-exclamation {
    color: #f2711c !important;
}

i.icon.outline {
    color: #ff5722 !important;
}

i.clipboard.icon {
    color: #21ba45 !important;
}

i.note.icon {
    color: #03a9f4 !important;
}

.nb-container {
    padding-top: 75px;
    min-height: 100vh;
    padding-bottom: 55px;
    margin-bottom: -47px;
}

#app .ui.fluid.accordion {
    border-radius: 19px;
    margin-bottom: 1rem;
    background-color: #fbfbfb26;
    -webkit-transition: all .3s ease;
    transition: all .3s ease;
    -webkit-box-shadow: 0 1px 4px 0 rgb(0 0 0 / 20%);
    box-shadow: 0 1px 4px 0 rgb(0 0 0 / 20%);
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.login.nb-container {
    display: flex;
    align-items: center;
    padding-top: unset;
}

.login.nb-container>.grid {
    width: 100%;
    margin: 0 auto;
}

.login.nb-container>.grid .column {
    max-width: 450px;
}

.status.cards .flag {
    margin-right: 0 !important;
}

.status.cards .header>.info.icon {
    float: right;
    margin-right: 0;
    cursor: pointer;
}

.status.cards .wide.column {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    height: 2rem !important;
}

.status.cards .three.wide.column {
    padding-right: 0 !important;
}

.status.cards .wide.column:nth-child(1) {
    margin-top: 0.5rem !important;
}

.status.cards .wide.column:nth-child(2) {
    margin-top: 0.5rem !important;
}

.ui.grid {
    margin-bottom: -0.3rem!important;
}

.status.cards .outline.icon {
    margin-right: -2px;
}

i.s {
    margin-right: 2px !important;
}

.ui.progress .bar {
    min-width: 1.75em !important;
    text-align: right;
    padding-right: 0.4em;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 700;
    max-width: 100% !important;
}

.network-primary-btn {
    background-color: rgb(255 255 255 / 50%) !important;
}

.server-primary-btn {
    background-color: #f88400 !important;
    color: white !important;
}

.server-primary-font {
    color: #f88400 !important;
}

.server-secondary-font {
    color: rgba(252, 166, 7, 0.952) !important;
}

/** 流量页样式**/
.ui.table {
    font-size: 1em;
    position: relative;
    border-radius: 5px 5px 10px 10px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: #fafafa80;
    -webkit-transition: all .3s ease;
    transition: all .3s ease;
    -webkit-box-shadow: 0 1px 4px 0 rgb(0 0 0 / 20%);
    box-shadow: 0 1px 4px 0 rgb(0 0 0 / 20%);
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.ui.table [class*="center aligned"],
.ui.table[class*="center aligned"] {
    background-color: rgb(255 255 255 / 5%);
}

.ui.button {
    background: rgb(250 250 250 / 75%) none;
}

.ui.menu .item:before {
    width: 0px !important;
}

/* 性能优化CSS - 减少非被动事件监听器警告 (from home.html) */
html, body {
    touch-action: manipulation; /* 优化触摸事件性能 */
    -webkit-overflow-scrolling: touch; /* iOS设备滚动优化 */
}

/* 卡片容器触摸优化 */
.ui.card {
    touch-action: manipulation;
}

/* 标签页容器触摸优化 */
.custom-tabs-container {
    touch-action: pan-x; /* 只允许水平滚动 */
}

/* 弹窗触摸优化 */
.ui.popup {
    touch-action: manipulation;
}

/* 折叠面板触摸优化 */
.ui.accordion {
    touch-action: manipulation;
}

/* 网络监控页面性能优化CSS (from network.html) */
.ui.container {
    contain: layout style;
}

.service-status {
    will-change: transform;
    contain: layout style;
}

.server-buttons-container {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-bottom: 15px;
}

.server-buttons-container .network-primary-btn {
    flex: 0 0 auto;
    margin: 0 !important;
}

.network-primary-btn {
    will-change: auto;
    backface-visibility: hidden;
    transform: translateZ(0);
    padding: 0.6em !important;
}

/* 减少重绘 */
[ref="chartDom"] {
    contain: layout style; /* 改为较宽松的contain，避免strict影响滚动 */
    will-change: auto;
    transform: scale(0.85); /* 整体缩小到85% */
    transform-origin: top center; /* 从顶部中心开始缩放 */
    margin-bottom: -60px; /* 调整底部边距补偿缩放后的空间 */
    touch-action: pan-y; /* 允许垂直滚动 */
}

/* 响应式设计：移动设备上进一步缩小 */
@media (max-width: 768px) {
    [ref="chartDom"] {
        transform: scale(0.75); /* 移动设备缩小到75% */
        margin-bottom: -80px;
        touch-action: manipulation; /* 允许基本触摸操作 */
        overflow: visible;
    }
    
    /* 温和的移动端滚动优化 */
    html, body {
        -webkit-overflow-scrolling: touch;
        overflow-y: auto;
    }
    
    /* 确保容器不干扰滚动 */
    .nb-container {
        overflow: visible;
        position: relative;
    }
}

/* 字体加载优化 */
.flag-icon {
    font-display: swap;
}

.fl-alpine, .fl-archlinux, .fl-centos, .fl-debian, .fl-fedora, .fl-tux, .fl-ubuntu {
    font-display: swap;
}

.ui-alerts{position:fixed;z-index:2060;padding:23px}.ui-alerts.center{top:50%;left:50%;margin-top:-100px;margin-left:-222px}.ui-alerts.top-right{top:20px;right:20px}.ui-alerts.top-center{top:20px;margin-left:-222px;left:50%}.ui-alerts.top-left{top:20px;left:20px}.ui-alerts.bottom-right{bottom:0;right:20px}.ui-alerts.bottom-center{bottom:0;margin-left:-222px;left:50%}.ui-alerts.bottom-left{bottom:0;left:20px}.ui-alerts.ui-alerts>.message>.content>.header{padding-right:13px}@media (min-width:320px){.ui-alerts.top-center{margin-left:-163px}}
