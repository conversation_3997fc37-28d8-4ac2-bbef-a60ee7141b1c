name: Release

on:
  # workflow_dispatch:
  push:
    tags:
      - "v*"

jobs:
  build:
    strategy:
      fail-fast: true
      matrix:
        goos: [linux, windows]
        goarch: [amd64]
        include:
          - goos: linux
            goarch: s390x
          - goos: linux
            goarch: arm64
            
    name: Build artifacts
    runs-on: ubuntu-latest
    container:
      image: goreleaser/goreleaser-cross:v1.23
    env:
      GOOS: ${{ matrix.goos }}
      GOARCH: ${{ matrix.goarch }}
      GOARM: ${{ matrix.goarm }}
    outputs:
      tag_name: ${{ steps.extract_branch.outputs.tag }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Ensure full repository is checked out

      - run: git config --global --add safe.directory /__w/ServerStatus/ServerStatus
      
      - name: Set working directory
        run: cd /__w/ServerStatus/ServerStatus

      - name: Fetch IPInfo GeoIP Database
        if: github.event_name == 'push' && contains(github.ref, 'refs/tags/')
        env:
          IPINFO_TOKEN: ${{ secrets.IPINFO_TOKEN }}
        run: |
          rm pkg/geoip/geoip.db
          wget -qO pkg/geoip/geoip.db https://ipinfo.io/data/free/country.mmdb?token=${IPINFO_TOKEN}

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.23.x"

      - name: Build
        uses: goreleaser/goreleaser-action@v6
        with:
          distribution: goreleaser
          version: "~> v2"
          args: build --single-target --clean --skip=validate

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: server-dash-${{ env.GOOS }}-${{ env.GOARCH }}
          path: |
            ./dist/*/*

  release:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && contains(github.ref, 'refs/tags/')
    needs: build
    name: Release
    steps:
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          path: ./assets

      - name: Archive and compress binaries
        run: |
          find assets/*/*/* -type f | while read -r file; do
            chmod +x $file
            dir=$(dirname "$file")
            filename=$(basename "$file")
            fileWithoutExt="${filename%.*}"
            zip -jr "$dir/$fileWithoutExt.zip" "$file"
          done

      - name: Release
        uses: ncipollo/release-action@v1
        with:
          artifacts: "assets/*/*/*.zip"
          generateReleaseNotes: true


  release-docker:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && contains(github.ref, 'refs/tags/')
    needs: build
    name: Release Docker images
    steps:
      - uses: actions/checkout@v4
        
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          path: ./assets

      - name: Fix permissions
        run: |
          chmod -R +x ./assets/*
          mkdir dist
          mv ./assets/*/*/* ./dist

      - name: Extract branch name
        run: |
          export TAG_NAME=$(echo ${GITHUB_REF#refs/tags/})
          echo "tag=$TAG_NAME" >> $GITHUB_OUTPUT
        id: extract_branch
         
      - name: Log into GHCR
        uses: docker/login-action@master
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ github.token }}

      - name: Log in to the AliyunCS
        uses: docker/login-action@master
        with:
          registry: registry.cn-shanghai.aliyuncs.com
          username: ${{ secrets.ALI_USER }}
          password: ${{ secrets.ALI_PAT }}

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Set up image name
        run: |
          GHCR_IMAGE_NAME=$(echo "ghcr.io/${{ github.repository_owner }}/server-dash" | tr '[:upper:]' '[:lower:]')
          if [ ${{ github.repository_owner }} = "xos" ]
            then ALI_IMAGE_NAME=$(echo "registry.cn-shanghai.aliyuncs.com/dns/server-dash")
            else ALI_IMAGE_NAME=$(echo "registry.cn-shanghai.aliyuncs.com/${{ github.repository_owner }}/server-dash" | tr '[:upper:]' '[:lower:]')
          fi
          echo "GHCR_IMAGE_NAME=$GHCR_IMAGE_NAME" >> $GITHUB_OUTPUT
          echo "ALI_IMAGE_NAME=$ALI_IMAGE_NAME" >> $GITHUB_OUTPUT
        id: image-name

      - name: Build dasbboard image And Push
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          platforms: linux/amd64,linux/arm64,linux/s390x
          push: true
          tags: |
            ${{ steps.image-name.outputs.GHCR_IMAGE_NAME }}:latest
            ${{ steps.image-name.outputs.GHCR_IMAGE_NAME }}:${{ steps.extract_branch.outputs.tag }}
            ${{ steps.image-name.outputs.ALI_IMAGE_NAME }}:latest
            ${{ steps.image-name.outputs.ALI_IMAGE_NAME }}:${{ steps.extract_branch.outputs.tag }}
      - name: Trigger sync
        env:
          GH_REPO: ${{ github.repository }}
          GH_TOKEN: ${{ github.token }}
          GH_DEBUG: api
        run: |
          gh workflow run sync-release.yml
