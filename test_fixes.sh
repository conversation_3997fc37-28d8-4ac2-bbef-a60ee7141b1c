#!/bin/bash

echo "🔧 测试ServerStatus修复效果"
echo "================================"

# 测试1: 检查OPTIONS请求是否正确处理
echo "📋 测试1: 检查OPTIONS请求处理"
echo "发送OPTIONS请求到 /api/search-server..."

# 使用curl测试OPTIONS请求
response=$(curl -s -o /dev/null -w "%{http_code}" -X OPTIONS "http://localhost:1001/api/search-server?word=" \
  -H "Access-Control-Request-Method: GET" \
  -H "Access-Control-Request-Headers: Content-Type")

if [ "$response" = "200" ]; then
    echo "✅ OPTIONS请求处理正常 (HTTP $response)"
else
    echo "❌ OPTIONS请求处理异常 (HTTP $response)"
fi

echo ""

# 测试2: 检查CORS头是否正确设置
echo "📋 测试2: 检查CORS头设置"
echo "检查CORS响应头..."

cors_headers=$(curl -s -I -X OPTIONS "http://localhost:1001/api/search-server?word=" | grep -i "access-control")

if [ -n "$cors_headers" ]; then
    echo "✅ CORS头设置正常:"
    echo "$cors_headers"
else
    echo "❌ CORS头设置异常"
fi

echo ""

# 测试3: 检查正常GET请求是否仍然工作
echo "📋 测试3: 检查正常API请求"
echo "测试GET请求到 /api/search-server..."

# 注意：这个请求可能需要认证，所以可能返回403，但不应该是404
get_response=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:1001/api/search-server?word=")

if [ "$get_response" = "200" ] || [ "$get_response" = "403" ]; then
    echo "✅ GET请求路由正常 (HTTP $get_response)"
else
    echo "❌ GET请求路由异常 (HTTP $get_response)"
fi

echo ""

# 测试4: 检查BadgerDB缓存警告是否被过滤
echo "📋 测试4: 检查BadgerDB日志过滤"
echo "检查最近的日志中是否还有BadgerDB缓存警告..."

if [ -f "issues.txt" ]; then
    # 检查最近50行日志中是否还有缓存警告
    recent_warnings=$(tail -50 issues.txt | grep -i "block cache might be too small" | wc -l)

    if [ "$recent_warnings" -eq 0 ]; then
        echo "✅ BadgerDB缓存警告已被成功过滤"
    else
        echo "❌ BadgerDB缓存警告仍然存在 ($recent_warnings 条)"
    fi
else
    echo "⚠️  无法找到 issues.txt 日志文件"
fi

echo ""
echo "🎯 修复效果总结:"
echo "1. OPTIONS请求处理: $([ "$response" = "200" ] && echo "✅ 正常" || echo "❌ 异常")"
echo "2. CORS头设置: $([ -n "$cors_headers" ] && echo "✅ 正常" || echo "❌ 异常")"
echo "3. API路由功能: $([ "$get_response" = "200" ] || [ "$get_response" = "403" ] && echo "✅ 正常" || echo "❌ 异常")"
echo "4. BadgerDB警告过滤: $([ "$recent_warnings" -eq 0 ] && echo "✅ 正常" || echo "❌ 异常")"
echo ""
echo "💡 提示: 如果服务器未运行，请先启动 ./server-dash"
