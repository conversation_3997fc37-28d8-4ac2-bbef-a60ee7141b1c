// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v5.26.1
// source: proto/server.proto

package proto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ServerService_ReportSystemState_FullMethodName = "/proto.ServerService/ReportSystemState"
	ServerService_ReportSystemInfo_FullMethodName  = "/proto.ServerService/ReportSystemInfo"
	ServerService_ReportTask_FullMethodName        = "/proto.ServerService/ReportTask"
	ServerService_RequestTask_FullMethodName       = "/proto.ServerService/RequestTask"
	ServerService_IOStream_FullMethodName          = "/proto.ServerService/IOStream"
	ServerService_LookupGeoIP_FullMethodName       = "/proto.ServerService/LookupGeoIP"
)

// ServerServiceClient is the client API for ServerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ServerServiceClient interface {
	ReportSystemState(ctx context.Context, in *State, opts ...grpc.CallOption) (*Receipt, error)
	ReportSystemInfo(ctx context.Context, in *Host, opts ...grpc.CallOption) (*Receipt, error)
	ReportTask(ctx context.Context, in *TaskResult, opts ...grpc.CallOption) (*Receipt, error)
	RequestTask(ctx context.Context, in *Host, opts ...grpc.CallOption) (ServerService_RequestTaskClient, error)
	IOStream(ctx context.Context, opts ...grpc.CallOption) (ServerService_IOStreamClient, error)
	LookupGeoIP(ctx context.Context, in *GeoIP, opts ...grpc.CallOption) (*GeoIP, error)
}

type serverServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewServerServiceClient(cc grpc.ClientConnInterface) ServerServiceClient {
	return &serverServiceClient{cc}
}

func (c *serverServiceClient) ReportSystemState(ctx context.Context, in *State, opts ...grpc.CallOption) (*Receipt, error) {
	out := new(Receipt)
	err := c.cc.Invoke(ctx, ServerService_ReportSystemState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) ReportSystemInfo(ctx context.Context, in *Host, opts ...grpc.CallOption) (*Receipt, error) {
	out := new(Receipt)
	err := c.cc.Invoke(ctx, ServerService_ReportSystemInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) ReportTask(ctx context.Context, in *TaskResult, opts ...grpc.CallOption) (*Receipt, error) {
	out := new(Receipt)
	err := c.cc.Invoke(ctx, ServerService_ReportTask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverServiceClient) RequestTask(ctx context.Context, in *Host, opts ...grpc.CallOption) (ServerService_RequestTaskClient, error) {
	stream, err := c.cc.NewStream(ctx, &ServerService_ServiceDesc.Streams[0], ServerService_RequestTask_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &serverServiceRequestTaskClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type ServerService_RequestTaskClient interface {
	Recv() (*Task, error)
	grpc.ClientStream
}

type serverServiceRequestTaskClient struct {
	grpc.ClientStream
}

func (x *serverServiceRequestTaskClient) Recv() (*Task, error) {
	m := new(Task)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *serverServiceClient) IOStream(ctx context.Context, opts ...grpc.CallOption) (ServerService_IOStreamClient, error) {
	stream, err := c.cc.NewStream(ctx, &ServerService_ServiceDesc.Streams[1], ServerService_IOStream_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &serverServiceIOStreamClient{stream}
	return x, nil
}

type ServerService_IOStreamClient interface {
	Send(*IOStreamData) error
	Recv() (*IOStreamData, error)
	grpc.ClientStream
}

type serverServiceIOStreamClient struct {
	grpc.ClientStream
}

func (x *serverServiceIOStreamClient) Send(m *IOStreamData) error {
	return x.ClientStream.SendMsg(m)
}

func (x *serverServiceIOStreamClient) Recv() (*IOStreamData, error) {
	m := new(IOStreamData)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *serverServiceClient) LookupGeoIP(ctx context.Context, in *GeoIP, opts ...grpc.CallOption) (*GeoIP, error) {
	out := new(GeoIP)
	err := c.cc.Invoke(ctx, ServerService_LookupGeoIP_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServerServiceServer is the server API for ServerService service.
// All implementations should embed UnimplementedServerServiceServer
// for forward compatibility
type ServerServiceServer interface {
	ReportSystemState(context.Context, *State) (*Receipt, error)
	ReportSystemInfo(context.Context, *Host) (*Receipt, error)
	ReportTask(context.Context, *TaskResult) (*Receipt, error)
	RequestTask(*Host, ServerService_RequestTaskServer) error
	IOStream(ServerService_IOStreamServer) error
	LookupGeoIP(context.Context, *GeoIP) (*GeoIP, error)
}

// UnimplementedServerServiceServer should be embedded to have forward compatible implementations.
type UnimplementedServerServiceServer struct {
}

func (UnimplementedServerServiceServer) ReportSystemState(context.Context, *State) (*Receipt, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportSystemState not implemented")
}
func (UnimplementedServerServiceServer) ReportSystemInfo(context.Context, *Host) (*Receipt, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportSystemInfo not implemented")
}
func (UnimplementedServerServiceServer) ReportTask(context.Context, *TaskResult) (*Receipt, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportTask not implemented")
}
func (UnimplementedServerServiceServer) RequestTask(*Host, ServerService_RequestTaskServer) error {
	return status.Errorf(codes.Unimplemented, "method RequestTask not implemented")
}
func (UnimplementedServerServiceServer) IOStream(ServerService_IOStreamServer) error {
	return status.Errorf(codes.Unimplemented, "method IOStream not implemented")
}
func (UnimplementedServerServiceServer) LookupGeoIP(context.Context, *GeoIP) (*GeoIP, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LookupGeoIP not implemented")
}

// UnsafeServerServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServerServiceServer will
// result in compilation errors.
type UnsafeServerServiceServer interface {
	mustEmbedUnimplementedServerServiceServer()
}

func RegisterServerServiceServer(s grpc.ServiceRegistrar, srv ServerServiceServer) {
	s.RegisterService(&ServerService_ServiceDesc, srv)
}

func _ServerService_ReportSystemState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(State)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).ReportSystemState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_ReportSystemState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).ReportSystemState(ctx, req.(*State))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_ReportSystemInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Host)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).ReportSystemInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_ReportSystemInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).ReportSystemInfo(ctx, req.(*Host))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_ReportTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskResult)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).ReportTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_ReportTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).ReportTask(ctx, req.(*TaskResult))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerService_RequestTask_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(Host)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(ServerServiceServer).RequestTask(m, &serverServiceRequestTaskServer{stream})
}

type ServerService_RequestTaskServer interface {
	Send(*Task) error
	grpc.ServerStream
}

type serverServiceRequestTaskServer struct {
	grpc.ServerStream
}

func (x *serverServiceRequestTaskServer) Send(m *Task) error {
	return x.ServerStream.SendMsg(m)
}

func _ServerService_IOStream_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(ServerServiceServer).IOStream(&serverServiceIOStreamServer{stream})
}

type ServerService_IOStreamServer interface {
	Send(*IOStreamData) error
	Recv() (*IOStreamData, error)
	grpc.ServerStream
}

type serverServiceIOStreamServer struct {
	grpc.ServerStream
}

func (x *serverServiceIOStreamServer) Send(m *IOStreamData) error {
	return x.ServerStream.SendMsg(m)
}

func (x *serverServiceIOStreamServer) Recv() (*IOStreamData, error) {
	m := new(IOStreamData)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _ServerService_LookupGeoIP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GeoIP)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerServiceServer).LookupGeoIP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ServerService_LookupGeoIP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerServiceServer).LookupGeoIP(ctx, req.(*GeoIP))
	}
	return interceptor(ctx, in, info, handler)
}

// ServerService_ServiceDesc is the grpc.ServiceDesc for ServerService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ServerService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "proto.ServerService",
	HandlerType: (*ServerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ReportSystemState",
			Handler:    _ServerService_ReportSystemState_Handler,
		},
		{
			MethodName: "ReportSystemInfo",
			Handler:    _ServerService_ReportSystemInfo_Handler,
		},
		{
			MethodName: "ReportTask",
			Handler:    _ServerService_ReportTask_Handler,
		},
		{
			MethodName: "LookupGeoIP",
			Handler:    _ServerService_LookupGeoIP_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "RequestTask",
			Handler:       _ServerService_RequestTask_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "IOStream",
			Handler:       _ServerService_IOStream_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "proto/server.proto",
}
