package controller

import (
	"net/http"
	"runtime"

	"github.com/gin-gonic/gin"
	"github.com/xos/serverstatus/service/rpc"
)

// SystemStatusAPI 系统状态API
func SystemStatusAPI(c *gin.Context) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// 获取基本统计信息
	goroutines := runtime.NumGoroutine()

	// 获取连接统计
	activeConns, maxConns, _ := rpc.GetConnectionStats()

	// 基础状态信息
	stats := map[string]interface{}{
		"memory": map[string]interface{}{
			"alloc_mb":      m.Alloc / 1024 / 1024,
			"sys_mb":        m.Sys / 1024 / 1024,
			"heap_alloc_mb": m.HeapAlloc / 1024 / 1024,
			"heap_sys_mb":   m.HeapSys / 1024 / 1024,
		},
		"goroutines": map[string]interface{}{
			"total":        goroutines,
			"target_limit": 250,
		},
		"connections": map[string]interface{}{
			"active": activeConns,
			"max":    maxConns,
		},
		"system": map[string]interface{}{
			"num_cpu":    runtime.NumCPU(),
			"gomaxprocs": runtime.GOMAXPROCS(0),
			"go_version": runtime.Version(),
		},
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"msg":  "success",
		"data": stats,
	})
}
