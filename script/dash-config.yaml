debug: true
language: zh-CN
httpport: site_port
language: language
grpcport: grpc_port
grpchost: grpc_host
oauth2:
  type: "oauth2_type" #Oauth2 登录接入类型，Github/Gitlab/jihulab/Gitee
  admin: "admin_logins" #管理员列表，半角逗号隔开
  clientid: "github_oauth_client_id" # 在 https://github.com/settings/developers 创建，无需审核 Callback 填 http(s)://域名或IP/oauth2/callback
  clientsecret: "github_oauth_client_secret"
  endpoint: "" # 如gitea自建需要设置
site:
  brand: "site_title"
  cookiename: "server-dash" #浏览器 Cookie 字段名，可不改
  theme: "default"
ddns:
  enable: false
  provider: "webhook" # 如需使用多配置功能，请把此项留空
  accessid: ""
  accesssecret: ""
  webhookmethod: ""
  webhookurl: ""
  webhookrequestbody: ""
  webhookheaders: ""
  maxretries: 3
  profiles:
    example:
      provider: ""
      accessid: ""
      accesssecret: ""
      webhookmethod: ""
      webhookurl: ""
      webhookrequestbody: ""
      webhookheaders: ""    