# 客户端适配指南 - 短连接拉取模式

## 重要变更说明

ServerStatus 服务端已从长连接推送模式改为短连接拉取模式，客户端需要相应调整。

## 新的工作模式

### 原模式（已废弃）
```
客户端 -> 连接服务器 -> 保持长连接 -> 服务器推送任务 -> 执行任务
        ↑_______________________________|
```

### 新模式（推荐）
```
客户端 -> 定期连接服务器 -> 拉取任务 -> 断开连接 -> 执行任务 -> 等待下次拉取
```

## 客户端修改要点

### 1. RequestTask 调用方式
```go
// 旧方式 - 长连接（不再支持）
stream, err := client.RequestTask(ctx, &pb.Host{...})
for {
    task, err := stream.Recv()  // 阻塞等待推送
    // 处理任务
}

// 新方式 - 短连接拉取
func pullTasks() {
    stream, err := client.RequestTask(ctx, &pb.Host{...})
    if err != nil {
        return
    }
    
    // 立即读取所有任务
    var tasks []*pb.Task
    for {
        task, err := stream.Recv()
        if err == io.EOF {
            break  // 服务器主动关闭连接，任务拉取完成
        }
        if err != nil {
            log.Printf("拉取任务失败: %v", err)
            break
        }
        tasks = append(tasks, task)
    }
    
    // 处理所有任务
    for _, task := range tasks {
        processTask(task)
    }
}

// 定期拉取
func main() {
    ticker := time.NewTicker(30 * time.Second)  // 30秒拉取一次
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            pullTasks()
        }
    }
}
```

### 2. 拉取频率建议
- **正常监控**：30-60秒间隔
- **高频监控**：10-30秒间隔  
- **低频监控**：60-300秒间隔

⚠️ **不建议过高频率**（如每秒拉取），这会增加服务器压力

### 3. 错误处理
```go
func pullTasksWithRetry() {
    const maxRetries = 3
    for i := 0; i < maxRetries; i++ {
        if err := pullTasks(); err == nil {
            return  // 成功
        }
        time.Sleep(time.Second * time.Duration(i+1))  // 递增重试间隔
    }
    log.Printf("拉取任务失败，将在下次定时任务中重试")
}
```

### 4. 状态上报（无变化）
ReportSystemState 方法保持不变，客户端可以继续按原来的方式上报状态：
```go
_, err := client.ReportSystemState(ctx, &pb.State{...})
```

## 优势说明

### 1. 资源效率
- **服务器端**：不再维持大量长连接，内存和 goroutine 使用量大幅下降
- **客户端**：连接时间短，网络资源占用更少

### 2. 容错性
- 网络中断不会影响长期运行
- 服务器重启后客户端自动重连
- 任务不会因为连接问题丢失

### 3. 扩展性
- 服务器可以支持更多客户端
- 易于负载均衡和横向扩展

## 兼容性说明

### 完全兼容
- `ReportSystemState` - 状态上报接口不变
- `RequestTask` - 接口不变，但行为改变为短连接

### 行为变化
- `RequestTask` 不再阻塞，会立即返回所有任务并关闭连接
- 客户端需要主动实现定期拉取逻辑

### 迁移步骤
1. 更新客户端代码实现定期拉取模式
2. 测试新的拉取逻辑
3. 部署新的服务端
4. 部署新的客户端

## 示例实现

完整的客户端适配示例：

```go
type TaskClient struct {
    client pb.ServerServiceClient
    host   *pb.Host
    ticker *time.Ticker
}

func (c *TaskClient) Start() {
    c.ticker = time.NewTicker(30 * time.Second)
    go func() {
        defer c.ticker.Stop()
        for range c.ticker.C {
            c.pullAndExecuteTasks()
        }
    }()
}

func (c *TaskClient) pullAndExecuteTasks() {
    ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
    defer cancel()
    
    stream, err := c.client.RequestTask(ctx, c.host)
    if err != nil {
        log.Printf("连接失败: %v", err)
        return
    }
    
    for {
        task, err := stream.Recv()
        if err == io.EOF {
            break
        }
        if err != nil {
            log.Printf("接收任务失败: %v", err)
            break
        }
        
        go c.executeTask(task)  // 异步执行任务
    }
}

func (c *TaskClient) executeTask(task *pb.Task) {
    // 执行任务逻辑
    log.Printf("执行任务: %s", task.GetType())
}
```

通过这种方式，客户端可以平滑迁移到新的短连接拉取模式，享受更好的性能和稳定性。
