Jun 14 20:44:48 Cat server-dash[61609]:   - 额定流量: 100.00 GB
Jun 14 20:44:48 Cat server-dash[61609]:   - 剩余流量: 48.52 GB
Jun 14 20:44:48 Cat server-dash[61609]:   - 统计周期: 2025-06-01 00:00:00 - 2025-07-01 00:00:00
Jun 14 20:44:48 Cat server-dash[61609]: 2025/06/14 20:44:48 NG>> 通知方式组 [default] 包含 3 个通知方式
Jun 14 20:44:48 Cat server-dash[61609]: 2025/06/14 20:44:48 NG>> 尝试通知 TG[佩佩]
Jun 14 20:44:48 Cat server-dash[61609]: 2025/06/14 20:44:48 NG>> 尝试通知 TG[翠花]
Jun 14 20:44:48 Cat server-dash[61609]: 2025/06/14 20:44:48 NG>> 尝试通知 TG[老王]
Jun 14 20:44:48 Cat server-dash[61609]: 2025/06/14 20:44:48 NG>> 向  TG[佩佩]  发送通知成功：
Jun 14 20:44:49 Cat server-dash[61609]: 2025/06/14 20:44:49 NG>> 向  TG[翠花]  发送通知成功：
Jun 14 20:44:49 Cat server-dash[61609]: 2025/06/14 20:44:49 NG>> 向  TG[老王]  发送通知成功：
Jun 14 20:45:00 Cat server-dash[61609]: 2025/06/14 20:45:00 BadgerDB模式：流量数据一致性检查完成
Jun 14 20:45:00 Cat server-dash[61609]: 2025/06/14 20:45:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 14 20:45:00 Cat server-dash[61609]: 2025/06/14 20:45:00 BadgerDB: 成功保存 4 个用户的数据
Jun 14 20:45:00 Cat server-dash[61609]: 2025/06/14 20:45:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 14 20:45:00 Cat server-dash[61609]: 2025/06/14 20:45:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 14 20:45:46 Cat server-dash[61609]: panic: close of closed channel
Jun 14 20:45:46 Cat server-dash[61609]: goroutine 534335 [running]:
Jun 14 20:45:46 Cat server-dash[61609]: github.com/xos/serverstatus/service/rpc.(*ServerHandler).RequestTask.func4()
Jun 14 20:45:46 Cat server-dash[61609]:         github.com/xos/serverstatus/service/rpc/server.go:344 +0x2d
Jun 14 20:45:46 Cat server-dash[61609]: github.com/xos/serverstatus/service/rpc.(*ServerHandler).RequestTask(0xc007aaea08, 0x13be100?, {0x3562950, 0xc0023045b0})
Jun 14 20:45:46 Cat server-dash[61609]:         github.com/xos/serverstatus/service/rpc/server.go:394 +0xa73
Jun 14 20:45:46 Cat server-dash[61609]: github.com/xos/serverstatus/proto._ServerService_RequestTask_Handler({0x13be100, 0xc007aaea08}, {0x35618c8, 0xc00234a1c0})
Jun 14 20:45:46 Cat server-dash[61609]:         github.com/xos/serverstatus/proto/server_grpc.pb.go:254 +0x110
Jun 14 20:45:46 Cat server-dash[61609]: google.golang.org/grpc.(*Server).processStreamingRPC(0xc0000d3800, {0x355daa8, 0xc00d3ca180}, 0xc007a402a0, 0xc007a43380, 0x3db2f60, 0x0)
Jun 14 20:45:46 Cat server-dash[61609]:         google.golang.org/grpc@v1.72.0/server.go:1695 +0x1252
Jun 14 20:45:46 Cat server-dash[61609]: google.golang.org/grpc.(*Server).handleStream(0xc0000d3800, {0x355e588, 0xc00289a000}, 0xc007a402a0)
Jun 14 20:45:46 Cat server-dash[61609]:         google.golang.org/grpc@v1.72.0/server.go:1819 +0xb47
Jun 14 20:45:46 Cat server-dash[61609]: google.golang.org/grpc.(*Server).serveStreams.func2.1()
Jun 14 20:45:46 Cat server-dash[61609]:         google.golang.org/grpc@v1.72.0/server.go:1035 +0x7f
Jun 14 20:45:46 Cat server-dash[61609]: created by google.golang.org/grpc.(*Server).serveStreams.func2 in goroutine 546210
Jun 14 20:45:46 Cat server-dash[61609]:         google.golang.org/grpc@v1.72.0/server.go:1046 +0x11d
Jun 14 20:45:46 Cat systemd[1]: server-dash.service: Main process exited, code=exited, status=2/INVALIDARGUMENT
