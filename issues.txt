问题：
一、"/network" 页面不显示监测数据了；

前端输出:"=== parseMonitorInfo 开始 ===
network:549 接收到的monitorInfo: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
network:550 monitorInfo类型: object
network:551 是否为数组: true
network:561 使用直接数组格式
network:573 最终dataArray长度: 50
network:332 === 开始处理监控历史数据 ===
network:333 原始数据数量: 50
network:334 原始数据: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
network:360 处理记录 0: MonitorID=1, ServerID=8, UniqueKey=1_8
network:363 创建新的监控点分组: 1_8
network:360 处理记录 1: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 2: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 3: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 4: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 5: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 6: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 7: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 8: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 9: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 10: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 11: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 12: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 13: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 14: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 15: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 16: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 17: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 18: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 19: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 20: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 21: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 22: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 23: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 24: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 25: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 26: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 27: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 28: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 29: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 30: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 31: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 32: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 33: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 34: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 35: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 36: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 37: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 38: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 39: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 40: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 41: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 42: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 43: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 44: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 45: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 46: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 47: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 48: MonitorID=1, ServerID=8, UniqueKey=1_8
network:360 处理记录 49: MonitorID=1, ServerID=8, UniqueKey=1_8
network:534 获取监控名称 - ID: 1
network:535 监控配置: {1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}}
network:536 配置已加载: true
network:540 找到监控配置: 重庆电信
network:434 === 数据处理完成 ===
network:435 最终监控点数量: 1
network:436 监控点详情: [{…}]0: {monitor_id: 1, server_id: 8, monitor_name: '重庆电信', unique_key: '1_8', data_count: 50}length: 1[[Prototype]]: Array(0)
network:443 ===================
network:741 [Violation] 'requestAnimationFrame' handler took 77ms
content_script.js:1 [Violation] 'load' handler took 180ms
network:548 === parseMonitorInfo 开始 ===
network:549 接收到的monitorInfo: []
network:550 monitorInfo类型: object
network:551 是否为数组: true
network:561 使用直接数组格式
network:573 最终dataArray长度: 0
network:548 === parseMonitorInfo 开始 ===
network:549 接收到的monitorInfo: []
network:550 monitorInfo类型: object
network:551 是否为数组: true
network:561 使用直接数组格式
network:573 最终dataArray长度: 0
network:548 === parseMonitorInfo 开始 ===
network:549 接收到的monitorInfo: []
network:550 monitorInfo类型: object
network:551 是否为数组: true
network:561 使用直接数组格式
network:573 最终dataArray长度: 0
content_script.js:1 [Violation] 'load' handler took 214ms";

后端输出"Jun 19 17:28:24 Cat server-dash[64544]: 2025/06/19 17:28:24 network: 进入网络页面处理函数
Jun 19 17:28:24 Cat server-dash[64544]: 2025/06/19 17:28:24 network: 使用BadgerDB模式，跳过GORM查询监控历史
Jun 19 17:28:24 Cat server-dash[64544]: 2025/06/19 17:28:24 network: 从监控配置构建监控服务器ID列表
Jun 19 17:28:24 Cat server-dash[64544]: 2025/06/19 17:28:24 network: 找到 9 个监控配置
Jun 19 17:28:24 Cat server-dash[64544]: 2025/06/19 17:28:24 network: 处理监控配置 1: 重庆电信
Jun 19 17:28:24 Cat server-dash[64544]: 2025/06/19 17:28:24 network: 添加服务器 4 ([AC]香港) 到监控列表 (Cover=1)
Jun 19 17:28:24 Cat server-dash[64544]: 2025/06/19 17:28:24 network: 添加服务器 22 ([GC]盐湖城) 到监控列表 (Cover=1)
Jun 19 17:28:24 Cat server-dash[64544]: 2025/06/19 17:28:24 network: 添加服务器 9 ([MS]伦敦) 到监控列表 (Cover=1)
Jun 19 17:28:24 Cat server-dash[64544]: 2025/06/19 17:28:24 network: 添加服务器 14 ([OC]春川②) 到监控列表 (Cover=1)
Jun 19 17:28:24 Cat server-dash[64544]: 2025/06/19 17:28:24 network: 添加服务器 34 (北莱茵) 到监控列表 (Cover=1)
Jun 19 17:28:24 Cat server-dash[64544]: 2025/06/19 17:28:24 network: 添加服务器 40 ([HT]香港) 到监控列表 (Cover=1)
Jun 19 17:28:24 Cat server-dash[64544]: 2025/06/19 17:28:24 network: 添加服务器 41 ([CC]纽约) 到监控列表 (Cover=1)
Jun 19 17:28:24 Cat server-dash[64544]: 2025/06/19 17:28:24 network: 添加服务器 6 ([MS]香港) 到监控列表 (Cover=1)
Jun 19 17:28:24 Cat server-dash[64544]: 2025/06/19 17:28:24 network: 添加服务器 23 ([MG]伦敦) 到监控列表 (Cover=1)
Jun 19 17:28:24 Cat server-dash[64544]: 2025/06/19 17:28:24 network: 添加服务器 8 ([MS]圣何塞) 到监控列表 (Cover=1)
Jun 19 17:28:24 Cat server-dash[64544]: 2025/06/19 17:28:24 network: 添加服务器 43 ([LW]洛杉矶) 到监控列表 (Cover=1)
Jun 19 17:28:24 Cat server-dash[64544]: 2025/06/19 17:28:24 network: 添加服务器 5 ([MS]新加坡) 到监控列表 (Cover=1)
Jun 19 17:28:24 Cat server-dash[64544]: 2025/06/19 17:28:24 network: 处理监控配置 2: 重庆移动
Jun 19 17:28:24 Cat server-dash[64544]: 2025/06/19 17:28:24 network: 处理监控配置 3: 重庆联通
Jun 19 17:28:24 Cat server-dash[64544]: 2025/06/19 17:28:24 network: 处理监控配置 4: 上海联通
Jun 19 17:28:24 Cat server-dash[64544]: 2025/06/19 17:28:24 network: 处理监控配置 5: 上海移动
Jun 19 17:28:24 Cat server-dash[64544]: 2025/06/19 17:28:24 network: 处理监控配置 6: 上海电信
Jun 19 17:28:24 Cat server-dash[64544]: 2025/06/19 17:28:24 network: 处理监控配置 7: 河南联通
Jun 19 17:28:24 Cat server-dash[64544]: 2025/06/19 17:28:24 network: 处理监控配置 8: 河南移动
Jun 19 17:28:24 Cat server-dash[64544]: 2025/06/19 17:28:24 network: 处理监控配置 9: 河南电信
Jun 19 17:28:24 Cat server-dash[64544]: 2025/06/19 17:28:24 network: 从监控配置获取到 12 个有监控任务的服务器
Jun 19 17:28:24 Cat server-dash[64544]: 2025/06/19 17:28:24 network: BadgerDB模式，查询监控历史记录
Jun 19 17:28:28 Cat server-dash[64544]: 2025/06/19 17:28:28 network: 为服务器 8 找到 50 条ICMP/TCP监控历史记录
Jun 19 17:28:28 Cat server-dash[64544]: [GIN] 2025/06/19 - 17:28:28 | 200 |  4.215066956s |  39.144.187.158 | GET      "/network"
Jun 19 17:28:30 Cat server-dash[64544]: 2025/06/19 17:28:30 API返回监控配置数量: 9
Jun 19 17:28:30 Cat server-dash[64544]: 2025/06/19 17:28:30 监控配置: ID=1, Name=重庆电信, Type=2
Jun 19 17:28:30 Cat server-dash[64544]: 2025/06/19 17:28:30 监控配置: ID=2, Name=重庆移动, Type=2
Jun 19 17:28:30 Cat server-dash[64544]: 2025/06/19 17:28:30 监控配置: ID=3, Name=重庆联通, Type=2
Jun 19 17:28:30 Cat server-dash[64544]: 2025/06/19 17:28:30 监控配置: ID=4, Name=上海联通, Type=2
Jun 19 17:28:30 Cat server-dash[64544]: 2025/06/19 17:28:30 监控配置: ID=5, Name=上海移动, Type=2
Jun 19 17:28:30 Cat server-dash[64544]: 2025/06/19 17:28:30 监控配置: ID=6, Name=上海电信, Type=2
Jun 19 17:28:30 Cat server-dash[64544]: 2025/06/19 17:28:30 监控配置: ID=7, Name=河南联通, Type=2
Jun 19 17:28:30 Cat server-dash[64544]: 2025/06/19 17:28:30 监控配置: ID=8, Name=河南移动, Type=2
Jun 19 17:28:30 Cat server-dash[64544]: 2025/06/19 17:28:30 监控配置: ID=9, Name=河南电信, Type=2
Jun 19 17:28:30 Cat server-dash[64544]: [GIN] 2025/06/19 - 17:28:30 | 200 |     198.733µs |  39.144.187.158 | GET      "/api/v1/monitor/configs"
Jun 19 17:28:38 Cat server-dash[64544]: 2025/06/19 17:28:38 服务器 40 的ICMP/TCP监控器ID列表: [1 2 3 4 5 6 7 8 9]
Jun 19 17:28:38 Cat server-dash[64544]: 2025/06/19 17:28:38 监控器 1 在服务器 40 上找到 0 条记录
Jun 19 17:28:38 Cat server-dash[64544]: 2025/06/19 17:28:38 监控器 2 在服务器 40 上找到 0 条记录
Jun 19 17:28:38 Cat server-dash[64544]: 2025/06/19 17:28:38 监控器 3 在服务器 40 上找到 0 条记录
Jun 19 17:28:38 Cat server-dash[64544]: 2025/06/19 17:28:38 监控器 4 在服务器 40 上找到 0 条记录
Jun 19 17:28:38 Cat server-dash[64544]: 2025/06/19 17:28:38 监控器 5 在服务器 40 上找到 0 条记录
Jun 19 17:28:38 Cat server-dash[64544]: 2025/06/19 17:28:38 监控器 6 在服务器 40 上找到 0 条记录
Jun 19 17:28:38 Cat server-dash[64544]: 2025/06/19 17:28:38 监控器 7 在服务器 40 上找到 0 条记录
Jun 19 17:28:38 Cat server-dash[64544]: 2025/06/19 17:28:38 监控器 8 在服务器 40 上找到 0 条记录
Jun 19 17:28:38 Cat server-dash[64544]: 2025/06/19 17:28:38 监控器 9 在服务器 40 上找到 0 条记录
Jun 19 17:28:38 Cat server-dash[64544]: 2025/06/19 17:28:38 服务器 40 最终返回 0 条ICMP/TCP监控记录
Jun 19 17:28:38 Cat server-dash[64544]: [GIN] 2025/06/19 - 17:28:38 | 200 |    87.20108ms |  39.144.187.158 | GET      "/api/v1/monitor/40"
Jun 19 17:28:42 Cat server-dash[64544]: 2025/06/19 17:28:42 服务器 9 的ICMP/TCP监控器ID列表: [1 2 3 4 5 6 7 8 9]
Jun 19 17:28:42 Cat server-dash[64544]: 2025/06/19 17:28:42 监控器 1 在服务器 9 上找到 0 条记录
Jun 19 17:28:42 Cat server-dash[64544]: 2025/06/19 17:28:42 监控器 2 在服务器 9 上找到 0 条记录
Jun 19 17:28:42 Cat server-dash[64544]: 2025/06/19 17:28:42 监控器 3 在服务器 9 上找到 0 条记录
Jun 19 17:28:42 Cat server-dash[64544]: 2025/06/19 17:28:42 监控器 4 在服务器 9 上找到 0 条记录
Jun 19 17:28:42 Cat server-dash[64544]: 2025/06/19 17:28:42 监控器 5 在服务器 9 上找到 0 条记录
Jun 19 17:28:42 Cat server-dash[64544]: 2025/06/19 17:28:42 监控器 6 在服务器 9 上找到 0 条记录
Jun 19 17:28:42 Cat server-dash[64544]: 2025/06/19 17:28:42 监控器 7 在服务器 9 上找到 0 条记录
Jun 19 17:28:42 Cat server-dash[64544]: 2025/06/19 17:28:42 监控器 8 在服务器 9 上找到 0 条记录
Jun 19 17:28:42 Cat server-dash[64544]: 2025/06/19 17:28:42 监控器 9 在服务器 9 上找到 0 条记录
Jun 19 17:28:42 Cat server-dash[64544]: 2025/06/19 17:28:42 服务器 9 最终返回 0 条ICMP/TCP监控记录
Jun 19 17:28:42 Cat server-dash[64544]: [GIN] 2025/06/19 - 17:28:42 | 200 |   79.037513ms |  39.144.187.158 | GET      "/api/v1/monitor/9"
Jun 19 17:28:44 Cat server-dash[64544]: 2025/06/19 17:28:44 服务器 4 的ICMP/TCP监控器ID列表: [1 2 3 4 5 6 7 8 9]
Jun 19 17:28:44 Cat server-dash[64544]: 2025/06/19 17:28:44 监控器 1 在服务器 4 上找到 0 条记录
Jun 19 17:28:44 Cat server-dash[64544]: 2025/06/19 17:28:44 监控器 2 在服务器 4 上找到 0 条记录
Jun 19 17:28:44 Cat server-dash[64544]: 2025/06/19 17:28:44 监控器 3 在服务器 4 上找到 0 条记录
Jun 19 17:28:44 Cat server-dash[64544]: 2025/06/19 17:28:44 监控器 4 在服务器 4 上找到 0 条记录
Jun 19 17:28:44 Cat server-dash[64544]: 2025/06/19 17:28:44 监控器 5 在服务器 4 上找到 0 条记录
Jun 19 17:28:44 Cat server-dash[64544]: 2025/06/19 17:28:44 监控器 6 在服务器 4 上找到 0 条记录
Jun 19 17:28:44 Cat server-dash[64544]: 2025/06/19 17:28:44 监控器 7 在服务器 4 上找到 0 条记录
Jun 19 17:28:44 Cat server-dash[64544]: 2025/06/19 17:28:44 监控器 8 在服务器 4 上找到 0 条记录
Jun 19 17:28:44 Cat server-dash[64544]: 2025/06/19 17:28:44 监控器 9 在服务器 4 上找到 0 条记录
Jun 19 17:28:44 Cat server-dash[64544]: 2025/06/19 17:28:44 服务器 4 最终返回 0 条ICMP/TCP监控记录
Jun 19 17:28:44 Cat server-dash[64544]: [GIN] 2025/06/19 - 17:28:44 | 200 |   88.092272ms |  39.144.187.158 | GET      "/api/v1/monitor/4"";
