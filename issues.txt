Jun 19 19:35:30 Cat server-dash[3398]: fatal error: concurrent map writes
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6896024 [running]:
Jun 19 19:35:30 Cat server-dash[3398]: internal/runtime/maps.fatal({0x143907f?, 0xc017afb280?})
Jun 19 19:35:30 Cat server-dash[3398]:         runtime/panic.go:1058 +0x18
Jun 19 19:35:30 Cat server-dash[3398]: github.com/xos/serverstatus/service/singleton.UpdateTrafficStats(0x9, 0x8a22aa60, 0x74a80f7d)
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/service/singleton/alertsentinel.go:430 +0x41b
Jun 19 19:35:30 Cat server-dash[3398]: github.com/xos/serverstatus/service/rpc.updateTrafficDisplay(...)
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/service/rpc/server.go:857
Jun 19 19:35:30 Cat server-dash[3398]: github.com/xos/serverstatus/service/rpc.(*ServerHandler).processServerStateWithoutLock(0xc00d7ca000, 0x9, 0xc017afb690, 0xc006da57c0, 0x0, 0xc017afb5f8)
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/service/rpc/server.go:544 +0xc0b
Jun 19 19:35:30 Cat server-dash[3398]: github.com/xos/serverstatus/service/rpc.(*ServerHandler).ReportSystemState(0xc00d7ca000, {0x359ae08?, 0xc006de2fc0?}, 0xc00653ed80)
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/service/rpc/server.go:401 +0x873
Jun 19 19:35:30 Cat server-dash[3398]: github.com/xos/serverstatus/proto._ServerService_ReportSystemState_Handler({0x13bdfa0, 0xc00d7ca000}, {0x359ae08, 0xc006de2fc0}, 0xc0032a9b00, 0x0)
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/proto/server_grpc.pb.go:201 +0x1a6
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).processUnaryRPC(0xc00d2a0200, {0x359ae08, 0xc006de2f30}, 0xc006d1a9c0, 0xc00d7a8330, 0x3df1580, 0x0)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1405 +0x1036
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).handleStream(0xc00d2a0200, {0x359b8e8, 0xc00da4e1a0}, 0xc006d1a9c0)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1815 +0xb88
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).serveStreams.func2.1()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1035 +0x7f
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc.(*Server).serveStreams.func2 in goroutine 6894987
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1046 +0x11d
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 1 [IO wait, 2 minutes]:
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.runtime_pollWait(0x7f539368fd98, 0x72)
Jun 19 19:35:30 Cat server-dash[3398]:         runtime/netpoll.go:351 +0x85
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*pollDesc).wait(0xc00028a300?, 0x900000036?, 0x0)
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_poll_runtime.go:84 +0x27
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*pollDesc).waitRead(...)
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_poll_runtime.go:89
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*FD).Accept(0xc00028a300)
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_unix.go:620 +0x295
Jun 19 19:35:30 Cat server-dash[3398]: net.(*netFD).accept(0xc00028a300)
Jun 19 19:35:30 Cat server-dash[3398]:         net/fd_unix.go:172 +0x29
Jun 19 19:35:30 Cat server-dash[3398]: net.(*TCPListener).accept(0xc0048f3100)
Jun 19 19:35:30 Cat server-dash[3398]:         net/tcpsock_posix.go:159 +0x1b
Jun 19 19:35:30 Cat server-dash[3398]: net.(*TCPListener).Accept(0xc0048f3100)
Jun 19 19:35:30 Cat server-dash[3398]:         net/tcpsock.go:380 +0x30
Jun 19 19:35:30 Cat server-dash[3398]: net/http.(*Server).Serve(0xc0001fd700, {0x3598f68, 0xc0048f3100})
Jun 19 19:35:30 Cat server-dash[3398]:         net/http/server.go:3424 +0x30c
Jun 19 19:35:30 Cat server-dash[3398]: net/http.(*Server).ListenAndServe(0xc0001fd700)
Jun 19 19:35:30 Cat server-dash[3398]:         net/http/server.go:3350 +0x71
Jun 19 19:35:30 Cat server-dash[3398]: main.main.func5()
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/cmd/dashboard/main.go:190 +0x17
Jun 19 19:35:30 Cat server-dash[3398]: github.com/ory/graceful.Graceful(0xc004f0dec8, 0xc00234ff38)
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/ory/graceful@v0.1.3/http_graceful.go:76 +0xaf
Jun 19 19:35:30 Cat server-dash[3398]: main.main()
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/cmd/dashboard/main.go:189 +0x90e
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 18 [select, 5 minutes]:
Jun 19 19:35:30 Cat server-dash[3398]: github.com/patrickmn/go-cache.(*janitor).Run(0xc00023cb70, 0xc00021ec00)
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/patrickmn/go-cache@v2.1.0+incompatible/cache.go:1079 +0x7b
Jun 19 19:35:30 Cat server-dash[3398]: created by github.com/patrickmn/go-cache.runJanitor in goroutine 1
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/patrickmn/go-cache@v2.1.0+incompatible/cache.go:1099 +0xdb
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 19 [select, 5 minutes]:
Jun 19 19:35:30 Cat server-dash[3398]: github.com/patrickmn/go-cache.(*janitor).Run(0xc00023cb80, 0xc00021ec40)
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/patrickmn/go-cache@v2.1.0+incompatible/cache.go:1079 +0x7b
Jun 19 19:35:30 Cat server-dash[3398]: created by github.com/patrickmn/go-cache.runJanitor in goroutine 1
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/patrickmn/go-cache@v2.1.0+incompatible/cache.go:1099 +0xdb
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 20 [chan receive, 5 minutes]:
Jun 19 19:35:30 Cat server-dash[3398]: github.com/xos/serverstatus/service/singleton.InitTimezoneAndCache.func1()
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/service/singleton/singleton.go:58 +0xa5
Jun 19 19:35:30 Cat server-dash[3398]: created by github.com/xos/serverstatus/service/singleton.InitTimezoneAndCache in goroutine 1
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/service/singleton/singleton.go:54 +0xf4
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 21 [select]:
Jun 19 19:35:30 Cat server-dash[3398]: github.com/dgraph-io/badger/v3/y.(*WaterMark).process(0xc000243ef0, 0xc000243e30)
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/dgraph-io/badger/v3@v3.2103.5/y/watermark.go:214 +0x267
Jun 19 19:35:30 Cat server-dash[3398]: created by github.com/dgraph-io/badger/v3/y.(*WaterMark).Init in goroutine 1
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/dgraph-io/badger/v3@v3.2103.5/y/watermark.go:72 +0xa7
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 22 [select]:
Jun 19 19:35:30 Cat server-dash[3398]: github.com/dgraph-io/badger/v3/y.(*WaterMark).process(0xc00035c060, 0xc000243e30)
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/dgraph-io/badger/v3@v3.2103.5/y/watermark.go:214 +0x267
Jun 19 19:35:30 Cat server-dash[3398]: created by github.com/dgraph-io/badger/v3/y.(*WaterMark).Init in goroutine 1
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/dgraph-io/badger/v3@v3.2103.5/y/watermark.go:72 +0xa7
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 23 [select]:
