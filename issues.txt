问题：
一、"/network" 页面切换仍旧很慢很卡，请从根本上优化解决，恢复展示 3 天的监测数据，不要一味地减少展示数据来解决，事实证明这个途径无效。

后端输出"Jun 19 16:40:06 Cat server-dash[62743]: 2025/06/19 16:40:06 network: 为服务器 43 找到 50 条ICMP/TCP监控历史记录
Jun 19 16:40:06 Cat server-dash[62743]: [GIN] 2025/06/19 - 16:40:06 | 200 |  4.994312114s |  39.144.187.158 | GET      "/network"
Jun 19 16:40:07 Cat server-dash[62743]: 2025/06/19 16:40:07 API返回监控配置数量: 9
Jun 19 16:40:07 Cat server-dash[62743]: 2025/06/19 16:40:07 监控配置: ID=1, Name=重庆电信, Type=2
Jun 19 16:40:07 Cat server-dash[62743]: 2025/06/19 16:40:07 监控配置: ID=2, Name=重庆移动, Type=2
Jun 19 16:40:07 Cat server-dash[62743]: 2025/06/19 16:40:07 监控配置: ID=3, Name=重庆联通, Type=2
Jun 19 16:40:07 Cat server-dash[62743]: 2025/06/19 16:40:07 监控配置: ID=4, Name=上海联通, Type=2
Jun 19 16:40:07 Cat server-dash[62743]: 2025/06/19 16:40:07 监控配置: ID=5, Name=上海移动, Type=2
Jun 19 16:40:07 Cat server-dash[62743]: 2025/06/19 16:40:07 监控配置: ID=6, Name=上海电信, Type=2
Jun 19 16:40:07 Cat server-dash[62743]: 2025/06/19 16:40:07 监控配置: ID=7, Name=河南联通, Type=2
Jun 19 16:40:07 Cat server-dash[62743]: 2025/06/19 16:40:07 监控配置: ID=8, Name=河南移动, Type=2
Jun 19 16:40:07 Cat server-dash[62743]: 2025/06/19 16:40:07 监控配置: ID=9, Name=河南电信, Type=2
Jun 19 16:40:07 Cat server-dash[62743]: [GIN] 2025/06/19 - 16:40:07 | 200 |     187.311µs |  39.144.187.158 | GET      "/api/v1/monitor/configs"
Jun 19 16:40:15 Cat server-dash[62743]: 2025/06/19 16:40:15 服务器 40 的ICMP/TCP监控器ID列表: [1 2 3 4 5 6 7 8 9]
Jun 19 16:40:15 Cat server-dash[62743]: 2025/06/19 16:40:15 监控器 1 在服务器 40 上找到 200 条记录
Jun 19 16:40:16 Cat server-dash[62743]: 2025/06/19 16:40:16 监控器 2 在服务器 40 上找到 200 条记录
Jun 19 16:40:17 Cat server-dash[62743]: 2025/06/19 16:40:17 监控器 3 在服务器 40 上找到 200 条记录
Jun 19 16:40:18 Cat server-dash[62743]: 2025/06/19 16:40:18 监控器 4 在服务器 40 上找到 200 条记录
Jun 19 16:40:20 Cat server-dash[62743]: 2025/06/19 16:40:20 监控器 5 在服务器 40 上找到 200 条记录
Jun 19 16:40:22 Cat server-dash[62743]: 2025/06/19 16:40:22 监控器 6 在服务器 40 上找到 200 条记录
Jun 19 16:40:24 Cat server-dash[62743]: 2025/06/19 16:40:24 监控器 7 在服务器 40 上找到 200 条记录
Jun 19 16:40:27 Cat server-dash[62743]: 2025/06/19 16:40:27 监控器 8 在服务器 40 上找到 200 条记录
Jun 19 16:40:30 Cat server-dash[62743]: 2025/06/19 16:40:30 监控器 9 在服务器 40 上找到 200 条记录
Jun 19 16:40:30 Cat server-dash[62743]: 2025/06/19 16:40:30 服务器 40 最终返回 1800 条ICMP/TCP监控记录
Jun 19 16:40:30 Cat server-dash[62743]: [GIN] 2025/06/19 - 16:40:30 | 200 | 15.283020112s |  39.144.187.158 | GET      "/api/v1/monitor/40"
Jun 19 16:40:45 Cat server-dash[62743]: 2025/06/19 16:40:45 服务器 14 的ICMP/TCP监控器ID列表: [1 2 3 4 5 6 7 8 9]
Jun 19 16:40:45 Cat server-dash[62743]: 2025/06/19 16:40:45 监控器 1 在服务器 14 上找到 200 条记录
Jun 19 16:40:46 Cat server-dash[62743]: 2025/06/19 16:40:46 监控器 2 在服务器 14 上找到 200 条记录
Jun 19 16:40:47 Cat server-dash[62743]: 2025/06/19 16:40:47 监控器 3 在服务器 14 上找到 200 条记录
Jun 19 16:40:48 Cat server-dash[62743]: 2025/06/19 16:40:48 监控器 4 在服务器 14 上找到 200 条记录
Jun 19 16:40:50 Cat server-dash[62743]: 2025/06/19 16:40:50 监控器 5 在服务器 14 上找到 200 条记录
Jun 19 16:40:52 Cat server-dash[62743]: 2025/06/19 16:40:52 监控器 6 在服务器 14 上找到 200 条记录
Jun 19 16:40:54 Cat server-dash[62743]: 2025/06/19 16:40:54 监控器 7 在服务器 14 上找到 200 条记录
Jun 19 16:40:57 Cat server-dash[62743]: 2025/06/19 16:40:57 监控器 8 在服务器 14 上找到 200 条记录
Jun 19 16:41:00 Cat server-dash[62743]: 2025/06/19 16:41:00 监控器 9 在服务器 14 上找到 200 条记录
Jun 19 16:41:00 Cat server-dash[62743]: 2025/06/19 16:41:00 服务器 14 最终返回 1800 条ICMP/TCP监控记录
Jun 19 16:41:00 Cat server-dash[62743]: [GIN] 2025/06/19 - 16:41:00 | 200 |  15.08408584s |  39.144.187.158 | GET      "/api/v1/monitor/14"
Jun 19 16:41:33 Cat server-dash[62743]: 2025/06/19 16:41:33 服务器 4 的ICMP/TCP监控器ID列表: [1 2 3 4 5 6 7 8 9]
Jun 19 16:41:34 Cat server-dash[62743]: 2025/06/19 16:41:34 监控器 1 在服务器 4 上找到 200 条记录
Jun 19 16:41:34 Cat server-dash[62743]: 2025/06/19 16:41:34 监控器 2 在服务器 4 上找到 200 条记录
Jun 19 16:41:35 Cat server-dash[62743]: 2025/06/19 16:41:35 监控器 3 在服务器 4 上找到 200 条记录
Jun 19 16:41:37 Cat server-dash[62743]: 2025/06/19 16:41:37 监控器 4 在服务器 4 上找到 200 条记录
Jun 19 16:41:38 Cat server-dash[62743]: 2025/06/19 16:41:38 监控器 5 在服务器 4 上找到 200 条记录
Jun 19 16:41:40 Cat server-dash[62743]: 2025/06/19 16:41:40 监控器 6 在服务器 4 上找到 200 条记录
Jun 19 16:41:43 Cat server-dash[62743]: 2025/06/19 16:41:43 监控器 7 在服务器 4 上找到 200 条记录
Jun 19 16:41:45 Cat server-dash[62743]: 2025/06/19 16:41:45 监控器 8 在服务器 4 上找到 200 条记录
Jun 19 16:41:48 Cat server-dash[62743]: 2025/06/19 16:41:48 监控器 9 在服务器 4 上找到 200 条记录
Jun 19 16:41:48 Cat server-dash[62743]: 2025/06/19 16:41:48 服务器 4 最终返回 1800 条ICMP/TCP监控记录
Jun 19 16:41:48 Cat server-dash[62743]: [GIN] 2025/06/19 - 16:41:48 | 200 | 14.826842004s |  39.144.187.158 | GET      "/api/v1/monitor/4"";
