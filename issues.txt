Jun 20 23:15:31 Cat server-dash[30097]: [GIN] 2025/06/20 - 23:15:31 | 200 |     203.141µs |   60.248.19.157 | GET      "/static/logo.svg?v20220602"
Jun 20 23:15:37 Cat server-dash[30097]: [GIN] 2025/06/20 - 23:15:37 | 200 |  8.026917866s |   60.248.19.157 | GET      "/ws"
Jun 20 23:16:37 Cat server-dash[30097]: 2025/06/20 23:16:37 警告：总 goroutine 数量过多 (401)，尝试强制清理
Jun 20 23:16:46 Cat server-dash[30097]: 2025/06/20 23:16:46 警告：总 goroutine 数量过多 (412)，尝试强制清理
Jun 20 23:16:46 Cat server-dash[30097]: 2025/06/20 23:16:46 警告：总 goroutine 数量过多 (413)，尝试强制清理
Jun 20 23:16:49 Cat server-dash[30097]: 2025/06/20 23:16:49 警告：总 goroutine 数量过多 (412)，尝试强制清理
Jun 20 23:16:53 Cat server-dash[30097]: 2025/06/20 23:16:53 警告：总 goroutine 数量过多 (411)，尝试强制清理
Jun 20 23:16:58 Cat server-dash[30097]: 2025/06/20 23:16:58 警告：总 goroutine 数量过多 (405)，尝试强制清理
Jun 20 23:16:58 Cat server-dash[30097]: 2025/06/20 23:16:58 警告：总 goroutine 数量过多 (404)，尝试强制清理
Jun 20 23:17:00 Cat server-dash[30097]: 2025/06/20 23:17:00 警告：总 goroutine 数量过多 (405)，尝试强制清理
Jun 20 23:17:02 Cat server-dash[30097]: 2025/06/20 23:17:02 警告：总 goroutine 数量过多 (409)，尝试强制清理
Jun 20 23:17:08 Cat server-dash[30097]: 2025/06/20 23:17:08 警告：总 goroutine 数量过多 (406)，尝试强制清理
Jun 20 23:17:09 Cat server-dash[30097]: 2025/06/20 23:17:09 警告：总 goroutine 数量过多 (404)，尝试强制清理
Jun 20 23:17:10 Cat server-dash[30097]: 2025/06/20 23:17:10 警告：总 goroutine 数量过多 (405)，尝试强制清理
Jun 20 23:17:13 Cat server-dash[30097]: 2025/06/20 23:17:13 警告：总 goroutine 数量过多 (403)，尝试强制清理
Jun 20 23:17:14 Cat server-dash[30097]: 2025/06/20 23:17:14 警告：总 goroutine 数量过多 (404)，尝试强制清理
Jun 20 23:17:15 Cat server-dash[30097]: 2025/06/20 23:17:15 警告：总 goroutine 数量过多 (408)，尝试强制清理
Jun 20 23:17:16 Cat server-dash[30097]: 2025/06/20 23:17:16 警告：总 goroutine 数量过多 (411)，尝试强制清理
Jun 20 23:17:18 Cat server-dash[30097]: 2025/06/20 23:17:18 警告：总 goroutine 数量过多 (411)，尝试强制清理
Jun 20 23:17:26 Cat server-dash[30097]: 2025/06/20 23:17:26 警告：总 goroutine 数量过多 (410)，尝试强制清理
Jun 20 23:17:27 Cat server-dash[30097]: 2025/06/20 23:17:27 警告：总 goroutine 数量过多 (412)，尝试强制清理
Jun 20 23:17:29 Cat server-dash[30097]: 2025/06/20 23:17:29 警告：总 goroutine 数量过多 (410)，尝试强制清理
Jun 20 23:17:34 Cat server-dash[30097]: 2025/06/20 23:17:34 警告：总 goroutine 数量过多 (409)，尝试强制清理
Jun 20 23:17:39 Cat server-dash[30097]: 2025/06/20 23:17:39 警告：总 goroutine 数量过多 (409)，尝试强制清理
Jun 20 23:17:41 Cat server-dash[30097]: 2025/06/20 23:17:41 警告：总 goroutine 数量过多 (408)，尝试强制清理
Jun 20 23:17:42 Cat server-dash[30097]: 2025/06/20 23:17:42 警告：总 goroutine 数量过多 (409)，尝试强制清理
Jun 20 23:17:49 Cat server-dash[30097]: 2025/06/20 23:17:49 警告：总 goroutine 数量过多 (409)，尝试强制清理
Jun 20 23:17:50 Cat server-dash[30097]: 2025/06/20 23:17:50 警告：总 goroutine 数量过多 (410)，尝试强制清理
Jun 20 23:17:51 Cat server-dash[30097]: 2025/06/20 23:17:51 警告：总 goroutine 数量过多 (411)，尝试强制清理
Jun 20 23:17:52 Cat server-dash[30097]: 2025/06/20 23:17:52 警告：总 goroutine 数量过多 (409)，尝试强制清理
Jun 20 23:17:54 Cat server-dash[30097]: 2025/06/20 23:17:54 警告：总 goroutine 数量过多 (410)，尝试强制清理
Jun 20 23:17:56 Cat server-dash[30097]: 2025/06/20 23:17:56 警告：总 goroutine 数量过多 (414)，尝试强制清理
Jun 20 23:17:56 Cat server-dash[30097]: 2025/06/20 23:17:56 警告：总 goroutine 数量过多 (418)，尝试强制清理
Jun 20 23:17:59 Cat server-dash[30097]: 2025/06/20 23:17:59 警告：总 goroutine 数量过多 (420)，尝试强制清理

🚨 修复严重的goroutine泄漏问题

=== 问题：频繁输出goroutine警告 ===
症状：
- 第3-34行：频繁输出"警告：总 goroutine 数量过多"
- goroutine数量持续维持在400-420之间
- 清理机制效果不佳，几乎每秒都在清理
- 严重影响系统性能和日志可读性

=== 根本原因分析 ===
1. **阈值设置过低**：400个goroutine对生产环境过低
2. **清理频率过高**：每次RequestTask连接都会检查和清理
3. **清理机制不够有效**：ForceCleanupStaleConnections只清理长时间无活动的连接
4. **日志噪音**：频繁的警告日志影响系统性能

=== 根本性修复方案 ===

✅ 1. **提高阈值并优化清理策略**：
   ```go
   // 提高阈值：400 → 600
   if total > 600 {
       // 控制清理频率：至少间隔30秒
       if now.Sub(lastGoroutineCleanupTime) > 30*time.Second {
           lastGoroutineCleanupTime = now
           // 执行清理
       }
   }

   // 提高拒绝阈值：500 → 800
   if runtime.NumGoroutine() > 800 {
       // 拒绝新连接
   }
   ```

✅ 2. **减少清理频率**：
   - 使用包级变量控制清理时间间隔
   - 避免每次连接都触发清理
   - 减少不必要的系统调用

✅ 3. **智能阈值管理**：
   - **正常运行阈值**：600个goroutine
   - **清理触发阈值**：600个goroutine
   - **拒绝连接阈值**：800个goroutine
   - **清理间隔**：30秒

✅ 4. **日志优化**：
   - 减少频繁的警告日志输出
   - 只在真正需要时记录清理日志
   - 避免日志噪音影响性能

=== 预期效果 ===
- **警告日志**：从每秒输出减少到30秒间隔
- **系统性能**：减少不必要的清理操作
- **goroutine管理**：更智能的阈值控制
- **用户体验**：减少日志噪音，系统更稳定

=== 监控指标 ===
- 正常运行：goroutine数量 < 600
- 需要关注：600-800个goroutine
- 需要干预：> 800个goroutine
