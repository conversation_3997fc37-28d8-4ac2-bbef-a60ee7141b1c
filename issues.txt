o:575 +0x78
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.NewServerTransport.func2()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:336 +0xde
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc/internal/transport.NewServerTransport in goroutine 6894776
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:334 +0x189b
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6894726 [select]:
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*http2Server).keepalive(0xc005fb7ba0)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:1197 +0x1ec
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc/internal/transport.NewServerTransport in goroutine 6894724
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:357 +0x18dd
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 847296 [chan receive, 5 minutes]:
Jun 19 19:35:30 Cat server-dash[3398]: github.com/ory/graceful.Graceful.func1()
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/ory/graceful@v0.1.3/http_graceful.go:62 +0xb5
Jun 19 19:35:30 Cat server-dash[3398]: created by github.com/ory/graceful.Graceful in goroutine 1
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/ory/graceful@v0.1.3/http_graceful.go:59 +0xa5
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6894931 [IO wait]:
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.runtime_pollWait(0x7f539368f190, 0x72)
Jun 19 19:35:30 Cat server-dash[3398]:         runtime/netpoll.go:351 +0x85
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*pollDesc).wait(0xc00028b380?, 0xc017aa0000?, 0x0)
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_poll_runtime.go:84 +0x27
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*pollDesc).waitRead(...)
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_poll_runtime.go:89
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*FD).Read(0xc00028b380, {0xc017aa0000, 0x8000, 0x8000})
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_unix.go:165 +0x27a
Jun 19 19:35:30 Cat server-dash[3398]: net.(*netFD).Read(0xc00028b380, {0xc017aa0000?, 0x7f5393819108?, 0x400000000000000?})
Jun 19 19:35:30 Cat server-dash[3398]:         net/fd_posix.go:55 +0x25
Jun 19 19:35:30 Cat server-dash[3398]: net.(*conn).Read(0xc00e0911d8, {0xc017aa0000?, 0x20?, 0x10001322ae0?})
Jun 19 19:35:30 Cat server-dash[3398]:         net/net.go:194 +0x45
Jun 19 19:35:30 Cat server-dash[3398]: bufio.(*Reader).Read(0xc0055dfe60, {0xc0054de9e0, 0x9, 0x478bd9?})
Jun 19 19:35:30 Cat server-dash[3398]:         bufio/bufio.go:245 +0x197
Jun 19 19:35:30 Cat server-dash[3398]: io.ReadAtLeast({0x358fb20, 0xc0055dfe60}, {0xc0054de9e0, 0x9, 0x9}, 0x9)
Jun 19 19:35:30 Cat server-dash[3398]:         io/io.go:335 +0x91
Jun 19 19:35:30 Cat server-dash[3398]: io.ReadFull(...)
Jun 19 19:35:30 Cat server-dash[3398]:         io/io.go:354
Jun 19 19:35:30 Cat server-dash[3398]: golang.org/x/net/http2.readFrameHeader({0xc0054de9e0, 0x9, 0xc00e3e0990?}, {0x358fb20?, 0xc0055dfe60?})
Jun 19 19:35:30 Cat server-dash[3398]:         golang.org/x/net@v0.40.0/http2/frame.go:242 +0x65
Jun 19 19:35:30 Cat server-dash[3398]: golang.org/x/net/http2.(*Framer).ReadFrame(0xc0054de9a0)
Jun 19 19:35:30 Cat server-dash[3398]:         golang.org/x/net@v0.40.0/http2/frame.go:506 +0x7d
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*http2Server).HandleStreams(0xc00796aea0, {0x359ae08, 0xc006fe1a40}, 0xc006fe1a70)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:657 +0x105
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).serveStreams(0xc00d2a0200, {0x359add0?, 0x3ea7f80?}, {0x359b8e8, 0xc00796aea0}, {0x35a0f58?, 0xc00e0911d8?})
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1029 +0x396
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).handleRawConn.func1()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:964 +0x56
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc.(*Server).handleRawConn in goroutine 6894896
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:963 +0x1cb
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 847294 [select]:
Jun 19 19:35:30 Cat server-dash[3398]: github.com/xos/serverstatus/service/singleton.(*ServiceSentinel).worker(0xc0000dc900)
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/service/singleton/servicesentinel.go:456 +0x170
Jun 19 19:35:30 Cat server-dash[3398]: created by github.com/xos/serverstatus/service/singleton.NewServiceSentinel in goroutine 1
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/service/singleton/servicesentinel.go:60 +0x245
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6895028 [select, 1 minutes]:
Jun 19 19:35:30 Cat server-dash[3398]: github.com/xos/serverstatus/service/rpc.(*ServerHandler).RequestTask(0xc00d7ca000, 0x13bdfa0?, {0x359fcb0, 0xc00da93810})
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/service/rpc/server.go:303 +0x77f
Jun 19 19:35:30 Cat server-dash[3398]: github.com/xos/serverstatus/proto._ServerService_RequestTask_Handler({0x13bdfa0, 0xc00d7ca000}, {0x359ec28, 0xc002b0a8c0})
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/proto/server_grpc.pb.go:254 +0x110
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).processStreamingRPC(0xc00d2a0200, {0x359ae08, 0xc00dac2de0}, 0xc0079fe660, 0xc00d7a8330, 0x3deff60, 0x0)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1695 +0x1252
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).handleStream(0xc00d2a0200, {0x359b8e8, 0xc00d781ba0}, 0xc0079fe660)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1819 +0xb47
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).serveStreams.func2.1()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1035 +0x7f
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc.(*Server).serveStreams.func2 in goroutine 6894954
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1046 +0x11d
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6894747 [select]:
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*controlBuffer).get(0xc00e4da600, 0x1)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/controlbuf.go:412 +0x108
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*loopyWriter).run(0xc0032a9e80)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/controlbuf.go:575 +0x78
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.NewServerTransport.func2()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:336 +0xde
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc/internal/transport.NewServerTransport in goroutine 6894746
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:334 +0x189b
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6894750 [select, 1 minutes]:
Jun 19 19:35:30 Cat server-dash[3398]: github.com/xos/serverstatus/service/rpc.(*ServerHandler).RequestTask(0xc00d7ca000, 0x13bdfa0?, {0x359fcb0, 0xc01f255540})
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/service/rpc/server.go:303 +0x77f
Jun 19 19:35:30 Cat server-dash[3398]: github.com/xos/serverstatus/proto._ServerService_RequestTask_Handler({0x13bdfa0, 0xc00d7ca000}, {0x359ec28, 0xc0026be7e0})
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/proto/server_grpc.pb.go:254 +0x110
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).processStreamingRPC(0xc00d2a0200, {0x359ae08, 0xc005c9b590}, 0xc0055f5380, 0xc00d7a8330, 0x3deff60, 0x0)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1695 +0x1252
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).handleStream(0xc00d2a0200, {0x359b8e8, 0xc005e50340}, 0xc0055f5380)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1819 +0xb47
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).serveStreams.func2.1()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1035 +0x7f
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc.(*Server).serveStreams.func2 in goroutine 6894749
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1046 +0x11d
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6894725 [select]:
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*controlBuffer).get(0xc00ecd4dc0, 0x1)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/controlbuf.go:412 +0x108
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*loopyWriter).run(0xc00028aa00)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/controlbuf.go:575 +0x78
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.NewServerTransport.func2()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:336 +0xde
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc/internal/transport.NewServerTransport in goroutine 6894724
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:334 +0x189b
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6894987 [runnable]:
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.runtime_pollWait(0x7f532c497c10, 0x72)
Jun 19 19:35:30 Cat server-dash[3398]:         runtime/netpoll.go:351 +0x85
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*pollDesc).wait(0xc00d526900?, 0xc017b96000?, 0x0)
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_poll_runtime.go:84 +0x27
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*pollDesc).waitRead(...)
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_poll_runtime.go:89
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*FD).Read(0xc00d526900, {0xc017b96000, 0x8000, 0x8000})
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_unix.go:165 +0x27a
Jun 19 19:35:30 Cat server-dash[3398]: net.(*netFD).Read(0xc00d526900, {0xc017b96000?, 0x7f5393819108?, 0x400000000000000?})
Jun 19 19:35:30 Cat server-dash[3398]:         net/fd_posix.go:55 +0x25
Jun 19 19:35:30 Cat server-dash[3398]: net.(*conn).Read(0xc00e091260, {0xc017b96000?, 0x20?, 0x1322ae0?})
Jun 19 19:35:30 Cat server-dash[3398]:         net/net.go:194 +0x45
Jun 19 19:35:30 Cat server-dash[3398]: bufio.(*Reader).Read(0xc00da102a0, {0xc0054def20, 0x9, 0xc006e07a01?})
Jun 19 19:35:30 Cat server-dash[3398]:         bufio/bufio.go:245 +0x197
Jun 19 19:35:30 Cat server-dash[3398]: io.ReadAtLeast({0x358fb20, 0xc00da102a0}, {0xc0054def20, 0x9, 0x9}, 0x9)
Jun 19 19:35:30 Cat server-dash[3398]:         io/io.go:335 +0x91
Jun 19 19:35:30 Cat server-dash[3398]: io.ReadFull(...)
Jun 19 19:35:30 Cat server-dash[3398]:         io/io.go:354
Jun 19 19:35:30 Cat server-dash[3398]: golang.org/x/net/http2.readFrameHeader({0xc0054def20, 0x9, 0x478605?}, {0x358fb20?, 0xc00da102a0?})
Jun 19 19:35:30 Cat server-dash[3398]:         golang.org/x/net@v0.40.0/http2/frame.go:242 +0x65
Jun 19 19:35:30 Cat server-dash[3398]: golang.org/x/net/http2.(*Framer).ReadFrame(0xc0054deee0)
Jun 19 19:35:30 Cat server-dash[3398]:         golang.org/x/net@v0.40.0/http2/frame.go:506 +0x7d
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*http2Server).HandleStreams(0xc00da4e1a0, {0x359ae08, 0xc00da1adb0}, 0xc00da1ade0)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:657 +0x105
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).serveStreams(0xc00d2a0200, {0x359add0?, 0x3ea7f80?}, {0x359b8e8, 0xc00da4e1a0}, {0x35a0f58?, 0xc00e091260?})
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1029 +0x396
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).handleRawConn.func1()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:964 +0x56
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc.(*Server).handleRawConn in goroutine 6894984
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:963 +0x1cb
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6894768 [select, 1 minutes]:
Jun 19 19:35:30 Cat server-dash[3398]: github.com/xos/serverstatus/service/rpc.(*ServerHandler).RequestTask(0xc00d7ca000, 0x13bdfa0?, {0x359fcb0, 0xc00ed9ce60})
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/service/rpc/server.go:303 +0x77f
Jun 19 19:35:30 Cat server-dash[3398]: github.com/xos/serverstatus/proto._ServerService_RequestTask_Handler({0x13bdfa0, 0xc00d7ca000}, {0x359ec28, 0xc002b0a0e0})
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/proto/server_grpc.pb.go:254 +0x110
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).processStreamingRPC(0xc00d2a0200, {0x359ae08, 0xc005f5c4b0}, 0xc00773f980, 0xc00d7a8330, 0x3deff60, 0x0)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1695 +0x1252
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).handleStream(0xc00d2a0200, {0x359b8e8, 0xc00d54c4e0}, 0xc00773f980)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1819 +0xb47
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).serveStreams.func2.1()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1035 +0x7f
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc.(*Server).serveStreams.func2 in goroutine 6894126
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1046 +0x11d
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6894124 [select]:
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*controlBuffer).get(0xc007859ac0, 0x1)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/controlbuf.go:412 +0x108
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*loopyWriter).run(0xc00d5e5280)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/controlbuf.go:575 +0x78
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.NewServerTransport.func2()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:336 +0xde
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc/internal/transport.NewServerTransport in goroutine 6894043
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:334 +0x189b
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6895516 [select]:
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*controlBuffer).get(0xc003345980, 0x1)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/controlbuf.go:412 +0x108
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*loopyWriter).run(0xc0041f4a00)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/controlbuf.go:575 +0x78
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.NewServerTransport.func2()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:336 +0xde
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc/internal/transport.NewServerTransport in goroutine 6895515
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:334 +0x189b
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6894981 [IO wait]:
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.runtime_pollWait(0x7f532c4979e0, 0x72)
Jun 19 19:35:30 Cat server-dash[3398]:         runtime/netpoll.go:351 +0x85
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*pollDesc).wait(0xc00d526800?, 0xc017b26000?, 0x0)
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_poll_runtime.go:84 +0x27
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*pollDesc).waitRead(...)
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_poll_runtime.go:89
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*FD).Read(0xc00d526800, {0xc017b26000, 0x8000, 0x8000})
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_unix.go:165 +0x27a
Jun 19 19:35:30 Cat server-dash[3398]: net.(*netFD).Read(0xc00d526800, {0xc017b26000?, 0x7f5393819f30?, 0x400000000000000?})
Jun 19 19:35:30 Cat server-dash[3398]:         net/fd_posix.go:55 +0x25
Jun 19 19:35:30 Cat server-dash[3398]: net.(*conn).Read(0xc00e091228, {0xc017b26000?, 0x20?, 0x10001322ae0?})
Jun 19 19:35:30 Cat server-dash[3398]:         net/net.go:194 +0x45
Jun 19 19:35:30 Cat server-dash[3398]: bufio.(*Reader).Read(0xc00da10120, {0xc0054dec80, 0x9, 0x478bd9?})
Jun 19 19:35:30 Cat server-dash[3398]:         bufio/bufio.go:245 +0x197
Jun 19 19:35:30 Cat server-dash[3398]: io.ReadAtLeast({0x358fb20, 0xc00da10120}, {0xc0054dec80, 0x9, 0x9}, 0x9)
Jun 19 19:35:30 Cat server-dash[3398]:         io/io.go:335 +0x91
Jun 19 19:35:30 Cat server-dash[3398]: io.ReadFull(...)
Jun 19 19:35:30 Cat server-dash[3398]:         io/io.go:354
Jun 19 19:35:30 Cat server-dash[3398]: golang.org/x/net/http2.readFrameHeader({0xc0054dec80, 0x9, 0xc00d7f0960?}, {0x358fb20?, 0xc00da10120?})
Jun 19 19:35:30 Cat server-dash[3398]:         golang.org/x/net@v0.40.0/http2/frame.go:242 +0x65
Jun 19 19:35:30 Cat server-dash[3398]: golang.org/x/net/http2.(*Framer).ReadFrame(0xc0054dec40)
Jun 19 19:35:30 Cat server-dash[3398]:         golang.org/x/net@v0.40.0/http2/frame.go:506 +0x7d
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*http2Server).HandleStreams(0xc00da0f040, {0x359ae08, 0xc00da1a600}, 0xc00da1a630)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:657 +0x105
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).serveStreams(0xc00d2a0200, {0x359add0?, 0x3ea7f80?}, {0x359b8e8, 0xc00da0f040}, {0x35a0f58?, 0xc00e091228?})
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1029 +0x396
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).handleRawConn.func1()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:964 +0x56
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc.(*Server).handleRawConn in goroutine 6894978
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:963 +0x1cb
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6894811 [IO wait]:
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.runtime_pollWait(0x7f539368f078, 0x72)
Jun 19 19:35:30 Cat server-dash[3398]:         runtime/netpoll.go:351 +0x85
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*pollDesc).wait(0xc00d5e4d80?, 0xc017a48000?, 0x0)
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_poll_runtime.go:84 +0x27
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*pollDesc).waitRead(...)
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_poll_runtime.go:89
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*FD).Read(0xc00d5e4d80, {0xc017a48000, 0x8000, 0x8000})
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_unix.go:165 +0x27a
Jun 19 19:35:30 Cat server-dash[3398]: net.(*netFD).Read(0xc00d5e4d80, {0xc017a48000?, 0x7f53938195c0?, 0x400000000000000?})
Jun 19 19:35:30 Cat server-dash[3398]:         net/fd_posix.go:55 +0x25
Jun 19 19:35:30 Cat server-dash[3398]: net.(*conn).Read(0xc002345ea8, {0xc017a48000?, 0x20?, 0x10001322ae0?})
Jun 19 19:35:30 Cat server-dash[3398]:         net/net.go:194 +0x45
Jun 19 19:35:30 Cat server-dash[3398]: bufio.(*Reader).Read(0xc00e4677a0, {0xc002a849e0, 0x9, 0x478bd9?})
Jun 19 19:35:30 Cat server-dash[3398]:         bufio/bufio.go:245 +0x197
Jun 19 19:35:30 Cat server-dash[3398]: io.ReadAtLeast({0x358fb20, 0xc00e4677a0}, {0xc002a849e0, 0x9, 0x9}, 0x9)
Jun 19 19:35:30 Cat server-dash[3398]:         io/io.go:335 +0x91
Jun 19 19:35:30 Cat server-dash[3398]: io.ReadFull(...)
Jun 19 19:35:30 Cat server-dash[3398]:         io/io.go:354
Jun 19 19:35:30 Cat server-dash[3398]: golang.org/x/net/http2.readFrameHeader({0xc002a849e0, 0x9, 0xc00cd34480?}, {0x358fb20?, 0xc00e4677a0?})
Jun 19 19:35:30 Cat server-dash[3398]:         golang.org/x/net@v0.40.0/http2/frame.go:242 +0x65
Jun 19 19:35:30 Cat server-dash[3398]: golang.org/x/net/http2.(*Framer).ReadFrame(0xc002a849a0)
Jun 19 19:35:30 Cat server-dash[3398]:         golang.org/x/net@v0.40.0/http2/frame.go:506 +0x7d
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*http2Server).HandleStreams(0xc0068d6820, {0x359ae08, 0xc005fc3650}, 0xc005fc3680)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:657 +0x105
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).serveStreams(0xc00d2a0200, {0x359add0?, 0x3ea7f80?}, {0x359b8e8, 0xc0068d6820}, {0x35a0f58?, 0xc002345ea8?})
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1029 +0x396
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).handleRawConn.func1()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:964 +0x56
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc.(*Server).handleRawConn in goroutine 6894808
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:963 +0x1cb
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6895517 [select]:
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*http2Server).keepalive(0xc0068d7520)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:1197 +0x1ec
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc/internal/transport.NewServerTransport in goroutine 6895515
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:357 +0x18dd
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6894954 [IO wait]:
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.runtime_pollWait(0x7f532c497af8, 0x72)
Jun 19 19:35:30 Cat server-dash[3398]:         runtime/netpoll.go:351 +0x85
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*pollDesc).wait(0xc0044a7080?, 0xc017ad8000?, 0x0)
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_poll_runtime.go:84 +0x27
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*pollDesc).waitRead(...)
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_poll_runtime.go:89
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*FD).Read(0xc0044a7080, {0xc017ad8000, 0x8000, 0x8000})
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_unix.go:165 +0x27a
Jun 19 19:35:30 Cat server-dash[3398]: net.(*netFD).Read(0xc0044a7080, {0xc017ad8000?, 0x7f53938195c0?, 0x400000000000000?})
Jun 19 19:35:30 Cat server-dash[3398]:         net/fd_posix.go:55 +0x25
Jun 19 19:35:30 Cat server-dash[3398]: net.(*conn).Read(0xc00dca9430, {0xc017ad8000?, 0x20?, 0x10001322ae0?})
Jun 19 19:35:30 Cat server-dash[3398]:         net/net.go:194 +0x45
Jun 19 19:35:30 Cat server-dash[3398]: bufio.(*Reader).Read(0xc00d8398c0, {0xc002a858c0, 0x9, 0x478bd9?})
Jun 19 19:35:30 Cat server-dash[3398]:         bufio/bufio.go:245 +0x197
Jun 19 19:35:30 Cat server-dash[3398]: io.ReadAtLeast({0x358fb20, 0xc00d8398c0}, {0xc002a858c0, 0x9, 0x9}, 0x9)
Jun 19 19:35:30 Cat server-dash[3398]:         io/io.go:335 +0x91
Jun 19 19:35:30 Cat server-dash[3398]: io.ReadFull(...)
Jun 19 19:35:30 Cat server-dash[3398]:         io/io.go:354
Jun 19 19:35:30 Cat server-dash[3398]: golang.org/x/net/http2.readFrameHeader({0xc002a858c0, 0x9, 0xc00e3e0b40?}, {0x358fb20?, 0xc00d8398c0?})
Jun 19 19:35:30 Cat server-dash[3398]:         golang.org/x/net@v0.40.0/http2/frame.go:242 +0x65
Jun 19 19:35:30 Cat server-dash[3398]: golang.org/x/net/http2.(*Framer).ReadFrame(0xc002a85880)
Jun 19 19:35:30 Cat server-dash[3398]:         golang.org/x/net@v0.40.0/http2/frame.go:506 +0x7d
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*http2Server).HandleStreams(0xc00d781ba0, {0x359ae08, 0xc00d938750}, 0xc00d938780)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:657 +0x105
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).serveStreams(0xc00d2a0200, {0x359add0?, 0x3ea7f80?}, {0x359b8e8, 0xc00d781ba0}, {0x35a0f58?, 0xc00dca9430?})
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1029 +0x396
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).handleRawConn.func1()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:964 +0x56
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc.(*Server).handleRawConn in goroutine 6894906
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:963 +0x1cb
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6894986 [select]:
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*http2Server).keepalive(0xc00da4e1a0)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:1197 +0x1ec
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc/internal/transport.NewServerTransport in goroutine 6894984
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:357 +0x18dd
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6894952 [select]:
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*controlBuffer).get(0xc00dbb2f40, 0x1)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/controlbuf.go:412 +0x108
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*loopyWriter).run(0xc005ed9980)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/controlbuf.go:575 +0x78
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.NewServerTransport.func2()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:336 +0xde
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc/internal/transport.NewServerTransport in goroutine 6894906
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:334 +0x189b
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6894771 [select]:
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*http2Server).keepalive(0xc005cbda00)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:1197 +0x1ec
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc/internal/transport.NewServerTransport in goroutine 6894769
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:357 +0x18dd
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6895033 [runnable]:
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.runtime_pollWait(0x7f532c4977b0, 0x72)
Jun 19 19:35:30 Cat server-dash[3398]:         runtime/netpoll.go:351 +0x85
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*pollDesc).wait(0xc00d5e5900?, 0xc017ba6000?, 0x0)
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_poll_runtime.go:84 +0x27
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*pollDesc).waitRead(...)
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_poll_runtime.go:89
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*FD).Read(0xc00d5e5900, {0xc017ba6000, 0x8000, 0x8000})
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_unix.go:165 +0x27a
Jun 19 19:35:30 Cat server-dash[3398]: net.(*netFD).Read(0xc00d5e5900, {0xc017ba6000?, 0x7f5393819108?, 0x400000000000000?})
Jun 19 19:35:30 Cat server-dash[3398]:         net/fd_posix.go:55 +0x25
Jun 19 19:35:30 Cat server-dash[3398]: net.(*conn).Read(0xc00ef8f8a0, {0xc017ba6000?, 0x20?, 0x10001322ae0?})
Jun 19 19:35:30 Cat server-dash[3398]:         net/net.go:194 +0x45
Jun 19 19:35:30 Cat server-dash[3398]: bufio.(*Reader).Read(0xc0079fe780, {0xc002b0a9e0, 0x9, 0x478bd9?})
Jun 19 19:35:30 Cat server-dash[3398]:         bufio/bufio.go:245 +0x197
Jun 19 19:35:30 Cat server-dash[3398]: io.ReadAtLeast({0x358fb20, 0xc0079fe780}, {0xc002b0a9e0, 0x9, 0x9}, 0x9)
Jun 19 19:35:30 Cat server-dash[3398]:         io/io.go:335 +0x91
Jun 19 19:35:30 Cat server-dash[3398]: io.ReadFull(...)
Jun 19 19:35:30 Cat server-dash[3398]:         io/io.go:354
Jun 19 19:35:30 Cat server-dash[3398]: golang.org/x/net/http2.readFrameHeader({0xc002b0a9e0, 0x9, 0xcb5790?}, {0x358fb20?, 0xc0079fe780?})
Jun 19 19:35:30 Cat server-dash[3398]:         golang.org/x/net@v0.40.0/http2/frame.go:242 +0x65
Jun 19 19:35:30 Cat server-dash[3398]: golang.org/x/net/http2.(*Framer).ReadFrame(0xc002b0a9a0)
Jun 19 19:35:30 Cat server-dash[3398]:         golang.org/x/net@v0.40.0/http2/frame.go:506 +0x7d
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*http2Server).HandleStreams(0xc00db54b60, {0x359ae08, 0xc00dac33b0}, 0xc00dac33e0)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:657 +0x105
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).serveStreams(0xc00d2a0200, {0x359add0?, 0x3ea7f80?}, {0x359b8e8, 0xc00db54b60}, {0x35a0f58?, 0xc00ef8f8a0?})
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1029 +0x396
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).handleRawConn.func1()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:964 +0x56
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc.(*Server).handleRawConn in goroutine 6895030
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:963 +0x1cb
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6894810 [select]:
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*http2Server).keepalive(0xc0068d6820)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:1197 +0x1ec
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc/internal/transport.NewServerTransport in goroutine 6894808
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:357 +0x18dd
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6894953 [select]:
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*http2Server).keepalive(0xc00d781ba0)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:1197 +0x1ec
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc/internal/transport.NewServerTransport in goroutine 6894906
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:357 +0x18dd
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6894749 [IO wait]:
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.runtime_pollWait(0x7f539368f4d8, 0x72)
Jun 19 19:35:30 Cat server-dash[3398]:         runtime/netpoll.go:351 +0x85
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*pollDesc).wait(0xc0041f4f00?, 0xc0175f8000?, 0x0)
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_poll_runtime.go:84 +0x27
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*pollDesc).waitRead(...)
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_poll_runtime.go:89
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*FD).Read(0xc0041f4f00, {0xc0175f8000, 0x8000, 0x8000})
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_unix.go:165 +0x27a
Jun 19 19:35:30 Cat server-dash[3398]: net.(*netFD).Read(0xc0041f4f00, {0xc0175f8000?, 0x7f5393819108?, 0x400000000000000?})
Jun 19 19:35:30 Cat server-dash[3398]:         net/fd_posix.go:55 +0x25
Jun 19 19:35:30 Cat server-dash[3398]: net.(*conn).Read(0xc00e0907a8, {0xc0175f8000?, 0x500000000000020?, 0x1532bc26cb8?})
Jun 19 19:35:30 Cat server-dash[3398]:         net/net.go:194 +0x45
Jun 19 19:35:30 Cat server-dash[3398]: bufio.(*Reader).Read(0xc0055f5320, {0xc000001000, 0x9, 0x478bd9?})
Jun 19 19:35:30 Cat server-dash[3398]:         bufio/bufio.go:245 +0x197
Jun 19 19:35:30 Cat server-dash[3398]: io.ReadAtLeast({0x358fb20, 0xc0055f5320}, {0xc000001000, 0x9, 0x9}, 0x9)
Jun 19 19:35:30 Cat server-dash[3398]:         io/io.go:335 +0x91
Jun 19 19:35:30 Cat server-dash[3398]: io.ReadFull(...)
Jun 19 19:35:30 Cat server-dash[3398]:         io/io.go:354
Jun 19 19:35:30 Cat server-dash[3398]: golang.org/x/net/http2.readFrameHeader({0xc000001000, 0x9, 0xc00d8a2198?}, {0x358fb20?, 0xc0055f5320?})
Jun 19 19:35:30 Cat server-dash[3398]:         golang.org/x/net@v0.40.0/http2/frame.go:242 +0x65
Jun 19 19:35:30 Cat server-dash[3398]: golang.org/x/net/http2.(*Framer).ReadFrame(0xc000000fc0)
Jun 19 19:35:30 Cat server-dash[3398]:         golang.org/x/net@v0.40.0/http2/frame.go:506 +0x7d
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*http2Server).HandleStreams(0xc005e50340, {0x359ae08, 0xc005e54060}, 0xc005e54090)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:657 +0x105
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).serveStreams(0xc00d2a0200, {0x359add0?, 0x3ea7f80?}, {0x359b8e8, 0xc005e50340}, {0x35a0f58?, 0xc00e0907a8?})
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1029 +0x396
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).handleRawConn.func1()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:964 +0x56
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc.(*Server).handleRawConn in goroutine 6894746
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:963 +0x1cb
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6894798 [IO wait]:
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.runtime_pollWait(0x7f532c497468, 0x72)
Jun 19 19:35:30 Cat server-dash[3398]:         runtime/netpoll.go:351 +0x85
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*pollDesc).wait(0xc005eb4b80?, 0xc017a58000?, 0x0)
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_poll_runtime.go:84 +0x27
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*pollDesc).waitRead(...)
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_poll_runtime.go:89
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*FD).Read(0xc005eb4b80, {0xc017a58000, 0x8000, 0x8000})
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_unix.go:165 +0x27a
Jun 19 19:35:30 Cat server-dash[3398]: net.(*netFD).Read(0xc005eb4b80, {0xc017a58000?, 0x7f5393819f30?, 0x400000000000000?})
Jun 19 19:35:30 Cat server-dash[3398]:         net/fd_posix.go:55 +0x25
Jun 19 19:35:30 Cat server-dash[3398]: net.(*conn).Read(0xc000060c18, {0xc017a58000?, 0x20?, 0x10001322ae0?})
Jun 19 19:35:30 Cat server-dash[3398]:         net/net.go:194 +0x45
Jun 19 19:35:30 Cat server-dash[3398]: bufio.(*Reader).Read(0xc0055f5740, {0xc0054de900, 0x9, 0x478bd9?})
Jun 19 19:35:30 Cat server-dash[3398]:         bufio/bufio.go:245 +0x197
Jun 19 19:35:30 Cat server-dash[3398]: io.ReadAtLeast({0x358fb20, 0xc0055f5740}, {0xc0054de900, 0x9, 0x9}, 0x9)
Jun 19 19:35:30 Cat server-dash[3398]:         io/io.go:335 +0x91
Jun 19 19:35:30 Cat server-dash[3398]: io.ReadFull(...)
Jun 19 19:35:30 Cat server-dash[3398]:         io/io.go:354
Jun 19 19:35:30 Cat server-dash[3398]: golang.org/x/net/http2.readFrameHeader({0xc0054de900, 0x9, 0xc00e40e7f8?}, {0x358fb20?, 0xc0055f5740?})
Jun 19 19:35:30 Cat server-dash[3398]:         golang.org/x/net@v0.40.0/http2/frame.go:242 +0x65
Jun 19 19:35:30 Cat server-dash[3398]: golang.org/x/net/http2.(*Framer).ReadFrame(0xc0054de8c0)
Jun 19 19:35:30 Cat server-dash[3398]:         golang.org/x/net@v0.40.0/http2/frame.go:506 +0x7d
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*http2Server).HandleStreams(0xc005e7dba0, {0x359ae08, 0xc005e55260}, 0xc005e55290)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:657 +0x105
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).serveStreams(0xc00d2a0200, {0x359add0?, 0x3ea7f80?}, {0x359b8e8, 0xc005e7dba0}, {0x35a0f58?, 0xc000060c18?})
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1029 +0x396
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).handleRawConn.func1()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:964 +0x56
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc.(*Server).handleRawConn in goroutine 6894795
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:963 +0x1cb
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6894791 [IO wait]:
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.runtime_pollWait(0x7f532c497698, 0x72)
Jun 19 19:35:30 Cat server-dash[3398]:         runtime/netpoll.go:351 +0x85
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*pollDesc).wait(0xc0041f5000?, 0xc017a18000?, 0x0)
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_poll_runtime.go:84 +0x27
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*pollDesc).waitRead(...)
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_poll_runtime.go:89
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*FD).Read(0xc0041f5000, {0xc017a18000, 0x8000, 0x8000})
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_unix.go:165 +0x27a
Jun 19 19:35:30 Cat server-dash[3398]: net.(*netFD).Read(0xc0041f5000, {0xc017a18000?, 0x7f532cf72878?, 0x400000000000000?})
Jun 19 19:35:30 Cat server-dash[3398]:         net/fd_posix.go:55 +0x25
Jun 19 19:35:30 Cat server-dash[3398]: net.(*conn).Read(0xc00e0907e8, {0xc017a18000?, 0x20?, 0x100010001322ae0?})
Jun 19 19:35:30 Cat server-dash[3398]:         net/net.go:194 +0x45
Jun 19 19:35:30 Cat server-dash[3398]: bufio.(*Reader).Read(0xc0055f54a0, {0xc0000011c0, 0x9, 0x478bd9?})
Jun 19 19:35:30 Cat server-dash[3398]:         bufio/bufio.go:245 +0x197
Jun 19 19:35:30 Cat server-dash[3398]: io.ReadAtLeast({0x358fb20, 0xc0055f54a0}, {0xc0000011c0, 0x9, 0x9}, 0x9)
Jun 19 19:35:30 Cat server-dash[3398]:         io/io.go:335 +0x91
Jun 19 19:35:30 Cat server-dash[3398]: io.ReadFull(...)
Jun 19 19:35:30 Cat server-dash[3398]:         io/io.go:354
Jun 19 19:35:30 Cat server-dash[3398]: golang.org/x/net/http2.readFrameHeader({0xc0000011c0, 0x9, 0xc0061e5e90?}, {0x358fb20?, 0xc0055f54a0?})
Jun 19 19:35:30 Cat server-dash[3398]:         golang.org/x/net@v0.40.0/http2/frame.go:242 +0x65
Jun 19 19:35:30 Cat server-dash[3398]: golang.org/x/net/http2.(*Framer).ReadFrame(0xc000001180)
Jun 19 19:35:30 Cat server-dash[3398]:         golang.org/x/net@v0.40.0/http2/frame.go:506 +0x7d
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*http2Server).HandleStreams(0xc005e6c1a0, {0x359ae08, 0xc005e547e0}, 0xc005e54810)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:657 +0x105
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).serveStreams(0xc00d2a0200, {0x359add0?, 0x3ea7f80?}, {0x359b8e8, 0xc005e6c1a0}, {0x35a0f58?, 0xc00e0907e8?})
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1029 +0x396
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).handleRawConn.func1()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:964 +0x56
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc.(*Server).handleRawConn in goroutine 6894788
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:963 +0x1cb
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6894786 [IO wait]:
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.runtime_pollWait(0x7f539368f2a8, 0x72)
Jun 19 19:35:30 Cat server-dash[3398]:         runtime/netpoll.go:351 +0x85
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*pollDesc).wait(0xc0041f4f80?, 0xc017a08000?, 0x0)
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_poll_runtime.go:84 +0x27
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*pollDesc).waitRead(...)
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_poll_runtime.go:89
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*FD).Read(0xc0041f4f80, {0xc017a08000, 0x8000, 0x8000})
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_unix.go:165 +0x27a
Jun 19 19:35:30 Cat server-dash[3398]: net.(*netFD).Read(0xc0041f4f80, {0xc017a08000?, 0x7f5393819a78?, 0x400000000000000?})
Jun 19 19:35:30 Cat server-dash[3398]:         net/fd_posix.go:55 +0x25
Jun 19 19:35:30 Cat server-dash[3398]: net.(*conn).Read(0xc00e0907c8, {0xc017a08000?, 0x20?, 0x10001322ae0?})
Jun 19 19:35:30 Cat server-dash[3398]:         net/net.go:194 +0x45
Jun 19 19:35:30 Cat server-dash[3398]: bufio.(*Reader).Read(0xc0055f53e0, {0xc0000010e0, 0x9, 0x478bd9?})
Jun 19 19:35:30 Cat server-dash[3398]:         bufio/bufio.go:245 +0x197
Jun 19 19:35:30 Cat server-dash[3398]: io.ReadAtLeast({0x358fb20, 0xc0055f53e0}, {0xc0000010e0, 0x9, 0x9}, 0x9)
Jun 19 19:35:30 Cat server-dash[3398]:         io/io.go:335 +0x91
Jun 19 19:35:30 Cat server-dash[3398]: io.ReadFull(...)
Jun 19 19:35:30 Cat server-dash[3398]:         io/io.go:354
Jun 19 19:35:30 Cat server-dash[3398]: golang.org/x/net/http2.readFrameHeader({0xc0000010e0, 0x9, 0xc00e40e600?}, {0x358fb20?, 0xc0055f53e0?})
Jun 19 19:35:30 Cat server-dash[3398]:         golang.org/x/net@v0.40.0/http2/frame.go:242 +0x65
Jun 19 19:35:30 Cat server-dash[3398]: golang.org/x/net/http2.(*Framer).ReadFrame(0xc0000010a0)
Jun 19 19:35:30 Cat server-dash[3398]:         golang.org/x/net@v0.40.0/http2/frame.go:506 +0x7d
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*http2Server).HandleStreams(0xc005e50b60, {0x359ae08, 0xc005e54420}, 0xc005e54450)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:657 +0x105
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).serveStreams(0xc00d2a0200, {0x359add0?, 0x3ea7f80?}, {0x359b8e8, 0xc005e50b60}, {0x35a0f58?, 0xc00e0907c8?})
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1029 +0x396
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).handleRawConn.func1()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:964 +0x56
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc.(*Server).handleRawConn in goroutine 6894751
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:963 +0x1cb
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6894796 [select]:
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*controlBuffer).get(0xc00352f280, 0x1)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/controlbuf.go:412 +0x108
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*loopyWriter).run(0xc005ed9380)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/controlbuf.go:575 +0x78
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.NewServerTransport.func2()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:336 +0xde
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc/internal/transport.NewServerTransport in goroutine 6894795
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:334 +0x189b
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6894789 [select]:
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*controlBuffer).get(0xc00e4da880, 0x1)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/controlbuf.go:412 +0x108
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*loopyWriter).run(0xc00d5e4880)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/controlbuf.go:575 +0x78
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.NewServerTransport.func2()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:336 +0xde
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc/internal/transport.NewServerTransport in goroutine 6894788
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:334 +0x189b
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6896023 [runnable]:
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*http2Server).writeHeaderLocked.func1()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:1038 +0x30
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*controlBuffer).executeAndPut(0xc003345980, 0x2?, {0x3593400, 0xc00662dd10})
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/controlbuf.go:360 +0xb7
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*http2Server).writeHeaderLocked(0xc0068d7520, 0xc006d1a960)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:1038 +0x386
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*http2Server).writeHeader(0xc0068d7520, 0xc006d1a960, 0x1?)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:1007 +0x1d4
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*http2Server).write(0xc0068d7520, 0xc006d1a960, {0xc0076e97f0, 0x5, 0x5}, {0xc00767eef0?, 0x0?, 0x3597e08?}, 0xc00021e000?)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:1137 +0xb5
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*ServerStream).Write(...)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/server_stream.go:70
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).sendResponse(0xc00d2a0200, {0x359ae08, 0xc0077166c0}, 0xc006d1a960, {0x1340a00, 0xc007716750}, {0x0, 0x0}, 0xc0076e97e8, {0x0, ...})
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1174 +0x444
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).processUnaryRPC(0xc00d2a0200, {0x359ae08, 0xc006de2db0}, 0xc006d1a960, 0xc00d7a8330, 0x3df1580, 0x0)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1452 +0x1169
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).handleStream(0xc00d2a0200, {0x359b8e8, 0xc0068d7520}, 0xc006d1a960)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1815 +0xb88
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).serveStreams.func2.1()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1035 +0x7f
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc.(*Server).serveStreams.func2 in goroutine 6895518
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1046 +0x11d
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6894951 [select, 1 minutes]:
Jun 19 19:35:30 Cat server-dash[3398]: github.com/xos/serverstatus/service/rpc.(*ServerHandler).RequestTask(0xc00d7ca000, 0x13bdfa0?, {0x359fcb0, 0xc00d8e7610})
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/service/rpc/server.go:303 +0x77f
Jun 19 19:35:30 Cat server-dash[3398]: github.com/xos/serverstatus/proto._ServerService_RequestTask_Handler({0x13bdfa0, 0xc00d7ca000}, {0x359ec28, 0xc002a857a0})
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/proto/server_grpc.pb.go:254 +0x110
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).processStreamingRPC(0xc00d2a0200, {0x359ae08, 0xc00d9384b0}, 0xc00d839860, 0xc00d7a8330, 0x3deff60, 0x0)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1695 +0x1252
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).handleStream(0xc00d2a0200, {0x359b8e8, 0xc00cdea820}, 0xc00d839860)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1819 +0xb47
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).serveStreams.func2.1()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1035 +0x7f
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc.(*Server).serveStreams.func2 in goroutine 6894925
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1046 +0x11d
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6894781 [select, 1 minutes]:
Jun 19 19:35:30 Cat server-dash[3398]: github.com/xos/serverstatus/service/rpc.(*ServerHandler).RequestTask(0xc00d7ca000, 0x13bdfa0?, {0x359fcb0, 0xc01f255870})
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/service/rpc/server.go:303 +0x77f
Jun 19 19:35:30 Cat server-dash[3398]: github.com/xos/serverstatus/proto._ServerService_RequestTask_Handler({0x13bdfa0, 0xc00d7ca000}, {0x359ec28, 0xc0026be8c0})
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/proto/server_grpc.pb.go:254 +0x110
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).processStreamingRPC(0xc00d2a0200, {0x359ae08, 0xc005c9b800}, 0xc003b87680, 0xc00d7a8330, 0x3deff60, 0x0)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1695 +0x1252
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).handleStream(0xc00d2a0200, {0x359b8e8, 0xc005cbda00}, 0xc003b87680)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1819 +0xb47
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).serveStreams.func2.1()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1035 +0x7f
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc.(*Server).serveStreams.func2 in goroutine 6894772
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1046 +0x11d
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6894930 [select]:
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*http2Server).keepalive(0xc00796aea0)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:1197 +0x1ec
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc/internal/transport.NewServerTransport in goroutine 6894896
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:357 +0x18dd
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6895518 [runnable]:
Jun 19 19:35:30 Cat server-dash[3398]: syscall.Syscall(0x0, 0x3a, 0xc005bfc000, 0x8000)
Jun 19 19:35:30 Cat server-dash[3398]:         syscall/syscall_linux.go:73 +0x25
Jun 19 19:35:30 Cat server-dash[3398]: syscall.read(0xc005ed5700?, {0xc005bfc000?, 0xc00d2a0200?, 0xc003345990?})
Jun 19 19:35:30 Cat server-dash[3398]:         syscall/zsyscall_linux_amd64.go:736 +0x38
Jun 19 19:35:30 Cat server-dash[3398]: syscall.Read(...)
Jun 19 19:35:30 Cat server-dash[3398]:         syscall/syscall_unix.go:183
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.ignoringEINTRIO(...)
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_unix.go:745
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*FD).Read(0xc005ed5700, {0xc005bfc000, 0x8000, 0x8000})
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_unix.go:161 +0x2ae
Jun 19 19:35:30 Cat server-dash[3398]: net.(*netFD).Read(0xc005ed5700, {0xc005bfc000?, 0xc01063eb80?, 0xc01063eb80?})
Jun 19 19:35:30 Cat server-dash[3398]:         net/fd_posix.go:55 +0x25
Jun 19 19:35:30 Cat server-dash[3398]: net.(*conn).Read(0xc0023448b8, {0xc005bfc000?, 0x3eaa4a0?, 0xc00d893920?})
Jun 19 19:35:30 Cat server-dash[3398]:         net/net.go:194 +0x45
Jun 19 19:35:30 Cat server-dash[3398]: bufio.(*Reader).Read(0xc01ab61c80, {0xc002a84040, 0x9, 0xc006e078e0?})
Jun 19 19:35:30 Cat server-dash[3398]:         bufio/bufio.go:245 +0x197
Jun 19 19:35:30 Cat server-dash[3398]: io.ReadAtLeast({0x358fb20, 0xc01ab61c80}, {0xc002a84040, 0x9, 0x9}, 0x9)
Jun 19 19:35:30 Cat server-dash[3398]:         io/io.go:335 +0x91
Jun 19 19:35:30 Cat server-dash[3398]: io.ReadFull(...)
Jun 19 19:35:30 Cat server-dash[3398]:         io/io.go:354
Jun 19 19:35:30 Cat server-dash[3398]: golang.org/x/net/http2.readFrameHeader({0xc002a84040, 0x9, 0x478605?}, {0x358fb20?, 0xc01ab61c80?})
Jun 19 19:35:30 Cat server-dash[3398]:         golang.org/x/net@v0.40.0/http2/frame.go:242 +0x65
Jun 19 19:35:30 Cat server-dash[3398]: golang.org/x/net/http2.(*Framer).ReadFrame(0xc002a84000)
Jun 19 19:35:30 Cat server-dash[3398]:         golang.org/x/net@v0.40.0/http2/frame.go:506 +0x7d
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc/internal/transport.(*http2Server).HandleStreams(0xc0068d7520, {0x359ae08, 0xc0041035f0}, 0xc004103620)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:657 +0x105
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).serveStreams(0xc00d2a0200, {0x359add0?, 0x3ea7f80?}, {0x359b8e8, 0xc0068d7520}, {0x35a0f58?, 0xc0023448b8?})
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1029 +0x396
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).handleRawConn.func1()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:964 +0x56
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc.(*Server).handleRawConn in goroutine 6895515
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:963 +0x1cb
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6894929 [select, 1 minutes]:
Jun 19 19:35:30 Cat server-dash[3398]: github.com/xos/serverstatus/service/rpc.(*ServerHandler).RequestTask(0xc00d7ca000, 0x13bdfa0?, {0x359fcb0, 0xc00d5d74d0})
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/service/rpc/server.go:303 +0x77f
Jun 19 19:35:30 Cat server-dash[3398]: github.com/xos/serverstatus/proto._ServerService_RequestTask_Handler({0x13bdfa0, 0xc00d7ca000}, {0x359ec28, 0xc002b0a540})
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/proto/server_grpc.pb.go:254 +0x110
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).processStreamingRPC(0xc00d2a0200, {0x359ae08, 0xc0078edaa0}, 0xc007789b00, 0xc00d7a8330, 0x3deff60, 0x0)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1695 +0x1252
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).handleStream(0xc00d2a0200, {0x359b8e8, 0xc00796aea0}, 0xc007789b00)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1819 +0xb47
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).serveStreams.func2.1()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1035 +0x7f
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc.(*Server).serveStreams.func2 in goroutine 6894931
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1046 +0x11d
Jun 19 19:35:30 Cat systemd[1]: server-dash.service: Main process exited, code=exited, status=2/INVALIDARGUMENT

