Jun 19 00:00:00 Cat server-dash[13059]: 2025/06/19 00:00:00 BadgerDB模式：流量数据一致性检查完成
Jun 19 00:00:00 Cat server-dash[13059]: 2025/06/19 00:00:00 执行监控历史记录一致性检查...
Jun 19 00:00:00 Cat server-dash[13059]: 2025/06/19 00:00:00 BadgerDB模式：监控历史记录检查完成
Jun 19 00:00:00 Cat server-dash[13059]: 2025/06/19 00:00:00 FindAll: 去重后保留 17 个服务器记录
Jun 19 00:00:00 Cat server-dash[13059]: 2025/06/19 00:00:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 19 00:00:00 Cat server-dash[13059]: 2025/06/19 00:00:00 BadgerDB: 成功保存 4 个用户的数据
Jun 19 00:00:00 Cat server-dash[13059]: 2025/06/19 00:00:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 19 00:00:00 Cat server-dash[13059]: 2025/06/19 00:00:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在 >
Jun 19 00:00:00 Cat server-dash[13059]: 2025/06/19 00:00:00 报警系统内存清理完成: 清理了 0 个失效报警规则, 0>
Jun 19 00:00:00 Cat server-dash[13059]: 2025/06/19 00:00:00 每小时清理完成，当前内存使用: 332 MB
Jun 19 00:00:00 Cat server-dash[13059]: 2025/06/19 00:00:00 BadgerDB: 成功保存 17 个服务器的累计流量数据
Jun 19 00:00:00 Cat server-dash[13059]: 2025/06/19 00:00:00 BadgerDB: 成功保存 17 个服务器的累计流量数据
Jun 19 00:00:57 Cat server-dash[13059]: fatal error: concurrent map read and map write
Jun 19 00:00:57 Cat server-dash[13059]: goroutine 4985913 [running]:
Jun 19 00:00:57 Cat server-dash[13059]: internal/runtime/maps.fatal({0x144ab65?, 0x3e439a0?})
Jun 19 00:00:57 Cat server-dash[13059]:         runtime/panic.go:1058 +0x18
Jun 19 00:00:57 Cat server-dash[13059]: github.com/xos/serverstatus/service/rpc.checkAndResetCycleTraffic(0x>
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/xos/serverstatus/service/rpc/server.go:914 +0x511
Jun 19 00:00:57 Cat server-dash[13059]: github.com/xos/serverstatus/service/rpc.(*ServerHandler).processServ>
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/xos/serverstatus/service/rpc/server.go:413 +0x8b
Jun 19 00:00:57 Cat server-dash[13059]: github.com/xos/serverstatus/service/rpc.(*ServerHandler).ReportSyste>
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/xos/serverstatus/service/rpc/server.go:401 +0x873
Jun 19 00:00:57 Cat server-dash[13059]: github.com/xos/serverstatus/proto._ServerService_ReportSystemState_H>
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/xos/serverstatus/proto/server_grpc.pb.go:201 +0x1>
Jun 19 00:00:57 Cat server-dash[13059]: google.golang.org/grpc.(*Server).processUnaryRPC(0xc0000d2200, {0x35>
Jun 19 00:00:57 Cat server-dash[13059]:         google.golang.org/grpc@v1.72.0/server.go:1405 +0x1036
Jun 19 00:00:57 Cat server-dash[13059]: google.golang.org/grpc.(*Server).handleStream(0xc0000d2200, {0x3561f>
Jun 19 00:00:57 Cat server-dash[13059]:         google.golang.org/grpc@v1.72.0/server.go:1815 +0xb88
Jun 19 00:00:57 Cat server-dash[13059]: google.golang.org/grpc.(*Server).serveStreams.func2.1()
Jun 19 00:00:57 Cat server-dash[13059]:         google.golang.org/grpc@v1.72.0/server.go:1035 +0x7f
Jun 19 00:00:57 Cat server-dash[13059]: created by google.golang.org/grpc.(*Server).serveStreams.func2 in go>
Jun 19 00:00:57 Cat server-dash[13059]:         google.golang.org/grpc@v1.72.0/server.go:1046 +0x11d
Jun 19 00:00:57 Cat server-dash[13059]: goroutine 1 [IO wait, 1 minutes]:
Jun 19 00:00:57 Cat server-dash[13059]: internal/poll.runtime_pollWait(0x7fa05a827d98, 0x72)
Jun 19 00:00:57 Cat server-dash[13059]:         runtime/netpoll.go:351 +0x85
Jun 19 00:00:57 Cat server-dash[13059]: internal/poll.(*pollDesc).wait(0xc00028a080?, 0x900000036?, 0x0)
Jun 19 00:00:57 Cat server-dash[13059]:         internal/poll/fd_poll_runtime.go:84 +0x27
Jun 19 00:00:57 Cat server-dash[13059]: internal/poll.(*pollDesc).waitRead(...)
Jun 19 00:00:57 Cat server-dash[13059]:         internal/poll/fd_poll_runtime.go:89
Jun 19 00:00:57 Cat server-dash[13059]: internal/poll.(*FD).Accept(0xc00028a080)
Jun 19 00:00:57 Cat server-dash[13059]:         internal/poll/fd_unix.go:620 +0x295
Jun 19 00:00:57 Cat server-dash[13059]: net.(*netFD).accept(0xc00028a080)
Jun 19 00:00:57 Cat server-dash[13059]:         net/fd_unix.go:172 +0x29
Jun 19 00:00:57 Cat server-dash[13059]: net.(*TCPListener).accept(0xc01bc62c80)
Jun 19 00:00:57 Cat server-dash[13059]:         net/tcpsock_posix.go:159 +0x1b
Jun 19 00:00:57 Cat server-dash[13059]: net.(*TCPListener).Accept(0xc01bc62c80)
Jun 19 00:00:57 Cat server-dash[13059]:         net/tcpsock.go:380 +0x30
Jun 19 00:00:57 Cat server-dash[13059]: net/http.(*Server).Serve(0xc01ba61300, {0x355f628, 0xc01bc62c80})
Jun 19 00:00:57 Cat server-dash[13059]:         net/http/server.go:3424 +0x30c
Jun 19 00:00:57 Cat server-dash[13059]: net/http.(*Server).ListenAndServe(0xc01ba61300)
Jun 19 00:00:57 Cat server-dash[13059]:         net/http/server.go:3350 +0x71
Jun 19 00:00:57 Cat server-dash[13059]: main.main.func5()
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/xos/serverstatus/cmd/dashboard/main.go:190 +0x17
Jun 19 00:00:57 Cat server-dash[13059]: github.com/ory/graceful.Graceful(0xc000041ec8, 0xc01b7e15c0)
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/ory/graceful@v0.1.3/http_graceful.go:76 +0xaf
Jun 19 00:00:57 Cat server-dash[13059]: main.main()
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/xos/serverstatus/cmd/dashboard/main.go:189 +0x90e
Jun 19 00:00:57 Cat server-dash[13059]: goroutine 9 [select, 10 minutes]:
Jun 19 00:00:57 Cat server-dash[13059]: github.com/patrickmn/go-cache.(*janitor).Run(0xc000115b00, 0xc00021e>
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/patrickmn/go-cache@v2.1.0+incompatible/cache.go:1>
Jun 19 00:00:57 Cat server-dash[13059]: created by github.com/patrickmn/go-cache.runJanitor in goroutine 1
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/patrickmn/go-cache@v2.1.0+incompatible/cache.go:1>
Jun 19 00:00:57 Cat server-dash[13059]: goroutine 10 [select, 22 minutes]:
Jun 19 00:00:57 Cat server-dash[13059]: github.com/patrickmn/go-cache.(*janitor).Run(0xc000115b10, 0xc00021e>
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/patrickmn/go-cache@v2.1.0+incompatible/cache.go:1>
Jun 19 00:00:57 Cat server-dash[13059]: created by github.com/patrickmn/go-cache.runJanitor in goroutine 1
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/patrickmn/go-cache@v2.1.0+incompatible/cache.go:1>
Jun 19 00:00:57 Cat server-dash[13059]: goroutine 11 [chan receive, 1 minutes]:
Jun 19 00:00:57 Cat server-dash[13059]: github.com/xos/serverstatus/service/singleton.InitTimezoneAndCache.f>
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/xos/serverstatus/service/singleton/singleton.go:5>
Jun 19 00:00:57 Cat server-dash[13059]: created by github.com/xos/serverstatus/service/singleton.InitTimezon>
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/xos/serverstatus/service/singleton/singleton.go:5>
Jun 19 00:00:57 Cat server-dash[13059]: goroutine 12 [select]:
Jun 19 00:00:57 Cat server-dash[13059]: github.com/dgraph-io/badger/v3/y.(*WaterMark).process(0xc000243ef0, >
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/dgraph-io/badger/v3@v3.2103.5/y/watermark.go:214 >
Jun 19 00:00:57 Cat server-dash[13059]: created by github.com/dgraph-io/badger/v3/y.(*WaterMark).Init in gor>
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/dgraph-io/badger/v3@v3.2103.5/y/watermark.go:72 +>
Jun 19 00:00:57 Cat server-dash[13059]: goroutine 13 [select]:
Jun 19 00:00:57 Cat server-dash[13059]: github.com/dgraph-io/badger/v3/y.(*WaterMark).process(0xc00035c060, >
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/dgraph-io/badger/v3@v3.2103.5/y/watermark.go:214 >
Jun 19 00:00:57 Cat server-dash[13059]: created by github.com/dgraph-io/badger/v3/y.(*WaterMark).Init in gor>
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/dgraph-io/badger/v3@v3.2103.5/y/watermark.go:72 +>
Jun 19 00:00:57 Cat server-dash[13059]: goroutine 14 [select]:
Jun 19 00:00:57 Cat server-dash[13059]: github.com/dgraph-io/ristretto/z.(*AllocatorPool).freeupAllocators(0>
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/dgraph-io/ristretto@v0.2.0/z/allocator.go:385 +0x>
Jun 19 00:00:57 Cat server-dash[13059]: created by github.com/dgraph-io/ristretto/z.NewAllocatorPool in goro>
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/dgraph-io/ristretto@v0.2.0/z/allocator.go:324 +0x>
Jun 19 00:00:57 Cat server-dash[13059]: goroutine 15 [select]:
Jun 19 00:00:57 Cat server-dash[13059]: github.com/dgraph-io/ristretto.(*defaultPolicy).processItems(0xc0001>
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/dgraph-io/ristretto@v0.2.0/policy.go:101 +0x7f
Jun 19 00:00:57 Cat server-dash[13059]: created by github.com/dgraph-io/ristretto.newDefaultPolicy in gorout>
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/dgraph-io/ristretto@v0.2.0/policy.go:85 +0x139
Jun 19 00:00:57 Cat server-dash[13059]: goroutine 16 [select]:
Jun 19 00:00:57 Cat server-dash[13059]: github.com/dgraph-io/ristretto.(*Cache).processItems(0xc0000ff080)
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/dgraph-io/ristretto@v0.2.0/cache.go:459 +0x112
Jun 19 00:00:57 Cat server-dash[13059]: created by github.com/dgraph-io/ristretto.NewCache in goroutine 1
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/dgraph-io/ristretto@v0.2.0/cache.go:211 +0x669
Jun 19 00:00:57 Cat server-dash[13059]: goroutine 50 [select]:
Jun 19 00:00:57 Cat server-dash[13059]: github.com/dgraph-io/badger/v3.(*DB).monitorCache(0xc000172908, 0xc0>
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/dgraph-io/badger/v3@v3.2103.5/db.go:469 +0x15d
Jun 19 00:00:57 Cat server-dash[13059]: created by github.com/dgraph-io/badger/v3.Open in goroutine 1
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/dgraph-io/badger/v3@v3.2103.5/db.go:311 +0xc09
Jun 19 00:00:57 Cat server-dash[13059]: goroutine 51 [select]:
Jun 19 00:00:57 Cat server-dash[13059]: github.com/dgraph-io/badger/v3.(*DB).updateSize(0xc000172908, 0xc000>
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/dgraph-io/badger/v3@v3.2103.5/db.go:1171 +0x13e
Jun 19 00:00:57 Cat server-dash[13059]: created by github.com/dgraph-io/badger/v3.Open in goroutine 1
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/dgraph-io/badger/v3@v3.2103.5/db.go:331 +0xe16
Jun 19 00:00:57 Cat server-dash[13059]: goroutine 38 [select, 1 minutes]:
Jun 19 00:00:57 Cat server-dash[13059]: github.com/xos/serverstatus/service/singleton.(*GoroutinePool).start>
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/xos/serverstatus/service/singleton/goroutine_pool>
Jun 19 00:00:57 Cat server-dash[13059]: created by github.com/xos/serverstatus/service/singleton.(*Goroutine>
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/xos/serverstatus/service/singleton/goroutine_pool>
Jun 19 00:00:57 Cat server-dash[13059]: goroutine 24 [select]:
Jun 19 00:00:57 Cat server-dash[13059]: github.com/dgraph-io/badger/v3.(*levelsController).runCompactor(0xc0>
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/dgraph-io/badger/v3@v3.2103.5/levels.go:508 +0x229
Jun 19 00:00:57 Cat server-dash[13059]: created by github.com/dgraph-io/badger/v3.(*levelsController).startC>
Jun 19 00:00:57 Cat server-dash[13059]:         github.com/dgraph-io/badger/v3@v3.2103.5/levels.go:354 +0x58