Jun 21 19:55:08 Cat server-dash[60878]: 2025/06/21 19:55:08 NG>> 尝试发送通知到组 [default]，消息：[DDNS记录变更]
Jun 21 19:55:08 Cat server-dash[60878]: 服务器: [WiFi]鸽鸽
Jun 21 19:55:08 Cat server-dash[60878]: 域名: wifi.edge.ip-ddns.com
Jun 21 19:55:08 Cat server-dash[60878]: 记录类型: A
Jun 21 19:55:08 Cat server-dash[60878]: IP变更: ************** => **************
Jun 21 19:55:08 Cat server-dash[60878]: 变更时间: 2025-06-21 19:55:08
Jun 21 19:55:08 Cat server-dash[60878]: 2025/06/21 19:55:08 NG>> 通知方式组 [default] 包含 3 个通知方式
Jun 21 19:55:08 Cat server-dash[60878]: 2025/06/21 19:55:08 NG>> 尝试通知 TG[佩佩]
Jun 21 19:55:08 Cat server-dash[60878]: 2025/06/21 19:55:08 NG>> 尝试通知 TG[翠花]
Jun 21 19:55:08 Cat server-dash[60878]: 2025/06/21 19:55:08 NG>> 尝试通知 TG[老王]
Jun 21 19:55:08 Cat server-dash[60878]: 2025/06/21 19:55:08 更新域名(wifi.edge.ip-ddns.com)DDNS成功
Jun 21 19:55:08 Cat server-dash[60878]: 2025/06/21 19:55:08 NG>> 向  TG[翠花]  发送通知成功：
Jun 21 19:55:09 Cat server-dash[60878]: 2025/06/21 19:55:09 NG>> 向  TG[老王]  发送通知成功：
Jun 21 19:55:09 Cat server-dash[60878]: 2025/06/21 19:55:09 NG>> 向  TG[佩佩]  发送通知成功：
Jun 21 19:57:37 Cat server-dash[60878]: [GIN] 2025/06/21 - 19:57:37 | 200 |     899.296µs | 2a01:4f8:1c1c:a98a::1 | GET      "/"
Jun 21 19:57:37 Cat server-dash[60878]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:59120: write: broken pipe
Jun 21 19:59:11 Cat server-dash[60878]: 2025/06/21 19:59:11 DispatchTask: 发送任务到服务器 5 失败: rpc error: code = Internal desc = transport: SendHeader called multiple times
Jun 21 20:00:00 Cat server-dash[60878]: 2025/06/21 20:00:00 BadgerDB模式：流量数据一致性检查完成
Jun 21 20:00:00 Cat server-dash[60878]: 2025/06/21 20:00:00 执行监控历史记录一致性检查...
Jun 21 20:00:00 Cat server-dash[60878]: 2025/06/21 20:00:00 BadgerDB模式：监控历史记录检查完成
Jun 21 20:00:00 Cat server-dash[60878]: 2025/06/21 20:00:00 FindAll: 去重后保留 17 个服务器记录
Jun 21 20:00:00 Cat server-dash[60878]: 2025/06/21 20:00:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 21 20:00:00 Cat server-dash[60878]: 2025/06/21 20:00:00 BadgerDB: 成功保存 4 个用户的数据
Jun 21 20:00:00 Cat server-dash[60878]: 2025/06/21 20:00:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 21 20:00:00 Cat server-dash[60878]: 2025/06/21 20:00:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 21 20:00:00 Cat server-dash[60878]: 2025/06/21 20:00:00 报警系统内存清理完成: 清理了 0 个失效报警规则, 0 个服务器历史记录, 释放内存 45MB
Jun 21 20:00:00 Cat server-dash[60878]: 2025/06/21 20:00:00 每小时清理完成，当前内存使用: 408 MB
Jun 21 20:00:00 Cat server-dash[60878]: 2025/06/21 20:00:00 BadgerDB: 成功保存 17 个服务器的累计流量数据
Jun 21 20:00:00 Cat server-dash[60878]: 2025/06/21 20:00:00 BadgerDB: 成功保存 17 个服务器的累计流量数据
Jun 21 20:02:20 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:02:20 | 200 |        1h0m0s |  ************** | GET      "/ws"
Jun 21 20:02:41 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:02:41 | 200 |     918.422µs |   ************* | GET      "/"
Jun 21 20:02:41 Cat server-dash[60878]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:12916: write: broken pipe
Jun 21 20:05:00 Cat server-dash[60878]: 2025/06/21 20:05:00 BadgerDB模式：流量数据一致性检查完成
Jun 21 20:05:00 Cat server-dash[60878]: 2025/06/21 20:05:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 21 20:05:00 Cat server-dash[60878]: 2025/06/21 20:05:00 BadgerDB: 成功保存 4 个用户的数据
Jun 21 20:05:00 Cat server-dash[60878]: 2025/06/21 20:05:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 21 20:05:00 Cat server-dash[60878]: 2025/06/21 20:05:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 21 20:07:44 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:07:44 | 200 |      917.46µs |    ************ | GET      "/"
Jun 21 20:07:44 Cat server-dash[60878]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:50836: write: connection reset by peer
Jun 21 20:10:00 Cat server-dash[60878]: 2025/06/21 20:10:00 执行监控历史记录一致性检查...
Jun 21 20:10:00 Cat server-dash[60878]: 2025/06/21 20:10:00 BadgerDB模式：监控历史记录检查完成
Jun 21 20:10:00 Cat server-dash[60878]: 2025/06/21 20:10:00 BadgerDB模式：流量数据一致性检查完成
Jun 21 20:10:00 Cat server-dash[60878]: 2025/06/21 20:10:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 21 20:10:00 Cat server-dash[60878]: 2025/06/21 20:10:00 BadgerDB: 成功保存 4 个用户的数据
Jun 21 20:10:00 Cat server-dash[60878]: 2025/06/21 20:10:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 21 20:10:00 Cat server-dash[60878]: 2025/06/21 20:10:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 21 20:10:00 Cat server-dash[60878]: 2025/06/21 20:10:00 BadgerDB: 成功保存 17 个服务器的累计流量数据
Jun 21 20:12:47 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:12:47 | 200 |    3.037626ms |    ************ | GET      "/"
Jun 21 20:12:47 Cat server-dash[60878]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:9766: write: broken pipe
Jun 21 20:15:00 Cat server-dash[60878]: 2025/06/21 20:15:00 BadgerDB模式：流量数据一致性检查完成
Jun 21 20:15:00 Cat server-dash[60878]: 2025/06/21 20:15:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 21 20:15:00 Cat server-dash[60878]: 2025/06/21 20:15:00 BadgerDB: 成功保存 4 个用户的数据
Jun 21 20:15:00 Cat server-dash[60878]: 2025/06/21 20:15:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 21 20:15:00 Cat server-dash[60878]: 2025/06/21 20:15:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 21 20:15:11 Cat server-dash[60878]: 2025/06/21 20:15:11 DispatchTask: 发送任务到服务器 40 失败: rpc error: code = Internal desc = transport: SendHeader called multiple times
Jun 21 20:17:52 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:17:52 | 200 |    1.140228ms | *************** | GET      "/"
Jun 21 20:17:52 Cat server-dash[60878]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:24990: write: broken pipe
Jun 21 20:18:01 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:18:01 | 200 |    1.454397ms |  ************** | GET      "/"
Jun 21 20:18:02 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:18:02 | 200 |     240.811µs |  ************** | GET      "/static/main.js?v2025060722"
Jun 21 20:18:02 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:18:02 | 200 |     468.739µs |  ************** | GET      "/static/fontawesome.min.css?v2025032723"
Jun 21 20:18:02 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:18:02 | 200 |     149.901µs |  ************** | GET      "/static/wallpaper.js?v20220423"
Jun 21 20:18:02 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:18:02 | 200 |      70.462µs |  ************** | GET      "/static/mixin.js?v20240912"
Jun 21 20:18:02 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:18:02 | 200 |     113.734µs |  ************** | GET      "/static/main.css?v2025060313"
Jun 21 20:18:03 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:18:03 | 200 |     223.359µs |  ************** | GET      "/static/logo.svg?v20220602"
Jun 21 20:18:03 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:18:03 | 200 |     836.017µs |  ************** | GET      "/static/webfonts/fa-solid-900.woff2"
Jun 21 20:18:03 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:18:03 | 200 |     798.367µs |  ************** | GET      "/static/webfonts/fa-regular-400.woff2"
Jun 21 20:18:03 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:18:03 | 200 |     284.724µs |  ************** | GET      "/static/bg/pattern.svg"
Jun 21 20:18:03 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:18:03 | 404 |     192.892µs |  ************** | OPTIONS  "/api/search-server?word="
Jun 21 20:18:04 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:18:04 | 200 |     818.936µs |  ************** | GET      "/static/webfonts/fa-light-300.woff2"
Jun 21 20:18:04 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:18:04 | 200 |    1.000947ms |  ************** | GET      "/static/webfonts/fa-thin-100.woff2"
Jun 21 20:18:35 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:18:35 | 200 |  32.03708602s |  ************** | GET      "/ws"
Jun 21 20:20:00 Cat server-dash[60878]: 2025/06/21 20:20:00 BadgerDB模式：流量数据一致性检查完成
Jun 21 20:20:00 Cat server-dash[60878]: 2025/06/21 20:20:00 执行监控历史记录一致性检查...
Jun 21 20:20:00 Cat server-dash[60878]: 2025/06/21 20:20:00 BadgerDB模式：监控历史记录检查完成
Jun 21 20:20:00 Cat server-dash[60878]: 2025/06/21 20:20:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 21 20:20:00 Cat server-dash[60878]: 2025/06/21 20:20:00 BadgerDB: 成功保存 4 个用户的数据
Jun 21 20:20:00 Cat server-dash[60878]: 2025/06/21 20:20:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 21 20:20:00 Cat server-dash[60878]: 2025/06/21 20:20:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 21 20:20:00 Cat server-dash[60878]: 2025/06/21 20:20:00 BadgerDB: 成功保存 17 个服务器的累计流量数据
Jun 21 20:21:06 Cat server-dash[60878]: 2025/06/21 20:21:06 服务器 [WiFi]鸽鸽 (ID:26) IP变化 (**************/2409:8949:2c13:895e:1848:55c6:b1f4:c45e -> **************/2409:8949:2c13:895e:1848:55c6:b1f4:c45e)，触发DDNS更新，配置数量: 1
Jun 21 20:21:06 Cat server-dash[60878]: 2025/06/21 20:21:06 NG>> 尝试发送通知到组 [default]，消息：[IP 变更]
Jun 21 20:21:06 Cat server-dash[60878]: 服务器: [WiFi]鸽鸽 (ID:26)
Jun 21 20:21:06 Cat server-dash[60878]: IP变更: **************/2409:8949:2c13:895e:1848:55c6:b1f4:c45e => **************/2409:8949:2c13:895e:1848:55c6:b1f4:c45e
Jun 21 20:21:06 Cat server-dash[60878]: 变更时间: 2025-06-21 20:21:06
Jun 21 20:21:06 Cat server-dash[60878]: 2025/06/21 20:21:06 NG>> 通知方式组 [default] 包含 3 个通知方式
Jun 21 20:21:06 Cat server-dash[60878]: 2025/06/21 20:21:06 NG>> 尝试通知 TG[佩佩]
Jun 21 20:21:06 Cat server-dash[60878]: 2025/06/21 20:21:06 NG>> 尝试通知 TG[翠花]
Jun 21 20:21:06 Cat server-dash[60878]: 2025/06/21 20:21:06 NG>> 尝试通知 TG[老王]
Jun 21 20:21:06 Cat server-dash[60878]: 2025/06/21 20:21:06 正在尝试更新域名(wifi.edge.ip-ddns.com)DDNS(1/3)
Jun 21 20:21:07 Cat server-dash[60878]: 2025/06/21 20:21:07 NG>> 向  TG[佩佩]  发送通知成功：
Jun 21 20:21:07 Cat server-dash[60878]: 2025/06/21 20:21:07 域名 wifi.edge.ip-ddns.com 的 A 记录IP发生变化: ************** -> **************
Jun 21 20:21:07 Cat server-dash[60878]: 2025/06/21 20:21:07 NG>> 向  TG[翠花]  发送通知成功：
Jun 21 20:21:07 Cat server-dash[60878]: 2025/06/21 20:21:07 NG>> 向  TG[老王]  发送通知成功：
Jun 21 20:21:07 Cat server-dash[60878]: 2025/06/21 20:21:07 检测到IP变化: ************** -> **************，发送DDNS变更通知
Jun 21 20:21:07 Cat server-dash[60878]: 2025/06/21 20:21:07 NG>> 尝试发送通知到组 [default]，消息：[DDNS记录变更]
Jun 21 20:21:07 Cat server-dash[60878]: 服务器: [WiFi]鸽鸽
Jun 21 20:21:07 Cat server-dash[60878]: 域名: wifi.edge.ip-ddns.com
Jun 21 20:21:07 Cat server-dash[60878]: 记录类型: A
Jun 21 20:21:07 Cat server-dash[60878]: IP变更: ************** => **************
Jun 21 20:21:07 Cat server-dash[60878]: 变更时间: 2025-06-21 20:21:07
Jun 21 20:21:07 Cat server-dash[60878]: 2025/06/21 20:21:07 NG>> 通知方式组 [default] 包含 3 个通知方式
Jun 21 20:21:07 Cat server-dash[60878]: 2025/06/21 20:21:07 NG>> 尝试通知 TG[老王]
Jun 21 20:21:07 Cat server-dash[60878]: 2025/06/21 20:21:07 NG>> 尝试通知 TG[佩佩]
Jun 21 20:21:07 Cat server-dash[60878]: 2025/06/21 20:21:07 NG>> 尝试通知 TG[翠花]
Jun 21 20:21:08 Cat server-dash[60878]: 2025/06/21 20:21:08 NG>> 向  TG[佩佩]  发送通知成功：
Jun 21 20:21:08 Cat server-dash[60878]: 2025/06/21 20:21:08 更新域名(wifi.edge.ip-ddns.com)DDNS成功
Jun 21 20:21:08 Cat server-dash[60878]: 2025/06/21 20:21:08 NG>> 向  TG[翠花]  发送通知成功：
Jun 21 20:21:08 Cat server-dash[60878]: 2025/06/21 20:21:08 NG>> 向  TG[老王]  发送通知成功：
Jun 21 20:22:45 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:22:45 | 200 |        1h0m0s |  ************** | GET      "/ws"
Jun 21 20:22:58 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:22:58 | 200 |     951.724µs |    ************ | GET      "/"
Jun 21 20:22:58 Cat server-dash[60878]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:14446: write: broken pipe
Jun 21 20:24:18 Cat server-dash[60878]: 2025/06/21 20:24:18 数据大小限制完成: 状态记录=54, 监控项=9
Jun 21 20:25:00 Cat server-dash[60878]: 2025/06/21 20:25:00 BadgerDB模式：流量数据一致性检查完成
Jun 21 20:25:00 Cat server-dash[60878]: 2025/06/21 20:25:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 21 20:25:00 Cat server-dash[60878]: 2025/06/21 20:25:00 BadgerDB: 成功保存 4 个用户的数据
Jun 21 20:25:00 Cat server-dash[60878]: 2025/06/21 20:25:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 21 20:25:00 Cat server-dash[60878]: 2025/06/21 20:25:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 21 20:28:03 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:28:03 | 200 |      874.67µs |   ************* | GET      "/"
Jun 21 20:28:03 Cat server-dash[60878]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:47864: write: broken pipe
Jun 21 20:30:00 Cat server-dash[60878]: 2025/06/21 20:30:00 BadgerDB模式：流量数据一致性检查完成
Jun 21 20:30:00 Cat server-dash[60878]: 2025/06/21 20:30:00 执行监控历史记录一致性检查...
Jun 21 20:30:00 Cat server-dash[60878]: 2025/06/21 20:30:00 BadgerDB模式：监控历史记录检查完成
Jun 21 20:30:00 Cat server-dash[60878]: 2025/06/21 20:30:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 21 20:30:00 Cat server-dash[60878]: 2025/06/21 20:30:00 BadgerDB: 成功保存 4 个用户的数据
Jun 21 20:30:00 Cat server-dash[60878]: 2025/06/21 20:30:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 21 20:30:00 Cat server-dash[60878]: 2025/06/21 20:30:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 21 20:30:00 Cat server-dash[60878]: 2025/06/21 20:30:00 BadgerDB: 成功保存 17 个服务器的累计流量数据
Jun 21 20:31:20 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:31:20 | 200 |    1.305308ms |  ************** | GET      "/"
Jun 21 20:31:21 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:31:21 | 200 |     448.501µs |  ************** | GET      "/static/fontawesome.min.css?v2025032723"
Jun 21 20:31:21 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:31:21 | 200 |     227.117µs |  ************** | GET      "/static/main.css?v2025060313"
Jun 21 20:31:21 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:31:21 | 200 |     214.803µs |  ************** | GET      "/static/logo.svg?v20220602"
Jun 21 20:31:26 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:31:26 | 200 |  4.031905623s |  ************** | GET      "/ws"
Jun 21 20:33:07 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:33:07 | 200 |      911.75µs |  168.119.123.75 | GET      "/"
Jun 21 20:33:07 Cat server-dash[60878]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:25092: write: connection reset by peer
Jun 21 20:35:00 Cat server-dash[60878]: 2025/06/21 20:35:00 BadgerDB模式：流量数据一致性检查完成
Jun 21 20:35:00 Cat server-dash[60878]: 2025/06/21 20:35:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 21 20:35:00 Cat server-dash[60878]: 2025/06/21 20:35:00 BadgerDB: 成功保存 4 个用户的数据
Jun 21 20:35:00 Cat server-dash[60878]: 2025/06/21 20:35:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 21 20:35:00 Cat server-dash[60878]: 2025/06/21 20:35:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 21 20:38:10 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:38:10 | 200 |      895.78µs | 2a01:4f8:1c1c:a98a::1 | GET      "/"
Jun 21 20:38:10 Cat server-dash[60878]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:59278: write: broken pipe
Jun 21 20:39:05 Cat server-dash[60878]: 2025/06/21 20:39:05 network: 进入网络页面处理函数
Jun 21 20:39:05 Cat server-dash[60878]: 2025/06/21 20:39:05 network: 使用BadgerDB模式，跳过GORM查询监控历史
Jun 21 20:39:05 Cat server-dash[60878]: 2025/06/21 20:39:05 network: 从监控配置构建监控服务器ID列表
Jun 21 20:39:06 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:39:06 | 200 |  430.392556ms |  ************** | GET      "/network"
Jun 21 20:39:07 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:39:07 | 200 |     325.451µs |  ************** | GET      "/static/fontawesome.min.css?v2025032723"
Jun 21 20:39:07 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:39:07 | 200 |     116.048µs |  ************** | GET      "/static/logo.svg?v20220602"
Jun 21 20:39:07 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:39:07 | 200 |     104.004µs |  ************** | GET      "/static/main.css?v2025060313"
Jun 21 20:39:07 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:39:07 | 200 |        16m16s |  ************** | GET      "/ws"
Jun 21 20:39:07 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:39:07 | 200 |      160.07µs |  ************** | GET      "/static/wallpaper.js?v20220423"
Jun 21 20:39:08 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:39:08 | 200 |     347.252µs |  ************** | GET      "/static/bg/pattern.svg"
Jun 21 20:39:08 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:39:08 | 200 |     118.914µs |  ************** | GET      "/static/mixin.js?v20240912"
Jun 21 20:39:09 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:39:09 | 200 |     239.669µs |  ************** | GET      "/static/main.js?v2025060722"
Jun 21 20:39:09 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:39:09 | 200 |        50m40s |  ************** | GET      "/ws"
Jun 21 20:39:09 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:39:09 | 200 |        39m34s |  ************** | GET      "/ws"
Jun 21 20:39:10 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:39:10 | 200 |     993.303µs |  ************** | GET      "/api/v1/monitor/configs"
Jun 21 20:39:10 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:39:10 | 200 |        36m44s |  ************** | GET      "/ws"
Jun 21 20:39:10 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:39:10 | 200 |     406.923µs |  ************** | GET      "/static/webfonts/fa-regular-400.woff2"
Jun 21 20:39:10 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:39:10 | 200 |     825.879µs |  ************** | GET      "/static/webfonts/fa-brands-400.woff2"
Jun 21 20:39:16 Cat server-dash[60878]: 2025/06/21 20:39:16 API /monitor/41 返回 15895 条记录（3天数据，所有监控器）
Jun 21 20:39:16 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:39:16 | 200 |  395.214485ms |  ************** | GET      "/api/v1/monitor/41"
Jun 21 20:39:20 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:39:20 | 404 |     245.961µs |   66.249.64.167 | GET      "/robots.txt"
Jun 21 20:39:20 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:39:20 | 200 |     209.884µs |   66.249.64.168 | GET      "/static/logo.svg?v20220602"
Jun 21 20:39:26 Cat server-dash[60878]: 2025/06/21 20:39:26 API /monitor/22 返回 15949 条记录（3天数据，所有监控器）
Jun 21 20:39:26 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:39:26 | 200 |  396.519051ms |  ************** | GET      "/api/v1/monitor/22"
Jun 21 20:39:29 Cat server-dash[60878]: 2025/06/21 20:39:29 数据大小限制完成: 状态记录=54, 监控项=9
Jun 21 20:39:33 Cat server-dash[60878]: 2025/06/21 20:39:33 API /monitor/8 返回 15873 条记录（3天数据，所有监控器）
Jun 21 20:39:33 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:39:33 | 200 |  361.148979ms |  ************** | GET      "/api/v1/monitor/8"
Jun 21 20:39:37 Cat server-dash[60878]: 2025/06/21 20:39:37 API /monitor/43 返回 16279 条记录（3天数据，所有监控器）
Jun 21 20:39:37 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:39:37 | 200 |    429.1411ms |  ************** | GET      "/api/v1/monitor/43"
Jun 21 20:39:39 Cat server-dash[60878]: 2025/06/21 20:39:39 数据大小限制完成: 状态记录=54, 监控项=9
Jun 21 20:39:40 Cat server-dash[60878]: 2025/06/21 20:39:40 API /monitor/14 返回 16066 条记录（3天数据，所有监控器）
Jun 21 20:39:40 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:39:40 | 200 |  435.653911ms |  ************** | GET      "/api/v1/monitor/14"
Jun 21 20:39:45 Cat server-dash[60878]: 2025/06/21 20:39:45 API /monitor/5 返回 16087 条记录（3天数据，所有监控器）
Jun 21 20:39:45 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:39:45 | 200 |  365.839744ms |  ************** | GET      "/api/v1/monitor/5"
Jun 21 20:39:49 Cat server-dash[60878]: 2025/06/21 20:39:49 数据大小限制完成: 状态记录=54, 监控项=9
Jun 21 20:39:50 Cat server-dash[60878]: 2025/06/21 20:39:50 API /monitor/6 返回 15735 条记录（3天数据，所有监控器）
Jun 21 20:39:50 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:39:50 | 200 |  354.296179ms |  ************** | GET      "/api/v1/monitor/6"
Jun 21 20:39:52 Cat server-dash[60878]: 2025/06/21 20:39:52 API /monitor/40 返回 15473 条记录（3天数据，所有监控器）
Jun 21 20:39:52 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:39:52 | 200 |  383.356359ms |  ************** | GET      "/api/v1/monitor/40"
Jun 21 20:39:59 Cat server-dash[60878]: 2025/06/21 20:39:59 数据大小限制完成: 状态记录=54, 监控项=9
Jun 21 20:40:00 Cat server-dash[60878]: 2025/06/21 20:40:00 BadgerDB模式：流量数据一致性检查完成
Jun 21 20:40:00 Cat server-dash[60878]: 2025/06/21 20:40:00 执行监控历史记录一致性检查...
Jun 21 20:40:00 Cat server-dash[60878]: 2025/06/21 20:40:00 BadgerDB模式：监控历史记录检查完成
Jun 21 20:40:00 Cat server-dash[60878]: 2025/06/21 20:40:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 21 20:40:00 Cat server-dash[60878]: 2025/06/21 20:40:00 BadgerDB: 成功保存 4 个用户的数据
Jun 21 20:40:00 Cat server-dash[60878]: 2025/06/21 20:40:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 21 20:40:00 Cat server-dash[60878]: 2025/06/21 20:40:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 21 20:40:00 Cat server-dash[60878]: 2025/06/21 20:40:00 BadgerDB: 成功保存 17 个服务器的累计流量数据
Jun 21 20:40:12 Cat server-dash[60878]: 2025/06/21 20:40:12 API /monitor/4 返回 15714 条记录（3天数据，所有监控器）
Jun 21 20:40:12 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:40:12 | 200 |   477.67719ms |  ************** | GET      "/api/v1/monitor/4"
Jun 21 20:40:23 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:40:23 | 200 |    1.281963ms |  ************** | GET      "/"
Jun 21 20:40:24 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:40:24 | 200 |      469.19µs |  ************** | GET      "/static/webfonts/fa-solid-900.woff2"
Jun 21 20:40:25 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:40:25 | 200 |       421.8µs |  ************** | GET      "/api/search-server?word="
Jun 21 20:43:13 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:43:13 | 200 |     915.696µs |  ************** | GET      "/"
Jun 21 20:43:13 Cat server-dash[60878]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:50656: write: broken pipe
Jun 21 20:45:00 Cat server-dash[60878]: 2025/06/21 20:45:00 BadgerDB模式：流量数据一致性检查完成
Jun 21 20:45:00 Cat server-dash[60878]: 2025/06/21 20:45:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 21 20:45:00 Cat server-dash[60878]: 2025/06/21 20:45:00 BadgerDB: 成功保存 4 个用户的数据
Jun 21 20:45:00 Cat server-dash[60878]: 2025/06/21 20:45:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 21 20:45:00 Cat server-dash[60878]: 2025/06/21 20:45:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 21 20:48:17 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:48:17 | 200 |     908.884µs | *************** | GET      "/"
Jun 21 20:48:17 Cat server-dash[60878]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:49294: write: broken pipe
Jun 21 20:50:00 Cat server-dash[60878]: 2025/06/21 20:50:00 执行监控历史记录一致性检查...
Jun 21 20:50:00 Cat server-dash[60878]: 2025/06/21 20:50:00 BadgerDB模式：监控历史记录检查完成
Jun 21 20:50:00 Cat server-dash[60878]: 2025/06/21 20:50:00 BadgerDB模式：流量数据一致性检查完成
Jun 21 20:50:00 Cat server-dash[60878]: 2025/06/21 20:50:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 21 20:50:00 Cat server-dash[60878]: 2025/06/21 20:50:00 BadgerDB: 成功保存 4 个用户的数据
Jun 21 20:50:00 Cat server-dash[60878]: 2025/06/21 20:50:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 21 20:50:00 Cat server-dash[60878]: 2025/06/21 20:50:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 21 20:50:00 Cat server-dash[60878]: 2025/06/21 20:50:00 BadgerDB: 成功保存 17 个服务器的累计流量数据
Jun 21 20:53:19 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:53:19 | 200 |    1.141551ms |    ************ | GET      "/"
Jun 21 20:53:19 Cat server-dash[60878]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:6636: write: broken pipe
Jun 21 20:54:18 Cat server-dash[60878]: 2025/06/21 20:54:18 数据大小限制完成: 状态记录=54, 监控项=9
Jun 21 20:54:26 Cat server-dash[60878]: 2025/06/21 20:54:26 报警系统内存清理完成: 清理了 0 个失效报警规则, 0 个服务器历史记录, 释放内存 2MB
Jun 21 20:54:26 Cat server-dash[60878]: 2025/06/21 20:54:26 NG>> 报警规则检测每小时 1200 次 2025-06-21 20:54:26.313984347 +0800 CST m=+25217.025467138 2025-06-21 20:54:26.361357476 +0800 CST m=+25217.072840257
Jun 21 20:55:00 Cat server-dash[60878]: 2025/06/21 20:55:00 BadgerDB模式：流量数据一致性检查完成
Jun 21 20:55:00 Cat server-dash[60878]: 2025/06/21 20:55:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 21 20:55:00 Cat server-dash[60878]: 2025/06/21 20:55:00 BadgerDB: 成功保存 4 个用户的数据
Jun 21 20:55:00 Cat server-dash[60878]: 2025/06/21 20:55:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 21 20:55:00 Cat server-dash[60878]: 2025/06/21 20:55:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 21 20:58:24 Cat server-dash[60878]: [GIN] 2025/06/21 - 20:58:24 | 200 |      943.47µs |  ************** | GET      "/"
Jun 21 20:58:24 Cat server-dash[60878]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:19490: write: broken pipe
Jun 21 21:00:00 Cat server-dash[60878]: 2025/06/21 21:00:00 执行监控历史记录一致性检查...
Jun 21 21:00:00 Cat server-dash[60878]: 2025/06/21 21:00:00 BadgerDB模式：监控历史记录检查完成
Jun 21 21:00:00 Cat server-dash[60878]: 2025/06/21 21:00:00 BadgerDB模式：流量数据一致性检查完成
Jun 21 21:00:00 Cat server-dash[60878]: 2025/06/21 21:00:00 内存清理完成，清理时间：2025-06-21 21:00:00.001856945 +0800 CST m=+25550.713339736
Jun 21 21:00:00 Cat server-dash[60878]: 2025/06/21 21:00:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 21 21:00:00 Cat server-dash[60878]: 2025/06/21 21:00:00 BadgerDB: 成功保存 4 个用户的数据
Jun 21 21:00:00 Cat server-dash[60878]: 2025/06/21 21:00:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 21 21:00:00 Cat server-dash[60878]: 2025/06/21 21:00:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 21 21:00:00 Cat server-dash[60878]: 2025/06/21 21:00:00 报警系统内存清理完成: 清理了 0 个失效报警规则, 0 个服务器历史记录, 释放内存 39MB
Jun 21 21:00:00 Cat server-dash[60878]: 2025/06/21 21:00:00 每小时清理完成，当前内存使用: 407 MB
Jun 21 21:00:00 Cat server-dash[60878]: 2025/06/21 21:00:00 BadgerDB: 成功保存 17 个服务器的累计流量数据
Jun 21 21:03:26 Cat server-dash[60878]: [GIN] 2025/06/21 - 21:03:26 | 200 |    1.137804ms |  ************** | GET      "/"
Jun 21 21:03:26 Cat server-dash[60878]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:50026: write: broken pipe
Jun 21 21:05:00 Cat server-dash[60878]: 2025/06/21 21:05:00 BadgerDB模式：流量数据一致性检查完成
Jun 21 21:05:00 Cat server-dash[60878]: 2025/06/21 21:05:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 21 21:05:00 Cat server-dash[60878]: 2025/06/21 21:05:00 BadgerDB: 成功保存 4 个用户的数据
Jun 21 21:05:00 Cat server-dash[60878]: 2025/06/21 21:05:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 21 21:05:00 Cat server-dash[60878]: 2025/06/21 21:05:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 21 21:08:31 Cat server-dash[60878]: [GIN] 2025/06/21 - 21:08:31 | 200 |      789.73µs | 2a01:4f8:c17:42e4::1 | GET      "/"
Jun 21 21:08:31 Cat server-dash[60878]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:15196: write: broken pipe
Jun 21 21:10:00 Cat server-dash[60878]: 2025/06/21 21:10:00 BadgerDB模式：流量数据一致性检查完成
Jun 21 21:10:00 Cat server-dash[60878]: 2025/06/21 21:10:00 执行监控历史记录一致性检查...
Jun 21 21:10:00 Cat server-dash[60878]: 2025/06/21 21:10:00 BadgerDB模式：监控历史记录检查完成
Jun 21 21:10:00 Cat server-dash[60878]: 2025/06/21 21:10:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 21 21:10:00 Cat server-dash[60878]: 2025/06/21 21:10:00 BadgerDB: 成功保存 4 个用户的数据
Jun 21 21:10:00 Cat server-dash[60878]: 2025/06/21 21:10:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 21 21:10:00 Cat server-dash[60878]: 2025/06/21 21:10:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 21 21:10:00 Cat server-dash[60878]: 2025/06/21 21:10:00 BadgerDB: 成功保存 17 个服务器的累计流量数据
Jun 21 21:13:35 Cat server-dash[60878]: [GIN] 2025/06/21 - 21:13:35 | 200 |    1.009883ms |    ************ | GET      "/"
Jun 21 21:13:35 Cat server-dash[60878]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:19714: write: connection reset by peer
Jun 21 21:14:11 Cat server-dash[60878]: 2025/06/21 21:14:11 DispatchTask: 发送任务到服务器 6 失败: rpc error: code = Internal desc = transport: SendHeader called multiple times
Jun 21 21:15:00 Cat server-dash[60878]: 2025/06/21 21:15:00 BadgerDB模式：流量数据一致性检查完成
Jun 21 21:15:00 Cat server-dash[60878]: 2025/06/21 21:15:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 21 21:15:00 Cat server-dash[60878]: 2025/06/21 21:15:00 BadgerDB: 成功保存 4 个用户的数据
Jun 21 21:15:00 Cat server-dash[60878]: 2025/06/21 21:15:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 21 21:15:00 Cat server-dash[60878]: 2025/06/21 21:15:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
