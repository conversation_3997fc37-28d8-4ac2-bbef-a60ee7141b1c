Jun 20 19:29:58 Cat server-dash[23054]: 2025/06/20 19:29:58 NG>> Graceful::START
Jun 20 19:29:58 Cat server-dash[23054]: 2025/06/20 19:29:58 程序正在优雅关闭，保存所有数据...
Jun 20 19:29:58 Cat systemd[1]: Stopping server-dash.service - Server Status Dashborad...
░░ Subject: A stop job for unit server-dash.service has begun execution
░░ Defined-By: systemd
░░ Support: https://www.debian.org/support
░░
░░ A stop job for unit server-dash.service has begun execution.
░░
░░ The job identifier is 5059.
Jun 20 19:30:00 Cat server-dash[23054]: 2025/06/20 19:30:00 系统繁忙，跳过监控历史记录一致性检查
Jun 20 19:30:00 Cat server-dash[23054]: 2025/06/20 19:30:00 BadgerDB模式：流量数据一致性检查完成
Jun 20 19:30:00 Cat server-dash[23054]: 2025/06/20 19:30:00 定期清理：当前goroutine数量 1554，开始清理
Jun 20 19:30:03 Cat server-dash[23054]: 2025/06/20 19:30:03 NG>> Graceful::TIMEOUT
Jun 20 19:31:05 Cat server-dash[23054]: 2025/06/20 19:31:05 数据大小限制完成: 状态记录=54, 监控项=9
Jun 20 19:31:28 Cat systemd[1]: server-dash.service: State 'stop-sigterm' timed out. Killing.
Jun 20 19:31:28 Cat systemd[1]: server-dash.service: Killing process 23054 (server-dash) with signal SIGKILL.
Jun 20 19:31:28 Cat systemd[1]: server-dash.service: Killing process 23057 (n/a) with signal SIGKILL.
Jun 20 19:31:28 Cat systemd[1]: server-dash.service: Killing process 23058 (server-dash) with signal SIGKILL.
Jun 20 19:31:28 Cat systemd[1]: server-dash.service: Killing process 23059 (n/a) with signal SIGKILL.
Jun 20 19:31:28 Cat systemd[1]: server-dash.service: Killing process 23061 (server-dash) with signal SIGKILL.
Jun 20 19:31:29 Cat systemd[1]: server-dash.service: Main process exited, code=killed, status=9/KILL
░░ Subject: Unit process exited
░░ Defined-By: systemd
░░ Support: https://www.debian.org/support
░░
░░ An ExecStart= process belonging to unit server-dash.service has exited.
░░
░░ The process' exit code is 'killed' and its exit status is 9.
Jun 20 19:31:29 Cat systemd[1]: server-dash.service: Failed with result 'timeout'.
░░ Subject: Unit failed
░░ Defined-By: systemd
░░ Support: https://www.debian.org/support
░░
░░ The unit server-dash.service has entered the 'failed' state with result 'timeout'.
Jun 20 19:31:29 Cat systemd[1]: Stopped server-dash.service - Server Status Dashborad.
░░ Subject: A stop job for unit server-dash.service has finished
░░ Defined-By: systemd
░░ Support: https://www.debian.org/support
░░
░░ A stop job for unit server-dash.service has finished.
░░
░░ The job identifier is 5059 and the job result is done.
Jun 20 19:31:29 Cat systemd[1]: server-dash.service: Consumed 2min 7.365s CPU time.
░░ Subject: Resources consumed by unit runtime
░░ Defined-By: systemd
░░ Support: https://www.debian.org/support
░░
░░ The unit server-dash.service completed and consumed the indicated resources.
Jun 20 19:31:29 Cat systemd[1]: Started server-dash.service - Server Status Dashborad.
░░ Subject: A start job for unit server-dash.service has finished successfully
░░ Defined-By: systemd
░░ Support: https://www.debian.org/support
░░
░░ A start job for unit server-dash.service has finished successfully.
░░
░░ The job identifier is 5059.
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 使用BadgerDB数据库...
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 BadgerDB将使用目录: data/badger
Jun 20 19:31:29 Cat server-dash[24945]: badger 2025/06/20 19:31:29 INFO: All 26 tables opened in 1ms
Jun 20 19:31:29 Cat server-dash[24945]: badger 2025/06/20 19:31:29 INFO: Discard stats nextEmptySlot: 0
Jun 20 19:31:29 Cat server-dash[24945]: badger 2025/06/20 19:31:29 INFO: Set nextTxnTs to 1312912
Jun 20 19:31:29 Cat server-dash[24945]: badger 2025/06/20 19:31:29 INFO: Deleting empty file: data/badger/000330.vlog
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 初始化BadgerDB...
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 BadgerDB已有 4 个用户，跳过创建默认管理员
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 Goroutine池启动新worker，当前workers: 1
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 Goroutine池启动新worker，当前workers: 2
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 Goroutine池启动新worker，当前workers: 3
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 Goroutine池启动新worker，当前workers: 4
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 Goroutine池启动新worker，当前workers: 5
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 Goroutine池启动新worker，当前workers: 1
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 Goroutine池启动新worker，当前workers: 2
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 Goroutine池初始化完成 - 通知池: 5-20 workers, 任务池: 2-10 workers
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 使用 BadgerDB 数据库，跳过表结构检查和迁移操作
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 启动数据库服务...
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 使用BadgerDB，跳过数据库连接池相关操作
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 正在加载通知服务...
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 正在加载服务器列表...
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 FindAll: 去重后保留 17 个服务器记录
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 BadgerDB模式：跳过注册GORM相关的定时任务
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 正在加载定时任务...
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 BadgerDB模式：使用空的定时任务列表
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 BadgerDB模式: 已初始化 1 个Cron任务的Servers字段
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 正在加载API服务...
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 从 BadgerDB 加载了 2 个API令牌
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 加载API令牌: X2WfFczBw1LiREh5g7I6zUkGa3zCQgEm (Note: 姐姐)
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 加载API令牌: a0A9UThBGdlcImOL581VHrE8c1AdYnpy (Note: 默认)
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 API令牌加载完成: 有效 2 个，清理无效 0 个
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 正在初始化NAT...
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 正在初始化DDNS...
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 初始化DDNS配置...
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 DDNS初始化完成，加载了 3 个配置
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 BadgerDB模式: 已初始化 3 个DDNS配置的Domains字段
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 正在同步流量数据...
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 使用 BadgerDB 模式，跳过流量数据同步
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 正在添加定时任务...
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 启动计划任务...
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 子服务加载完成
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 开始验证和修复数据完整性...
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 监控配置数据已在ServiceSentinel初始化时验证
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 报警规则数据已在BadgerDB.FindAll中验证
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 用户数据完整性正常
Jun 20 19:31:29 Cat server-dash[24945]: 2025/06/20 19:31:29 数据完整性验证和修复完成
Jun 20 19:31:31 Cat server-dash[24945]: 2025/06/20 19:31:31 使用BadgerDB，执行BadgerDB监控历史清理...
Jun 20 19:31:31 Cat server-dash[24945]: 2025/06/20 19:31:31 FindAll: 去重后保留 17 个服务器记录
Jun 20 19:31:31 Cat server-dash[24945]: 2025/06/20 19:31:31 BadgerDB监控历史清理完成，清理了0条记录
Jun 20 19:31:31 Cat server-dash[24945]: 2025/06/20 19:31:31 BadgerDB模式：跳过流量数据重新计算
Jun 20 19:31:31 Cat server-dash[24945]: 2025/06/20 19:31:31 正在初始化ServiceSentinel...
Jun 20 19:31:31 Cat server-dash[24945]: 2025/06/20 19:31:31 gRPC服务器启动在端口 2002，配置了连接管理和超时控制
Jun 20 19:31:36 Cat server-dash[24945]: 2025/06/20 19:31:36 ServiceSentinel初始化完成
Jun 20 19:31:36 Cat server-dash[24945]: 2025/06/20 19:31:36 正在创建HTTP服务器...
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /debug/pprof/             --> github.com/gin-contrib/pprof.RouteRegister.WrapF.func1 (3 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /debug/pprof/cmdline      --> github.com/gin-contrib/pprof.RouteRegister.WrapF.func2 (3 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /debug/pprof/profile      --> github.com/gin-contrib/pprof.RouteRegister.WrapF.func3 (3 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] POST   /debug/pprof/symbol       --> github.com/gin-contrib/pprof.RouteRegister.WrapF.func4 (3 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /debug/pprof/symbol       --> github.com/gin-contrib/pprof.RouteRegister.WrapF.func5 (3 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /debug/pprof/trace        --> github.com/gin-contrib/pprof.RouteRegister.WrapF.func6 (3 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /debug/pprof/allocs       --> github.com/gin-contrib/pprof.RouteRegister.WrapH.func7 (3 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /debug/pprof/block        --> github.com/gin-contrib/pprof.RouteRegister.WrapH.func8 (3 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /debug/pprof/goroutine    --> github.com/gin-contrib/pprof.RouteRegister.WrapH.func9 (3 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /debug/pprof/heap         --> github.com/gin-contrib/pprof.RouteRegister.WrapH.func10 (3 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /debug/pprof/mutex        --> github.com/gin-contrib/pprof.RouteRegister.WrapH.func11 (3 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /debug/pprof/threadcreate --> github.com/gin-contrib/pprof.RouteRegister.WrapH.func12 (3 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] [WARNING] Since SetHTMLTemplate() is NOT thread-safe. It should only be called
Jun 20 19:31:36 Cat server-dash[24945]: at initialization. ie. before any route is registered or the router is listening in a socket:
Jun 20 19:31:36 Cat server-dash[24945]:         router := gin.Default()
Jun 20 19:31:36 Cat server-dash[24945]:         router.SetHTMLTemplate(template) // << good place
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /static/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (5 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] HEAD   /static/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (5 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] POST   /view-password            --> github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).issueViewPassword-fm (7 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /terminal/:id             --> github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).terminal-fm (7 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /                         --> github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).home-fm (8 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /service                  --> github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).service-fm (8 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /network/:id              --> github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).network-fm (8 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /network                  --> github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).network-fm (8 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /ws                       --> github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).ws-fm (8 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] POST   /terminal                 --> github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).createTerminal-fm (8 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /file                     --> github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).createFM-fm (8 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /file/:id                 --> github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).fm-fm (8 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /api/traffic              --> github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).apiTraffic-fm (8 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /api/server/:id/traffic   --> github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).apiServerTraffic-fm (8 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /login                    --> github.com/xos/serverstatus/cmd/dashboard/controller.(*guestPage).login-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] POST   /debug-login              --> github.com/xos/serverstatus/cmd/dashboard/controller.(*guestPage).debugLogin-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /oauth2/login             --> github.com/xos/serverstatus/cmd/dashboard/controller.(*oauth2controller).login-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /oauth2/callback          --> github.com/xos/serverstatus/cmd/dashboard/controller.(*oauth2controller).callback-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /server                   --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberPage).server-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /monitor                  --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberPage).monitor-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /cron                     --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberPage).cron-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /notification             --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberPage).notification-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /ddns                     --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberPage).ddns-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /nat                      --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberPage).nat-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /setting                  --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberPage).setting-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /api                      --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberPage).api-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /api/search-server        --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).searchServer-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /api/search-tasks         --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).searchTask-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /api/search-ddns          --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).searchDDNS-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] POST   /api/server               --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).addOrEditServer-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] POST   /api/monitor              --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).addOrEditMonitor-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] POST   /api/traffic              --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).addOrEditAlertRule-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] POST   /api/cron                 --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).addOrEditCron-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /api/cron/:id/manual      --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).manualTrigger-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] POST   /api/force-update         --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).forceUpdate-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] POST   /api/batch-update-server-group --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).batchUpdateServerGroup-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] POST   /api/batch-delete-server  --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).batchDeleteServer-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] POST   /api/notification         --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).addOrEditNotification-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] POST   /api/ddns                 --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).addOrEditDDNS-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] POST   /api/nat                  --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).addOrEditNAT-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] POST   /api/alert-rule           --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).addOrEditAlertRule-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /api/alert-rule/:id       --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).getAlertRule-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] POST   /api/setting              --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).updateSetting-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] DELETE /api/:model/:id           --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).delete-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] POST   /api/logout               --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).logout-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /api/token                --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).getToken-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] POST   /api/token                --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).issueNewToken-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] DELETE /api/token/:token         --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).deleteToken-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /api/v1/server/list       --> github.com/xos/serverstatus/cmd/dashboard/controller.(*apiV1).serverList-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /api/v1/server/details    --> github.com/xos/serverstatus/cmd/dashboard/controller.(*apiV1).serverDetails-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] POST   /api/v1/server/register   --> github.com/xos/serverstatus/cmd/dashboard/controller.(*apiV1).RegisterServer-fm (6 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /api/v1/monitor/:id       --> github.com/xos/serverstatus/cmd/dashboard/controller.(*apiV1).monitorHistoriesById-fm (7 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: [GIN-debug] GET    /api/v1/monitor/configs   --> github.com/xos/serverstatus/cmd/dashboard/controller.(*apiV1).monitorConfigs-fm (7 handlers)
Jun 20 19:31:36 Cat server-dash[24945]: 2025/06/20 19:31:36 HTTP服务器创建完成
Jun 20 19:31:36 Cat server-dash[24945]: 2025/06/20 19:31:36 正在启动HTTP服务器在端口 1001...
Jun 20 19:31:39 Cat server-dash[24945]: 2025/06/20 19:31:39 数据大小限制完成: 状态记录=54, 监控项=9
Jun 20 19:31:40 Cat server-dash[24945]: 2025/06/20 19:31:40 NG>> 尝试发送通知到组 [default]，消息：📊 50%流量使用提醒
Jun 20 19:31:40 Cat server-dash[24945]: 时间: 2025-06-20 19:31:40
Jun 20 19:31:40 Cat server-dash[24945]: 服务器: [MS]新加坡
Jun 20 19:31:40 Cat server-dash[24945]: 报警规则: [AZ]香港|新加坡|圣何塞|伦敦
Jun 20 19:31:40 Cat server-dash[24945]: • 总流量使用情况:
Jun 20 19:31:40 Cat server-dash[24945]:   - 当前使用: 53.32 GB (53.32%)
Jun 20 19:31:40 Cat server-dash[24945]:   - 额定流量: 100.00 GB
Jun 20 19:31:40 Cat server-dash[24945]:   - 剩余流量: 46.68 GB
Jun 20 19:31:40 Cat server-dash[24945]:   - 统计周期: 2025-06-01 00:00:00 - 2025-07-01 00:00:00
Jun 20 19:31:40 Cat server-dash[24945]: 2025/06/20 19:31:40 NG>> 通知方式组 [default] 包含 3 个通知方式
Jun 20 19:31:40 Cat server-dash[24945]: 2025/06/20 19:31:40 NG>> 尝试通知 TG[老王]
Jun 20 19:31:40 Cat server-dash[24945]: 2025/06/20 19:31:40 NG>> 尝试通知 TG[佩佩]
Jun 20 19:31:40 Cat server-dash[24945]: 2025/06/20 19:31:40 NG>> 尝试通知 TG[翠花]
Jun 20 19:31:41 Cat server-dash[24945]: 2025/06/20 19:31:41 NG>> 报警规则检测每小时 1 次 2025-06-20 19:31:41.130667311 +0800 CST m=+12.053081793 2025-06-20 19:31:41.145130731 +0800 CST m=+12.067545214
Jun 20 19:31:41 Cat server-dash[24945]: 2025/06/20 19:31:41 NG>> 向  TG[佩佩]  发送通知成功：
Jun 20 19:31:41 Cat server-dash[24945]: 2025/06/20 19:31:41 NG>> 向  TG[翠花]  发送通知成功：
Jun 20 19:31:41 Cat server-dash[24945]: 2025/06/20 19:31:41 NG>> 向  TG[老王]  发送通知成功：
Jun 20 19:31:42 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:31:42 | 200 |    2.347121ms |  ************** | GET      "/"
Jun 20 19:31:43 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:31:43 | 200 |      72.827µs |  ************** | GET      "/api/search-server?word="
Jun 20 19:31:51 Cat server-dash[24945]: 2025/06/20 19:31:51 服务器 [WiFi]鸽鸽 (ID:26) IP变化 (**************/2409:8949:2c13:895e:1848:55c6:b1f4:c45e -> **************/2409:8949:2c13:895e:1848:55c6:b1f4:c45e)，触发DDNS更新，配置数量: 1
Jun 20 19:31:51 Cat server-dash[24945]: 2025/06/20 19:31:51 NG>> 尝试发送通知到组 [default]，消息：[IP 变更]
Jun 20 19:31:51 Cat server-dash[24945]: 服务器: [WiFi]鸽鸽 (ID:26)
Jun 20 19:31:51 Cat server-dash[24945]: IP变更: **************/2409:8949:2c13:895e:1848:55c6:b1f4:c45e => **************/2409:8949:2c13:895e:1848:55c6:b1f4:c45e
Jun 20 19:31:51 Cat server-dash[24945]: 变更时间: 2025-06-20 19:31:51
Jun 20 19:31:51 Cat server-dash[24945]: 2025/06/20 19:31:51 NG>> 通知方式组 [default] 包含 3 个通知方式
Jun 20 19:31:51 Cat server-dash[24945]: 2025/06/20 19:31:51 NG>> 尝试通知 TG[佩佩]
Jun 20 19:31:51 Cat server-dash[24945]: 2025/06/20 19:31:51 NG>> 尝试通知 TG[翠花]
Jun 20 19:31:51 Cat server-dash[24945]: 2025/06/20 19:31:51 NG>> 尝试通知 TG[老王]
Jun 20 19:31:51 Cat server-dash[24945]: 2025/06/20 19:31:51 正在尝试更新域名(wifi.edge.ip-ddns.com)DDNS(1/3)
Jun 20 19:31:51 Cat server-dash[24945]: 2025/06/20 19:31:51 NG>> 向  TG[佩佩]  发送通知成功：
Jun 20 19:31:51 Cat server-dash[24945]: 2025/06/20 19:31:51 NG>> 向  TG[翠花]  发送通知成功：
Jun 20 19:31:51 Cat server-dash[24945]: 2025/06/20 19:31:51 NG>> 向  TG[老王]  发送通知成功：
Jun 20 19:31:52 Cat server-dash[24945]: 2025/06/20 19:31:52 更新域名(wifi.edge.ip-ddns.com)DDNS成功
Jun 20 19:32:01 Cat server-dash[24945]: 2025/06/20 19:32:01 network: 进入网络页面处理函数
Jun 20 19:32:01 Cat server-dash[24945]: 2025/06/20 19:32:01 network: 使用BadgerDB模式，跳过GORM查询监控历史
Jun 20 19:32:01 Cat server-dash[24945]: 2025/06/20 19:32:01 network: 从监控配置构建监控服务器ID列表
Jun 20 19:32:07 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:32:07 | 200 |   5.76600916s |  ************** | GET      "/network"
Jun 20 19:32:08 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:32:08 | 200 | 24.040840829s |  ************** | GET      "/ws"
Jun 20 19:32:08 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:32:08 | 200 |     153.267µs |  ************** | GET      "/api/v1/monitor/configs"
Jun 20 19:32:08 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:32:08 | 200 |    2.703349ms |  ************** | GET      "/static/webfonts/fa-brands-400.woff2"
Jun 20 19:32:19 Cat server-dash[24945]: 2025/06/20 19:32:19 API /monitor/43 返回 15719 条记录（3天数据，所有监控器）
Jun 20 19:32:19 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:32:19 | 200 |  457.787224ms |  ************** | GET      "/api/v1/monitor/43"
Jun 20 19:32:29 Cat server-dash[24945]: 2025/06/20 19:32:29 API /monitor/40 返回 14787 条记录（3天数据，所有监控器）
Jun 20 19:32:29 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:32:29 | 200 |  365.784009ms |  ************** | GET      "/api/v1/monitor/40"
Jun 20 19:32:31 Cat server-dash[24945]: 2025/06/20 19:32:31 警告：总 goroutine 数量过多 (688)，尝试强制清理
Jun 20 19:32:31 Cat server-dash[24945]: 2025/06/20 19:32:31 清理后 goroutine 数量仍过多 (682)，拒绝新的 RequestTask 连接
Jun 20 19:32:31 Cat server-dash[24945]: 2025/06/20 19:32:31 警告：总 goroutine 数量过多 (752)，尝试强制清理
Jun 20 19:32:31 Cat server-dash[24945]: 2025/06/20 19:32:31 清理后 goroutine 数量仍过多 (808)，拒绝新的 RequestTask 连接
Jun 20 19:32:31 Cat server-dash[24945]: 2025/06/20 19:32:31 API /monitor/4 返回 15143 条记录（3天数据，所有监控器）
Jun 20 19:32:31 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:32:31 | 200 |  428.230403ms |  ************** | GET      "/api/v1/monitor/4"
Jun 20 19:32:34 Cat server-dash[24945]: 2025/06/20 19:32:34 network: 进入网络页面处理函数
Jun 20 19:32:34 Cat server-dash[24945]: 2025/06/20 19:32:34 network: 使用BadgerDB模式，跳过GORM查询监控历史
Jun 20 19:32:34 Cat server-dash[24945]: 2025/06/20 19:32:34 network: 从监控配置构建监控服务器ID列表
Jun 20 19:32:39 Cat server-dash[24945]: 2025/06/20 19:32:39 数据大小限制完成: 状态记录=54, 监控项=9
Jun 20 19:32:40 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:32:40 | 200 |  5.974012615s |  ************** | GET      "/network"
Jun 20 19:32:41 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:32:41 | 200 |     180.137µs |  ************** | GET      "/api/v1/monitor/configs"
Jun 20 19:32:49 Cat server-dash[24945]: 2025/06/20 19:32:49 数据大小限制完成: 状态记录=54, 监控项=9
Jun 20 19:32:51 Cat server-dash[24945]: 2025/06/20 19:32:51 API /monitor/4 返回 15143 条记录（3天数据，所有监控器）
Jun 20 19:32:51 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:32:51 | 200 |  426.171401ms |  ************** | GET      "/api/v1/monitor/4"
Jun 20 19:32:55 Cat server-dash[24945]: 2025/06/20 19:32:55 API /monitor/22 返回 15361 条记录（3天数据，所有监控器）
Jun 20 19:32:55 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:32:55 | 200 |  360.992624ms |  ************** | GET      "/api/v1/monitor/22"
Jun 20 19:32:57 Cat server-dash[24945]: 2025/06/20 19:32:57 API /monitor/8 返回 15253 条记录（3天数据，所有监控器）
Jun 20 19:32:57 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:32:57 | 200 |  348.757944ms |  ************** | GET      "/api/v1/monitor/8"
Jun 20 19:32:59 Cat server-dash[24945]: 2025/06/20 19:32:59 数据大小限制完成: 状态记录=54, 监控项=9
Jun 20 19:33:08 Cat server-dash[24945]: 2025/06/20 19:33:08 警告：总 goroutine 数量过多 (867)，尝试强制清理
Jun 20 19:33:08 Cat server-dash[24945]: 2025/06/20 19:33:08 清理后 goroutine 数量仍过多 (780)，拒绝新的 RequestTask 连接
Jun 20 19:33:08 Cat server-dash[24945]: 2025/06/20 19:33:08 API /monitor/34 返回 15538 条记录（3天数据，所有监控器）
Jun 20 19:33:08 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:33:08 | 200 |  353.913821ms |  ************** | GET      "/api/v1/monitor/34"
Jun 20 19:33:14 Cat server-dash[24945]: 2025/06/20 19:33:14 network: 进入网络页面处理函数
Jun 20 19:33:14 Cat server-dash[24945]: 2025/06/20 19:33:14 network: 使用BadgerDB模式，跳过GORM查询监控历史
Jun 20 19:33:14 Cat server-dash[24945]: 2025/06/20 19:33:14 network: 从监控配置构建监控服务器ID列表
Jun 20 19:33:19 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:33:19 | 200 |  5.150528515s |  ************** | GET      "/network"
Jun 20 19:33:21 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:33:21 | 200 |     162.695µs |  ************** | GET      "/api/v1/monitor/configs"
Jun 20 19:33:29 Cat server-dash[24945]: 2025/06/20 19:33:29 数据大小限制完成: 状态记录=54, 监控项=9
Jun 20 19:33:34 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:33:34 | 200 |    1.634214ms |  142.132.180.39 | GET      "/"
Jun 20 19:33:38 Cat server-dash[24945]: 2025/06/20 19:33:38 API /monitor/5 返回 15488 条记录（3天数据，所有监控器）
Jun 20 19:33:39 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:33:39 | 200 |  372.353298ms |  ************** | GET      "/api/v1/monitor/5"
Jun 20 19:33:44 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:33:44 | 200 |    1.368667ms | 2a03:db40::334b | GET      "/"
Jun 20 19:33:45 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:33:45 | 200 |    1.807971ms |  79.137.143.131 | GET      "/"
Jun 20 19:33:46 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:33:46 | 200 |     347.852µs |  79.137.143.131 | GET      "/static/fontawesome.min.css?v2025032723"
Jun 20 19:33:46 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:33:46 | 200 |     127.039µs |  79.137.143.131 | GET      "/static/main.css?v2025060313"
Jun 20 19:33:50 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:33:50 | 200 |     476.113µs |  79.137.143.131 | GET      "/static/wallpaper.js?v20220423"
Jun 20 19:33:51 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:33:51 | 200 |     552.455µs |  79.137.143.131 | GET      "/static/mixin.js?v20240912"
Jun 20 19:33:51 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:33:51 | 200 |     800.691µs |  79.137.143.131 | GET      "/static/main.js?v2025060722"
Jun 20 19:33:51 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:33:51 | 200 |     182.001µs |  79.137.143.131 | GET      "/static/logo.svg?v20220602"
Jun 20 19:33:52 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:33:52 | 404 |     368.421µs |  79.137.143.131 | OPTIONS  "/api/search-server?word="
Jun 20 19:33:53 Cat server-dash[24945]: 2025/06/20 19:33:53 network: 进入网络页面处理函数
Jun 20 19:33:53 Cat server-dash[24945]: 2025/06/20 19:33:53 network: 使用BadgerDB模式，跳过GORM查询监控历史
Jun 20 19:33:53 Cat server-dash[24945]: 2025/06/20 19:33:53 network: 从监控配置构建监控服务器ID列表
Jun 20 19:33:57 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:33:57 | 200 |  4.794761379s |  ************** | GET      "/network"
Jun 20 19:33:59 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:33:59 | 200 |     174.146µs |  ************** | GET      "/api/v1/monitor/configs"
Jun 20 19:34:00 Cat server-dash[24945]: [GIN] 2025/06/20 - 19:34:00 | 200 |   8.02092004s |  79.137.143.131 | GET      "/ws"
