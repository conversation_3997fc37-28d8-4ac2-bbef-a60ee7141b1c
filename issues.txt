Jun 20 18:44:20 Cat server-dash[21906]: 2025/06/20 18:44:20 network: 进入网络页面处理函数
Jun 20 18:44:20 Cat server-dash[21906]: 2025/06/20 18:44:20 network: 使用BadgerDB模式，跳过GORM查询监控历史
Jun 20 18:44:20 Cat server-dash[21906]: 2025/06/20 18:44:20 network: 从监控配置构建监控服务器ID列表
Jun 20 18:44:26 Cat server-dash[21906]: [GIN] 2025/06/20 - 18:44:26 | 200 |  5.730970199s |  39.144.187.158 | GET      "/network"
Jun 20 18:44:27 Cat server-dash[21906]: [GIN] 2025/06/20 - 18:44:27 | 200 |     193.292µs |  39.144.187.158 | GET      "/api/v1/monitor/configs"
Jun 20 18:44:39 Cat server-dash[21906]: 2025/06/20 18:44:39 数据大小限制完成: 状态记录=54, 监控项=9
Jun 20 18:44:44 Cat server-dash[21906]: [GIN] 2025/06/20 - 18:44:44 | 200 |  5.680796257s |  39.144.187.158 | GET      "/api/v1/monitor/43"
Jun 20 18:44:49 Cat server-dash[21906]: 2025/06/20 18:44:49 数据大小限制完成: 状态记录=54, 监控项=9
Jun 20 18:45:00 Cat server-dash[21906]: 2025/06/20 18:45:00 BadgerDB模式：流量数据一致性检查完成
Jun 20 18:45:00 Cat server-dash[21906]: 2025/06/20 18:45:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 20 18:45:00 Cat server-dash[21906]: 2025/06/20 18:45:00 BadgerDB: 成功保存 4 个用户的数据
Jun 20 18:45:00 Cat server-dash[21906]: 2025/06/20 18:45:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 20 18:45:00 Cat server-dash[21906]: 2025/06/20 18:45:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 20 18:45:08 Cat server-dash[21906]: [GIN] 2025/06/20 - 18:45:08 | 200 |    2.520958ms | 167.235.143.113 | GET      "/"
Jun 20 18:45:26 Cat server-dash[21906]: [GIN] 2025/06/20 - 18:45:26 | 200 |  5.008089019s |  39.144.187.158 | GET      "/api/v1/monitor/41"
Jun 20 18:45:39 Cat server-dash[21906]: 2025/06/20 18:45:39 数据大小限制完成: 状态记录=54, 监控项=9
Jun 20 18:45:41 Cat server-dash[21906]: [GIN] 2025/06/20 - 18:45:41 | 200 |  4.865803098s |  39.144.187.158 | GET      "/api/v1/monitor/4"
Jun 20 18:45:53 Cat server-dash[21906]: [GIN] 2025/06/20 - 18:45:53 | 200 |    1.204359ms |  39.144.187.158 | GET      "/"
Jun 20 18:45:54 Cat server-dash[21906]: [GIN] 2025/06/20 - 18:45:54 | 200 |      70.673µs |  39.144.187.158 | GET      "/api/search-server?word="
Jun 20 18:46:14 Cat server-dash[21906]: [GIN] 2025/06/20 - 18:46:14 | 200 |    1.254081ms |  39.144.187.158 | GET      "/"
Jun 20 18:46:15 Cat server-dash[21906]: [GIN] 2025/06/20 - 18:46:15 | 200 |      47.538µs |  39.144.187.158 | GET      "/api/search-server?word="
Jun 20 18:46:18 Cat server-dash[21906]: [GIN] 2025/06/20 - 18:46:18 | 200 | 24.020507331s |  39.144.187.158 | GET      "/ws"
Jun 20 18:46:27 Cat server-dash[21906]: 2025/06/20 18:46:27 network: 进入网络页面处理函数
Jun 20 18:46:27 Cat server-dash[21906]: 2025/06/20 18:46:27 network: 使用BadgerDB模式，跳过GORM查询监控历史
Jun 20 18:46:27 Cat server-dash[21906]: 2025/06/20 18:46:27 network: 从监控配置构建监控服务器ID列表
Jun 20 18:46:32 Cat server-dash[21906]: [GIN] 2025/06/20 - 18:46:32 | 200 |  5.252098529s |  39.144.187.158 | GET      "/network"
Jun 20 18:46:34 Cat server-dash[21906]: [GIN] 2025/06/20 - 18:46:34 | 200 |     151.674µs |  39.144.187.158 | GET      "/api/v1/monitor/configs"
Jun 20 18:46:35 Cat server-dash[21906]: [GIN] 2025/06/20 - 18:46:35 | 200 | 20.019692667s |  39.144.187.158 | GET      "/ws"
Jun 20 18:47:03 Cat server-dash[21906]: 2025/06/20 18:47:03 警告：总 goroutine 数量过多 (503)，尝试强制清理
Jun 20 18:47:04 Cat server-dash[21906]: 2025/06/20 18:47:04 警告：总 goroutine 数量过多 (604)，尝试强制清理
Jun 20 18:47:04 Cat server-dash[21906]: 2025/06/20 18:47:04 清理后 goroutine 数量仍过多 (604)，拒绝新的 RequestTask 连接
Jun 20 18:47:06 Cat server-dash[21906]: [GIN] 2025/06/20 - 18:47:06 | 200 |  8.754981343s |  39.144.187.158 | GET      "/api/v1/monitor/5"
Jun 20 18:47:06 Cat server-dash[21906]: 2025/06/20 18:47:06 警告：总 goroutine 数量过多 (646)，尝试强制清理
Jun 20 18:47:06 Cat server-dash[21906]: 2025/06/20 18:47:06 清理后 goroutine 数量仍过多 (647)，拒绝新的 RequestTask 连接
Jun 20 18:47:09 Cat server-dash[21906]: 2025/06/20 18:47:09 内存使用超过1000MB (1061MB)，程序即将强制退出避免系统崩溃
Jun 20 18:47:09 Cat server-dash[21906]: 2025/06/20 18:47:09 Goroutine数量: 524
Jun 20 18:47:09 Cat server-dash[21906]: 2025/06/20 18:47:09 堆内存: 1061MB
Jun 20 18:47:09 Cat server-dash[21906]: 2025/06/20 18:47:09 系统内存: 1261MB
Jun 20 18:47:10 Cat server-dash[21906]: [GIN] 2025/06/20 - 18:47:10 | 200 | 11.647456497s |  39.144.187.158 | GET      "/api/v1/monitor/4"
Jun 20 18:47:10 Cat server-dash[21906]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:22474: write: connection reset by peer
Jun 20 18:47:11 Cat server-dash[21906]: [GIN] 2025/06/20 - 18:47:11 | 200 | 11.745235171s |  39.144.187.158 | GET      "/api/v1/monitor/9"
Jun 20 18:47:11 Cat server-dash[21906]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:22504: write: connection reset by peer
Jun 20 18:47:11 Cat server-dash[21906]: [GIN] 2025/06/20 - 18:47:11 | 200 | 12.450936768s |  39.144.187.158 | GET      "/api/v1/monitor/23"
Jun 20 18:47:11 Cat server-dash[21906]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:22488: write: connection reset by peer
Jun 20 18:47:12 Cat systemd[1]: server-dash.service: Main process exited, code=exited, status=1/FAILURE
