Jun 20 18:22:49 Cat server-dash[21338]: 2025/06/20 18:22:49 监控配置数据已在ServiceSentinel初始化时验证
Jun 20 18:22:49 Cat server-dash[21338]: 2025/06/20 18:22:49 报警规则数据已在BadgerDB.FindAll中验证
Jun 20 18:22:49 Cat server-dash[21338]: 2025/06/20 18:22:49 用户数据完整性正常
Jun 20 18:22:49 Cat server-dash[21338]: 2025/06/20 18:22:49 数据完整性验证和修复完成
Jun 20 18:22:51 Cat server-dash[21338]: 2025/06/20 18:22:51 使用BadgerDB，执行BadgerDB监控历史清理...
Jun 20 18:22:51 Cat server-dash[21338]: 2025/06/20 18:22:51 FindAll: 去重后保留 17 个服务器记录
Jun 20 18:22:51 Cat server-dash[21338]: 2025/06/20 18:22:51 BadgerDB监控历史清理完成，清理了0条记录
Jun 20 18:22:51 Cat server-dash[21338]: 2025/06/20 18:22:51 BadgerDB模式：跳过流量数据重新计算
Jun 20 18:22:51 Cat server-dash[21338]: 2025/06/20 18:22:51 正在初始化ServiceSentinel...
Jun 20 18:22:51 Cat server-dash[21338]: 2025/06/20 18:22:51 gRPC服务器启动在端口 2002，配置了连接管理和超时控制
Jun 20 18:22:57 Cat server-dash[21338]: 2025/06/20 18:22:57 ServiceSentinel初始化完成
Jun 20 18:22:57 Cat server-dash[21338]: 2025/06/20 18:22:57 正在创建HTTP服务器...
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /debug/pprof/             --> github.com/gin-contrib/pprof.RouteRegister.WrapF.func1 (3 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /debug/pprof/cmdline      --> github.com/gin-contrib/pprof.RouteRegister.WrapF.func2 (3 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /debug/pprof/profile      --> github.com/gin-contrib/pprof.RouteRegister.WrapF.func3 (3 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] POST   /debug/pprof/symbol       --> github.com/gin-contrib/pprof.RouteRegister.WrapF.func4 (3 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /debug/pprof/symbol       --> github.com/gin-contrib/pprof.RouteRegister.WrapF.func5 (3 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /debug/pprof/trace        --> github.com/gin-contrib/pprof.RouteRegister.WrapF.func6 (3 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /debug/pprof/allocs       --> github.com/gin-contrib/pprof.RouteRegister.WrapH.func7 (3 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /debug/pprof/block        --> github.com/gin-contrib/pprof.RouteRegister.WrapH.func8 (3 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /debug/pprof/goroutine    --> github.com/gin-contrib/pprof.RouteRegister.WrapH.func9 (3 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /debug/pprof/heap         --> github.com/gin-contrib/pprof.RouteRegister.WrapH.func10 (3 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /debug/pprof/mutex        --> github.com/gin-contrib/pprof.RouteRegister.WrapH.func11 (3 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /debug/pprof/threadcreate --> github.com/gin-contrib/pprof.RouteRegister.WrapH.func12 (3 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] [WARNING] Since SetHTMLTemplate() is NOT thread-safe. It should only be called
Jun 20 18:22:57 Cat server-dash[21338]: at initialization. ie. before any route is registered or the router is listening in a socket:
Jun 20 18:22:57 Cat server-dash[21338]:         router := gin.Default()
Jun 20 18:22:57 Cat server-dash[21338]:         router.SetHTMLTemplate(template) // << good place
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /static/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (5 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] HEAD   /static/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (5 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] POST   /view-password            --> github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).issueViewPassword-fm (7 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /terminal/:id             --> github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).terminal-fm (7 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /                         --> github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).home-fm (8 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /service                  --> github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).service-fm (8 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /network/:id              --> github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).network-fm (8 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /network                  --> github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).network-fm (8 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /ws                       --> github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).ws-fm (8 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] POST   /terminal                 --> github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).createTerminal-fm (8 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /file                     --> github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).createFM-fm (8 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /file/:id                 --> github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).fm-fm (8 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /api/traffic              --> github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).apiTraffic-fm (8 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /api/server/:id/traffic   --> github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).apiServerTraffic-fm (8 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /login                    --> github.com/xos/serverstatus/cmd/dashboard/controller.(*guestPage).login-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] POST   /debug-login              --> github.com/xos/serverstatus/cmd/dashboard/controller.(*guestPage).debugLogin-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /oauth2/login             --> github.com/xos/serverstatus/cmd/dashboard/controller.(*oauth2controller).login-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /oauth2/callback          --> github.com/xos/serverstatus/cmd/dashboard/controller.(*oauth2controller).callback-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /server                   --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberPage).server-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /monitor                  --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberPage).monitor-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /cron                     --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberPage).cron-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /notification             --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberPage).notification-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /ddns                     --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberPage).ddns-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /nat                      --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberPage).nat-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /setting                  --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberPage).setting-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /api                      --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberPage).api-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /api/search-server        --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).searchServer-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /api/search-tasks         --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).searchTask-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /api/search-ddns          --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).searchDDNS-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] POST   /api/server               --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).addOrEditServer-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] POST   /api/monitor              --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).addOrEditMonitor-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] POST   /api/traffic              --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).addOrEditAlertRule-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] POST   /api/cron                 --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).addOrEditCron-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /api/cron/:id/manual      --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).manualTrigger-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] POST   /api/force-update         --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).forceUpdate-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] POST   /api/batch-update-server-group --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).batchUpdateServerGroup-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] POST   /api/batch-delete-server  --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).batchDeleteServer-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] POST   /api/notification         --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).addOrEditNotification-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] POST   /api/ddns                 --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).addOrEditDDNS-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] POST   /api/nat                  --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).addOrEditNAT-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] POST   /api/alert-rule           --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).addOrEditAlertRule-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /api/alert-rule/:id       --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).getAlertRule-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] POST   /api/setting              --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).updateSetting-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] DELETE /api/:model/:id           --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).delete-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] POST   /api/logout               --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).logout-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /api/token                --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).getToken-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] POST   /api/token                --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).issueNewToken-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] DELETE /api/token/:token         --> github.com/xos/serverstatus/cmd/dashboard/controller.(*memberAPI).deleteToken-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /api/v1/server/list       --> github.com/xos/serverstatus/cmd/dashboard/controller.(*apiV1).serverList-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /api/v1/server/details    --> github.com/xos/serverstatus/cmd/dashboard/controller.(*apiV1).serverDetails-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] POST   /api/v1/server/register   --> github.com/xos/serverstatus/cmd/dashboard/controller.(*apiV1).RegisterServer-fm (6 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /api/v1/monitor/:id       --> github.com/xos/serverstatus/cmd/dashboard/controller.(*apiV1).monitorHistoriesById-fm (7 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: [GIN-debug] GET    /api/v1/monitor/configs   --> github.com/xos/serverstatus/cmd/dashboard/controller.(*apiV1).monitorConfigs-fm (7 handlers)
Jun 20 18:22:57 Cat server-dash[21338]: 2025/06/20 18:22:57 HTTP服务器创建完成
Jun 20 18:22:57 Cat server-dash[21338]: 2025/06/20 18:22:57 正在启动HTTP服务器在端口 1001...
Jun 20 18:22:59 Cat server-dash[21338]: 2025/06/20 18:22:59 数据大小限制完成: 状态记录=54, 监控项=9
Jun 20 18:23:01 Cat server-dash[21338]: 2025/06/20 18:23:01 NG>> 报警规则检测每小时 1 次 2025-06-20 18:23:01.962610459 +0800 CST m=+12.069475915 2025-06-20 18:23:01.974262769 +0800 CST m=+12.081128233
Jun 20 18:24:54 Cat server-dash[21338]: 2025/06/20 18:24:54 network: 进入网络页面处理函数
Jun 20 18:24:54 Cat server-dash[21338]: 2025/06/20 18:24:54 network: 使用BadgerDB模式，跳过GORM查询监控历史
Jun 20 18:24:54 Cat server-dash[21338]: 2025/06/20 18:24:54 network: 从监控配置构建监控服务器ID列表
Jun 20 18:24:59 Cat server-dash[21338]: 2025/06/20 18:24:59 进入内存高压模式: 内存=387MB, Goroutines=631
Jun 20 18:24:59 Cat server-dash[21338]: 2025/06/20 18:24:59 执行适度内存清理...
Jun 20 18:24:59 Cat server-dash[21338]: 2025/06/20 18:24:59 内存清理完成，清理时间：2025-06-20 18:24:59.945617148 +0800 CST m=+130.052482613
Jun 20 18:25:00 Cat server-dash[21338]: 2025/06/20 18:25:00 BadgerDB模式：流量数据一致性检查完成
Jun 20 18:25:00 Cat server-dash[21338]: 2025/06/20 18:25:00 network: 进入网络页面处理函数
Jun 20 18:25:00 Cat server-dash[21338]: 2025/06/20 18:25:00 network: 使用BadgerDB模式，跳过GORM查询监控历史
Jun 20 18:25:00 Cat server-dash[21338]: 2025/06/20 18:25:00 network: 从监控配置构建监控服务器ID列表
