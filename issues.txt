Jun 19 19:35:30 Cat server-dash[3398]: fatal error: concurrent map writes
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 6896024 [running]:
Jun 19 19:35:30 Cat server-dash[3398]: internal/runtime/maps.fatal({0x143907f?, 0xc017afb280?})
Jun 19 19:35:30 Cat server-dash[3398]:         runtime/panic.go:1058 +0x18
Jun 19 19:35:30 Cat server-dash[3398]: github.com/xos/serverstatus/service/singleton.UpdateTrafficStats(0x9, 0x8a22aa60, 0x74a80f7d)
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/service/singleton/alertsentinel.go:430 +0x41b
Jun 19 19:35:30 Cat server-dash[3398]: github.com/xos/serverstatus/service/rpc.updateTrafficDisplay(...)
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/service/rpc/server.go:857
Jun 19 19:35:30 Cat server-dash[3398]: github.com/xos/serverstatus/service/rpc.(*ServerHandler).processServerStateWithoutLock(0xc00d7ca000, 0x9, 0xc017afb690, 0xc006da57c0, 0x0, 0xc017afb5f8)
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/service/rpc/server.go:544 +0xc0b
Jun 19 19:35:30 Cat server-dash[3398]: github.com/xos/serverstatus/service/rpc.(*ServerHandler).ReportSystemState(0xc00d7ca000, {0x359ae08?, 0xc006de2fc0?}, 0xc00653ed80)
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/service/rpc/server.go:401 +0x873
Jun 19 19:35:30 Cat server-dash[3398]: github.com/xos/serverstatus/proto._ServerService_ReportSystemState_Handler({0x13bdfa0, 0xc00d7ca000}, {0x359ae08, 0xc006de2fc0}, 0xc0032a9b00, 0x0)
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/proto/server_grpc.pb.go:201 +0x1a6
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).processUnaryRPC(0xc00d2a0200, {0x359ae08, 0xc006de2f30}, 0xc006d1a9c0, 0xc00d7a8330, 0x3df1580, 0x0)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1405 +0x1036
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).handleStream(0xc00d2a0200, {0x359b8e8, 0xc00da4e1a0}, 0xc006d1a9c0)
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1815 +0xb88
Jun 19 19:35:30 Cat server-dash[3398]: google.golang.org/grpc.(*Server).serveStreams.func2.1()
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1035 +0x7f
Jun 19 19:35:30 Cat server-dash[3398]: created by google.golang.org/grpc.(*Server).serveStreams.func2 in goroutine 6894987
Jun 19 19:35:30 Cat server-dash[3398]:         google.golang.org/grpc@v1.72.0/server.go:1046 +0x11d
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 1 [IO wait, 2 minutes]:
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.runtime_pollWait(0x7f539368fd98, 0x72)
Jun 19 19:35:30 Cat server-dash[3398]:         runtime/netpoll.go:351 +0x85
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*pollDesc).wait(0xc00028a300?, 0x900000036?, 0x0)
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_poll_runtime.go:84 +0x27
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*pollDesc).waitRead(...)
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_poll_runtime.go:89
Jun 19 19:35:30 Cat server-dash[3398]: internal/poll.(*FD).Accept(0xc00028a300)
Jun 19 19:35:30 Cat server-dash[3398]:         internal/poll/fd_unix.go:620 +0x295
Jun 19 19:35:30 Cat server-dash[3398]: net.(*netFD).accept(0xc00028a300)
Jun 19 19:35:30 Cat server-dash[3398]:         net/fd_unix.go:172 +0x29
Jun 19 19:35:30 Cat server-dash[3398]: net.(*TCPListener).accept(0xc0048f3100)
Jun 19 19:35:30 Cat server-dash[3398]:         net/tcpsock_posix.go:159 +0x1b
Jun 19 19:35:30 Cat server-dash[3398]: net.(*TCPListener).Accept(0xc0048f3100)
Jun 19 19:35:30 Cat server-dash[3398]:         net/tcpsock.go:380 +0x30
Jun 19 19:35:30 Cat server-dash[3398]: net/http.(*Server).Serve(0xc0001fd700, {0x3598f68, 0xc0048f3100})
Jun 19 19:35:30 Cat server-dash[3398]:         net/http/server.go:3424 +0x30c
Jun 19 19:35:30 Cat server-dash[3398]: net/http.(*Server).ListenAndServe(0xc0001fd700)
Jun 19 19:35:30 Cat server-dash[3398]:         net/http/server.go:3350 +0x71
Jun 19 19:35:30 Cat server-dash[3398]: main.main.func5()
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/cmd/dashboard/main.go:190 +0x17
Jun 19 19:35:30 Cat server-dash[3398]: github.com/ory/graceful.Graceful(0xc004f0dec8, 0xc00234ff38)
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/ory/graceful@v0.1.3/http_graceful.go:76 +0xaf
Jun 19 19:35:30 Cat server-dash[3398]: main.main()
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/cmd/dashboard/main.go:189 +0x90e
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 18 [select, 5 minutes]:
Jun 19 19:35:30 Cat server-dash[3398]: github.com/patrickmn/go-cache.(*janitor).Run(0xc00023cb70, 0xc00021ec00)
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/patrickmn/go-cache@v2.1.0+incompatible/cache.go:1079 +0x7b
Jun 19 19:35:30 Cat server-dash[3398]: created by github.com/patrickmn/go-cache.runJanitor in goroutine 1
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/patrickmn/go-cache@v2.1.0+incompatible/cache.go:1099 +0xdb
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 19 [select, 5 minutes]:
Jun 19 19:35:30 Cat server-dash[3398]: github.com/patrickmn/go-cache.(*janitor).Run(0xc00023cb80, 0xc00021ec40)
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/patrickmn/go-cache@v2.1.0+incompatible/cache.go:1079 +0x7b
Jun 19 19:35:30 Cat server-dash[3398]: created by github.com/patrickmn/go-cache.runJanitor in goroutine 1
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/patrickmn/go-cache@v2.1.0+incompatible/cache.go:1099 +0xdb
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 20 [chan receive, 5 minutes]:
Jun 19 19:35:30 Cat server-dash[3398]: github.com/xos/serverstatus/service/singleton.InitTimezoneAndCache.func1()
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/service/singleton/singleton.go:58 +0xa5
Jun 19 19:35:30 Cat server-dash[3398]: created by github.com/xos/serverstatus/service/singleton.InitTimezoneAndCache in goroutine 1
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/xos/serverstatus/service/singleton/singleton.go:54 +0xf4
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 21 [select]:
Jun 19 19:35:30 Cat server-dash[3398]: github.com/dgraph-io/badger/v3/y.(*WaterMark).process(0xc000243ef0, 0xc000243e30)
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/dgraph-io/badger/v3@v3.2103.5/y/watermark.go:214 +0x267
Jun 19 19:35:30 Cat server-dash[3398]: created by github.com/dgraph-io/badger/v3/y.(*WaterMark).Init in goroutine 1
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/dgraph-io/badger/v3@v3.2103.5/y/watermark.go:72 +0xa7
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 22 [select]:
Jun 19 19:35:30 Cat server-dash[3398]: github.com/dgraph-io/badger/v3/y.(*WaterMark).process(0xc00035c060, 0xc000243e30)
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/dgraph-io/badger/v3@v3.2103.5/y/watermark.go:214 +0x267
Jun 19 19:35:30 Cat server-dash[3398]: created by github.com/dgraph-io/badger/v3/y.(*WaterMark).Init in goroutine 1
Jun 19 19:35:30 Cat server-dash[3398]:         github.com/dgraph-io/badger/v3@v3.2103.5/y/watermark.go:72 +0xa7
Jun 19 19:35:30 Cat server-dash[3398]: goroutine 23 [select]:

还有：
"Jun 20 00:06:55 Cat server-dash[5954]: 2025/06/20 00:06:55 数据大小限制完成: 状态记录=54, 监控项=9
Jun 20 00:09:46 Cat server-dash[5954]: [GIN] 2025/06/20 - 00:09:46 | 200 |     900.078µs |    ************ | GET      "/"
Jun 20 00:09:46 Cat server-dash[5954]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:50482: write: broken pipe
Jun 20 00:10:00 Cat server-dash[5954]: 2025/06/20 00:10:00 BadgerDB模式：流量数据一致性检查完成
Jun 20 00:10:00 Cat server-dash[5954]: 2025/06/20 00:10:00 执行监控历史记录一致性检查...
Jun 20 00:10:00 Cat server-dash[5954]: 2025/06/20 00:10:00 BadgerDB模式：监控历史记录检查完成
Jun 20 00:10:00 Cat server-dash[5954]: 2025/06/20 00:10:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 20 00:10:00 Cat server-dash[5954]: 2025/06/20 00:10:00 BadgerDB: 成功保存 4 个用户的数据
Jun 20 00:10:00 Cat server-dash[5954]: 2025/06/20 00:10:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 20 00:10:00 Cat server-dash[5954]: 2025/06/20 00:10:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 20 00:10:00 Cat server-dash[5954]: 2025/06/20 00:10:00 BadgerDB: 成功保存 17 个服务器的累计流量数据
Jun 20 00:10:03 Cat server-dash[5954]: [GIN] 2025/06/20 - 00:10:03 | 200 |         3m40s |  ************** | GET      "/ws"
Jun 20 00:11:09 Cat server-dash[5954]: panic: runtime error: invalid memory address or nil pointer dereference
Jun 20 00:11:09 Cat server-dash[5954]: [signal SIGSEGV: segmentation violation code=0x1 addr=0x0 pc=0xc4ad3b]
Jun 20 00:11:09 Cat server-dash[5954]: goroutine 5467837 [running]:
Jun 20 00:11:09 Cat server-dash[5954]: gorm.io/gorm.(*DB).Session(0x0, 0xc01164bd20)
Jun 20 00:11:09 Cat server-dash[5954]:         gorm.io/gorm@v1.26.0/gorm.go:235 +0x3b
Jun 20 00:11:09 Cat server-dash[5954]: gorm.io/gorm.(*DB).WithContext(...)
Jun 20 00:11:09 Cat server-dash[5954]:         gorm.io/gorm@v1.26.0/gorm.go:336
Jun 20 00:11:09 Cat server-dash[5954]: github.com/xos/serverstatus/service/singleton.executeDBWriteRequest.func1()
Jun 20 00:11:09 Cat server-dash[5954]:         github.com/xos/serverstatus/service/singleton/singleton.go:2162 +0xb0
Jun 20 00:11:09 Cat server-dash[5954]: github.com/xos/serverstatus/service/singleton.executeWithAdvancedRetry(0xc01164beb8, 0x5, 0x2faf080, 0x12a05f200)
Jun 20 00:11:09 Cat server-dash[5954]:         github.com/xos/serverstatus/service/singleton/singleton.go:599 +0x124
Jun 20 00:11:09 Cat server-dash[5954]: github.com/xos/serverstatus/service/singleton.ExecuteWithRetry(...)
Jun 20 00:11:09 Cat server-dash[5954]:         github.com/xos/serverstatus/service/singleton/singleton.go:593
Jun 20 00:11:09 Cat server-dash[5954]: github.com/xos/serverstatus/service/singleton.executeDBWriteRequest({0x1a, {0x14272e5, 0x7}, 0xc00d378a20, 0xc006af49a0})
Jun 20 00:11:09 Cat server-dash[5954]:         github.com/xos/serverstatus/service/singleton/singleton.go:2157 +0x88
Jun 20 00:11:09 Cat server-dash[5954]: github.com/xos/serverstatus/service/singleton.AsyncDBUpdate.func1()
Jun 20 00:11:09 Cat server-dash[5954]:         github.com/xos/serverstatus/service/singleton/singleton.go:2247 +0x14a
Jun 20 00:11:09 Cat server-dash[5954]: created by github.com/xos/serverstatus/service/singleton.AsyncDBUpdate in goroutine 5467862
Jun 20 00:11:09 Cat server-dash[5954]:         github.com/xos/serverstatus/service/singleton/singleton.go:2234 +0x118
Jun 20 00:11:09 Cat systemd[1]: server-dash.service: Main process exited, code=exited, status=2/INVALIDARGUMENT
Jun 20 00:11:09 Cat systemd[1]: server-dash.service: Failed with result 'exit-code'.
Jun 20 00:11:09 Cat systemd[1]: server-dash.service: Consumed 5min 12.056s CPU time.";

还有：
"Jun 20 01:40:00 Cat server-dash[20498]: 2025/06/20 01:40:00 BadgerDB模式：流量数据一致性检查完成
Jun 20 01:40:00 Cat server-dash[20498]: 2025/06/20 01:40:00 执行监控历史记录一致性检查...
Jun 20 01:40:00 Cat server-dash[20498]: 2025/06/20 01:40:00 BadgerDB模式：监控历史记录检查完成
Jun 20 01:40:00 Cat server-dash[20498]: 2025/06/20 01:40:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 20 01:40:00 Cat server-dash[20498]: 2025/06/20 01:40:00 BadgerDB: 成功保存 4 个用户的数据
Jun 20 01:40:00 Cat server-dash[20498]: 2025/06/20 01:40:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 20 01:40:00 Cat server-dash[20498]: 2025/06/20 01:40:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 20 01:40:00 Cat server-dash[20498]: 2025/06/20 01:40:00 BadgerDB: 成功保存 17 个服务器的累计流量数据
Jun 20 01:40:30 Cat server-dash[20498]: [GIN] 2025/06/20 - 01:40:30 | 200 |      47.249µs |  ************** | GET      "/api/search-server?word="
Jun 20 01:40:30 Cat server-dash[20498]: [GIN] 2025/06/20 - 01:40:30 | 200 |     171.802µs |  ************** | GET      "/static/logo.svg?v20220602"
Jun 20 01:40:30 Cat server-dash[20498]: [GIN] 2025/06/20 - 01:40:30 | 404 |     183.444µs |  ************** | GET      "/apple-touch-icon-precomposed.png"
Jun 20 01:40:30 Cat server-dash[20498]: [GIN] 2025/06/20 - 01:40:30 | 200 |      138.66µs |  ************** | GET      "/static/logo.svg?v20220602"
Jun 20 01:40:30 Cat server-dash[20498]: [GIN] 2025/06/20 - 01:40:30 | 404 |      299.05µs |  ************** | GET      "/favicon.ico"
Jun 20 01:40:30 Cat server-dash[20498]: [GIN] 2025/06/20 - 01:40:30 | 404 |     360.937µs |  ************** | GET      "/apple-touch-icon.png"
Jun 20 01:40:46 Cat server-dash[20498]: [GIN] 2025/06/20 - 01:40:46 | 200 | 16.031253579s |  ************** | GET      "/ws"
Jun 20 01:41:17 Cat server-dash[20498]: 2025/06/20 01:41:17 数据大小限制完成: 状态记录=54, 监控项=9
Jun 20 01:41:19 Cat server-dash[20498]: [GIN] 2025/06/20 - 01:41:19 | 200 |     948.097µs |  ************** | GET      "/"
Jun 20 01:41:19 Cat server-dash[20498]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:60774: write: broken pipe
Jun 20 01:44:54 Cat server-dash[20498]: fatal error: concurrent map writes
Jun 20 01:44:54 Cat server-dash[20498]: goroutine 3589736 [running]:
Jun 20 01:44:54 Cat server-dash[20498]: internal/runtime/maps.fatal({0x143807f?, 0xc01123b280?})
Jun 20 01:44:54 Cat server-dash[20498]:         runtime/panic.go:1058 +0x18
Jun 20 01:44:54 Cat server-dash[20498]: github.com/xos/serverstatus/service/singleton.UpdateTrafficStats(0x9, 0x8bbfbf93, 0x75b45406)
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/xos/serverstatus/service/singleton/alertsentinel.go:434 +0x44a
Jun 20 01:44:54 Cat server-dash[20498]: github.com/xos/serverstatus/service/rpc.updateTrafficDisplay(...)
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/xos/serverstatus/service/rpc/server.go:862
Jun 20 01:44:54 Cat server-dash[20498]: github.com/xos/serverstatus/service/rpc.(*ServerHandler).processServerStateWithoutLock(0xc012073038, 0x9, 0xc01123b690, 0xc007ec0000, 0x0, 0xc01123b5f8)
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/xos/serverstatus/service/rpc/server.go:549 +0xc0b
Jun 20 01:44:54 Cat server-dash[20498]: github.com/xos/serverstatus/service/rpc.(*ServerHandler).ReportSystemState(0xc012073038, {0x3599a08?, 0xc0077dac60?}, 0xc007ebe000)
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/xos/serverstatus/service/rpc/server.go:406 +0x873
Jun 20 01:44:54 Cat server-dash[20498]: github.com/xos/serverstatus/proto._ServerService_ReportSystemState_Handler({0x13bcfa0, 0xc012073038}, {0x3599a08, 0xc0077dac60}, 0xc00624f400, 0x0)
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/xos/serverstatus/proto/server_grpc.pb.go:201 +0x1a6
Jun 20 01:44:54 Cat server-dash[20498]: google.golang.org/grpc.(*Server).processUnaryRPC(0xc00235e000, {0x3599a08, 0xc007f0a300}, 0xc00f69ae40, 0xc008b94690, 0x3def580, 0x0)
Jun 20 01:44:54 Cat server-dash[20498]:         google.golang.org/grpc@v1.72.0/server.go:1405 +0x1036
Jun 20 01:44:54 Cat server-dash[20498]: google.golang.org/grpc.(*Server).handleStream(0xc00235e000, {0x359a4e8, 0xc0053f36c0}, 0xc00f69ae40)
Jun 20 01:44:54 Cat server-dash[20498]:         google.golang.org/grpc@v1.72.0/server.go:1815 +0xb88
Jun 20 01:44:54 Cat server-dash[20498]: google.golang.org/grpc.(*Server).serveStreams.func2.1()
Jun 20 01:44:54 Cat server-dash[20498]:         google.golang.org/grpc@v1.72.0/server.go:1035 +0x7f
Jun 20 01:44:54 Cat server-dash[20498]: created by google.golang.org/grpc.(*Server).serveStreams.func2 in goroutine 3586846
Jun 20 01:44:54 Cat server-dash[20498]:         google.golang.org/grpc@v1.72.0/server.go:1046 +0x11d
Jun 20 01:44:54 Cat server-dash[20498]: goroutine 1 [IO wait, 3 minutes]:
Jun 20 01:44:54 Cat server-dash[20498]: internal/poll.runtime_pollWait(0x7ffa5f2c6d98, 0x72)
Jun 20 01:44:54 Cat server-dash[20498]:         runtime/netpoll.go:351 +0x85
Jun 20 01:44:54 Cat server-dash[20498]: internal/poll.(*pollDesc).wait(0xc01167f300?, 0x900000036?, 0x0)
Jun 20 01:44:54 Cat server-dash[20498]:         internal/poll/fd_poll_runtime.go:84 +0x27
Jun 20 01:44:54 Cat server-dash[20498]: internal/poll.(*pollDesc).waitRead(...)
Jun 20 01:44:54 Cat server-dash[20498]:         internal/poll/fd_poll_runtime.go:89
Jun 20 01:44:54 Cat server-dash[20498]: internal/poll.(*FD).Accept(0xc01167f300)
Jun 20 01:44:54 Cat server-dash[20498]:         internal/poll/fd_unix.go:620 +0x295
Jun 20 01:44:54 Cat server-dash[20498]: net.(*netFD).accept(0xc01167f300)
Jun 20 01:44:54 Cat server-dash[20498]:         net/fd_unix.go:172 +0x29
Jun 20 01:44:54 Cat server-dash[20498]: net.(*TCPListener).accept(0xc0032da080)
Jun 20 01:44:54 Cat server-dash[20498]:         net/tcpsock_posix.go:159 +0x1b
Jun 20 01:44:54 Cat server-dash[20498]: net.(*TCPListener).Accept(0xc0032da080)
Jun 20 01:44:54 Cat server-dash[20498]:         net/tcpsock.go:380 +0x30
Jun 20 01:44:54 Cat server-dash[20498]: net/http.(*Server).Serve(0xc00308ee00, {0x3597b68, 0xc0032da080})
Jun 20 01:44:54 Cat server-dash[20498]:         net/http/server.go:3424 +0x30c
Jun 20 01:44:54 Cat server-dash[20498]: net/http.(*Server).ListenAndServe(0xc00308ee00)
Jun 20 01:44:54 Cat server-dash[20498]:         net/http/server.go:3350 +0x71
Jun 20 01:44:54 Cat server-dash[20498]: main.main.func5()
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/xos/serverstatus/cmd/dashboard/main.go:190 +0x17
Jun 20 01:44:54 Cat server-dash[20498]: github.com/ory/graceful.Graceful(0xc000041ec8, 0xc022461cf8)
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/ory/graceful@v0.1.3/http_graceful.go:76 +0xaf
Jun 20 01:44:54 Cat server-dash[20498]: main.main()
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/xos/serverstatus/cmd/dashboard/main.go:189 +0x90e
Jun 20 01:44:54 Cat server-dash[20498]: goroutine 36 [select, 10 minutes]:
Jun 20 01:44:54 Cat server-dash[20498]: github.com/patrickmn/go-cache.(*janitor).Run(0xc002343ad0, 0xc00021ebc0)
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/patrickmn/go-cache@v2.1.0+incompatible/cache.go:1079 +0x7b
Jun 20 01:44:54 Cat server-dash[20498]: created by github.com/patrickmn/go-cache.runJanitor in goroutine 1
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/patrickmn/go-cache@v2.1.0+incompatible/cache.go:1099 +0xdb
Jun 20 01:44:54 Cat server-dash[20498]: goroutine 37 [select, 4 minutes]:
Jun 20 01:44:54 Cat server-dash[20498]: github.com/patrickmn/go-cache.(*janitor).Run(0xc002343ae0, 0xc00021ec00)
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/patrickmn/go-cache@v2.1.0+incompatible/cache.go:1079 +0x7b
Jun 20 01:44:54 Cat server-dash[20498]: created by github.com/patrickmn/go-cache.runJanitor in goroutine 1
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/patrickmn/go-cache@v2.1.0+incompatible/cache.go:1099 +0xdb
Jun 20 01:44:54 Cat server-dash[20498]: goroutine 38 [chan receive, 24 minutes]:
Jun 20 01:44:54 Cat server-dash[20498]: github.com/xos/serverstatus/service/singleton.InitTimezoneAndCache.func1()
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/xos/serverstatus/service/singleton/singleton.go:58 +0xa5
Jun 20 01:44:54 Cat server-dash[20498]: created by github.com/xos/serverstatus/service/singleton.InitTimezoneAndCache in goroutine 1
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/xos/serverstatus/service/singleton/singleton.go:54 +0xf4
Jun 20 01:44:54 Cat server-dash[20498]: goroutine 39 [select]:
Jun 20 01:44:54 Cat server-dash[20498]: github.com/dgraph-io/badger/v3/y.(*WaterMark).process(0xc000134b10, 0xc000134ae0)
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/dgraph-io/badger/v3@v3.2103.5/y/watermark.go:214 +0x267
Jun 20 01:44:54 Cat server-dash[20498]: created by github.com/dgraph-io/badger/v3/y.(*WaterMark).Init in goroutine 1
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/dgraph-io/badger/v3@v3.2103.5/y/watermark.go:72 +0xa7
Jun 20 01:44:54 Cat server-dash[20498]: goroutine 40 [select]:
Jun 20 01:44:54 Cat server-dash[20498]: github.com/dgraph-io/badger/v3/y.(*WaterMark).process(0xc000134b40, 0xc000134ae0)
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/dgraph-io/badger/v3@v3.2103.5/y/watermark.go:214 +0x267
Jun 20 01:44:54 Cat server-dash[20498]: created by github.com/dgraph-io/badger/v3/y.(*WaterMark).Init in goroutine 1
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/dgraph-io/badger/v3@v3.2103.5/y/watermark.go:72 +0xa7
Jun 20 01:44:54 Cat server-dash[20498]: goroutine 41 [select]:
Jun 20 01:44:54 Cat server-dash[20498]: github.com/dgraph-io/ristretto/z.(*AllocatorPool).freeupAllocators(0xc0023fa378)
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/dgraph-io/ristretto@v0.2.0/z/allocator.go:385 +0x113
Jun 20 01:44:54 Cat server-dash[20498]: created by github.com/dgraph-io/ristretto/z.NewAllocatorPool in goroutine 1
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/dgraph-io/ristretto@v0.2.0/z/allocator.go:324 +0xb9
Jun 20 01:44:54 Cat server-dash[20498]: goroutine 42 [select, 4 minutes]:
Jun 20 01:44:54 Cat server-dash[20498]: github.com/dgraph-io/ristretto.(*defaultPolicy).processItems(0xc002372600)
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/dgraph-io/ristretto@v0.2.0/policy.go:101 +0x7f
Jun 20 01:44:54 Cat server-dash[20498]: created by github.com/dgraph-io/ristretto.newDefaultPolicy in goroutine 1
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/dgraph-io/ristretto@v0.2.0/policy.go:85 +0x139
Jun 20 01:44:54 Cat server-dash[20498]: goroutine 43 [select]:
Jun 20 01:44:54 Cat server-dash[20498]: github.com/dgraph-io/ristretto.(*Cache).processItems(0xc0001bb380)
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/dgraph-io/ristretto@v0.2.0/cache.go:459 +0x112
Jun 20 01:44:54 Cat server-dash[20498]: created by github.com/dgraph-io/ristretto.NewCache in goroutine 1
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/dgraph-io/ristretto@v0.2.0/cache.go:211 +0x669
Jun 20 01:44:54 Cat server-dash[20498]: goroutine 44 [select, 1 minutes]:
Jun 20 01:44:54 Cat server-dash[20498]: github.com/dgraph-io/badger/v3.(*DB).monitorCache(0xc000288908, 0xc002412d80)
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/dgraph-io/badger/v3@v3.2103.5/db.go:469 +0x15d
Jun 20 01:44:54 Cat server-dash[20498]: created by github.com/dgraph-io/badger/v3.Open in goroutine 1
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/dgraph-io/badger/v3@v3.2103.5/db.go:311 +0xc09
Jun 20 01:44:54 Cat server-dash[20498]: goroutine 45 [select, 1 minutes]:
Jun 20 01:44:54 Cat server-dash[20498]: github.com/dgraph-io/badger/v3.(*DB).updateSize(0xc000288908, 0xc002412e40)
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/dgraph-io/badger/v3@v3.2103.5/db.go:1171 +0x13e
Jun 20 01:44:54 Cat server-dash[20498]: created by github.com/dgraph-io/badger/v3.Open in goroutine 1
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/dgraph-io/badger/v3@v3.2103.5/db.go:331 +0xe16
Jun 20 01:44:54 Cat server-dash[20498]: goroutine 24 [select]:
Jun 20 01:44:54 Cat server-dash[20498]: github.com/dgraph-io/badger/v3.(*levelsController).runCompactor(0xc000220150, 0x0, 0xc00035a030)
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/dgraph-io/badger/v3@v3.2103.5/levels.go:508 +0x229
Jun 20 01:44:54 Cat server-dash[20498]: created by github.com/dgraph-io/badger/v3.(*levelsController).startCompact in goroutine 1
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/dgraph-io/badger/v3@v3.2103.5/levels.go:354 +0x58
Jun 20 01:44:54 Cat server-dash[20498]: goroutine 25 [select]:
Jun 20 01:44:54 Cat server-dash[20498]: github.com/dgraph-io/badger/v3.(*levelsController).runCompactor(0xc000220150, 0x1, 0xc00035a030)
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/dgraph-io/badger/v3@v3.2103.5/levels.go:508 +0x229
Jun 20 01:44:54 Cat server-dash[20498]: created by github.com/dgraph-io/badger/v3.(*levelsController).startCompact in goroutine 1
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/dgraph-io/badger/v3@v3.2103.5/levels.go:354 +0x58
Jun 20 01:44:54 Cat server-dash[20498]: goroutine 26 [select]:
Jun 20 01:44:54 Cat server-dash[20498]: github.com/dgraph-io/badger/v3.(*levelsController).runCompactor(0xc000220150, 0x2, 0xc00035a030)
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/dgraph-io/badger/v3@v3.2103.5/levels.go:508 +0x229
Jun 20 01:44:54 Cat server-dash[20498]: created by github.com/dgraph-io/badger/v3.(*levelsController).startCompact in goroutine 1
Jun 20 01:44:54 Cat server-dash[20498]:         github.com/dgraph-io/badger/v3@v3.2103.5/levels.go:354 +0x58
Jun 20 01:44:54 Cat server-dash[20498]: goroutine 27 [select]:
Jun 20 01:44:54 Cat server-dash[20498]: github.com/dgraph-io/badger/v3.(*levelsController).runCompactor(0xc000220150, 0x3, 0xc00035a030)";

还有：
"Jun 20 05:20:00 Cat server-dash[26443]: 2025/06/20 05:20:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 20 05:20:00 Cat server-dash[26443]: 2025/06/20 05:20:00 BadgerDB: 成功保存 17 个服务器的累计流量数据
Jun 20 05:24:59 Cat server-dash[26443]: [GIN] 2025/06/20 - 05:24:59 | 200 |    1.057472ms |  ************** | GET      "/"
Jun 20 05:24:59 Cat server-dash[26443]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:24518: write: broken pipe
Jun 20 05:25:00 Cat server-dash[26443]: 2025/06/20 05:25:00 BadgerDB模式：流量数据一致性检查完成
Jun 20 05:25:00 Cat server-dash[26443]: 2025/06/20 05:25:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 20 05:25:00 Cat server-dash[26443]: 2025/06/20 05:25:00 BadgerDB: 成功保存 4 个用户的数据
Jun 20 05:25:00 Cat server-dash[26443]: 2025/06/20 05:25:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 20 05:25:00 Cat server-dash[26443]: 2025/06/20 05:25:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 20 05:25:16 Cat server-dash[26443]: panic: runtime error: invalid memory address or nil pointer dereference
Jun 20 05:25:16 Cat server-dash[26443]: [signal SIGSEGV: segmentation violation code=0x1 addr=0x0 pc=0xc4ad3b]
Jun 20 05:25:16 Cat server-dash[26443]: goroutine 1087722 [running]:
Jun 20 05:25:16 Cat server-dash[26443]: gorm.io/gorm.(*DB).Session(0x0, 0xc00dcced20)
Jun 20 05:25:16 Cat server-dash[26443]:         gorm.io/gorm@v1.26.0/gorm.go:235 +0x3b
Jun 20 05:25:16 Cat server-dash[26443]: gorm.io/gorm.(*DB).WithContext(...)
Jun 20 05:25:16 Cat server-dash[26443]:         gorm.io/gorm@v1.26.0/gorm.go:336
Jun 20 05:25:16 Cat server-dash[26443]: github.com/xos/serverstatus/service/singleton.executeDBWriteRequest.func1()
Jun 20 05:25:16 Cat server-dash[26443]:         github.com/xos/serverstatus/service/singleton/singleton.go:2162 +0xb0
Jun 20 05:25:16 Cat server-dash[26443]: github.com/xos/serverstatus/service/singleton.executeWithAdvancedRetry(0xc00dcceeb8, 0x5, 0x2faf080, 0x12a05f200)
Jun 20 05:25:16 Cat server-dash[26443]:         github.com/xos/serverstatus/service/singleton/singleton.go:599 +0x124
Jun 20 05:25:16 Cat server-dash[26443]: github.com/xos/serverstatus/service/singleton.ExecuteWithRetry(...)
Jun 20 05:25:16 Cat server-dash[26443]:         github.com/xos/serverstatus/service/singleton/singleton.go:593
Jun 20 05:25:16 Cat server-dash[26443]: github.com/xos/serverstatus/service/singleton.executeDBWriteRequest({0xe, {0x14272e5, 0x7}, 0xc006070750, 0xc00df90620})
Jun 20 05:25:16 Cat server-dash[26443]:         github.com/xos/serverstatus/service/singleton/singleton.go:2157 +0x88
Jun 20 05:25:16 Cat server-dash[26443]: github.com/xos/serverstatus/service/singleton.AsyncDBUpdate.func1()
Jun 20 05:25:16 Cat server-dash[26443]:         github.com/xos/serverstatus/service/singleton/singleton.go:2247 +0x14a
Jun 20 05:25:16 Cat server-dash[26443]: created by github.com/xos/serverstatus/service/singleton.AsyncDBUpdate in goroutine 1087766
Jun 20 05:25:16 Cat server-dash[26443]:         github.com/xos/serverstatus/service/singleton/singleton.go:2234 +0x118
Jun 20 05:25:16 Cat systemd[1]: server-dash.service: Main process exited, code=exited, status=2/INVALIDARGUMENT
Jun 20 05:25:16 Cat systemd[1]: server-dash.service: Failed with result 'exit-code'.
Jun 20 05:25:16 Cat systemd[1]: server-dash.service: Consumed 4min 39.979s CPU time.
Jun 20 05:25:16 Cat systemd[1]: server-dash.service: Scheduled restart job, restart counter is at 3.
Jun 20 05:25:16 Cat systemd[1]: Stopped server-dash.service - Server Status Dashborad.";

还有：
"Jun 20 09:25:00 Cat server-dash[52333]: 2025/06/20 09:25:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 20 09:29:00 Cat server-dash[52333]: [GIN] 2025/06/20 - 09:29:00 | 200 |      901.38µs |  ************** | GET      "/"
Jun 20 09:29:00 Cat server-dash[52333]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:31552: write: broken pipe
Jun 20 09:30:00 Cat server-dash[52333]: 2025/06/20 09:30:00 执行监控历史记录一致性检查...
Jun 20 09:30:00 Cat server-dash[52333]: 2025/06/20 09:30:00 BadgerDB模式：监控历史记录检查完成
Jun 20 09:30:00 Cat server-dash[52333]: 2025/06/20 09:30:00 BadgerDB模式：流量数据一致性检查完成
Jun 20 09:30:00 Cat server-dash[52333]: 2025/06/20 09:30:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 20 09:30:00 Cat server-dash[52333]: 2025/06/20 09:30:00 BadgerDB: 成功保存 4 个用户的数据
Jun 20 09:30:00 Cat server-dash[52333]: 2025/06/20 09:30:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 20 09:30:00 Cat server-dash[52333]: 2025/06/20 09:30:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 20 09:30:00 Cat server-dash[52333]: 2025/06/20 09:30:00 BadgerDB: 成功保存 17 个服务器的累计流量数据
Jun 20 09:31:37 Cat server-dash[52333]: fatal error: concurrent map iteration and map write
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 900519 [running]:
Jun 20 09:31:37 Cat server-dash[52333]: internal/runtime/maps.fatal({0x1450cc9?, 0xc0117b0f00?})
Jun 20 09:31:37 Cat server-dash[52333]:         runtime/panic.go:1058 +0x18
Jun 20 09:31:37 Cat server-dash[52333]: internal/runtime/maps.(*Iter).Next(0xc012e72060?)
Jun 20 09:31:37 Cat server-dash[52333]:         internal/runtime/maps/table.go:683 +0x86
Jun 20 09:31:37 Cat server-dash[52333]: github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).getServerStat.func1()
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/xos/serverstatus/cmd/dashboard/controller/common_page.go:642 +0x26e9
Jun 20 09:31:37 Cat server-dash[52333]: golang.org/x/sync/singleflight.(*Group).doCall.func2(0xc012e3b3a6, 0xc010b2d860, 0x478bb9?)
Jun 20 09:31:37 Cat server-dash[52333]:         golang.org/x/sync@v0.14.0/singleflight/singleflight.go:198 +0x55
Jun 20 09:31:37 Cat server-dash[52333]: golang.org/x/sync/singleflight.(*Group).doCall(0x12ba960?, 0xc00751e1e0?, {0xc00e773b60?, 0x12?}, 0x12?)
Jun 20 09:31:37 Cat server-dash[52333]:         golang.org/x/sync@v0.14.0/singleflight/singleflight.go:200 +0x7e
Jun 20 09:31:37 Cat server-dash[52333]: golang.org/x/sync/singleflight.(*Group).Do(0xc02ca96c98, {0xc00e773b60, 0x12}, 0xc012e3b480)
Jun 20 09:31:37 Cat server-dash[52333]:         golang.org/x/sync@v0.14.0/singleflight/singleflight.go:113 +0x15b
Jun 20 09:31:37 Cat server-dash[52333]: github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).getServerStat(0xc02ca96c90, 0xc02cad4000, 0x0)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/xos/serverstatus/cmd/dashboard/controller/common_page.go:513 +0xf8
Jun 20 09:31:37 Cat server-dash[52333]: github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).ws(0xc02ca96c90, 0xc02cad4000)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/xos/serverstatus/cmd/dashboard/controller/common_page.go:1038 +0x3d0
Jun 20 09:31:37 Cat server-dash[52333]: github.com/gin-gonic/gin.(*Context).Next(...)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/gin-gonic/gin@v1.10.0/context.go:185
Jun 20 09:31:37 Cat server-dash[52333]: github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1(0xc02cad4000)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/gin-gonic/gin@v1.10.0/recovery.go:102 +0x6f
Jun 20 09:31:37 Cat server-dash[52333]: github.com/gin-gonic/gin.(*Context).Next(...)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/gin-gonic/gin@v1.10.0/context.go:185
Jun 20 09:31:37 Cat server-dash[52333]: github.com/gin-gonic/gin.LoggerWithConfig.func1(0xc02cad4000)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/gin-gonic/gin@v1.10.0/logger.go:249 +0xe5
Jun 20 09:31:37 Cat server-dash[52333]: github.com/gin-gonic/gin.(*Context).Next(...)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/gin-gonic/gin@v1.10.0/context.go:185
Jun 20 09:31:37 Cat server-dash[52333]: github.com/gin-gonic/gin.(*Engine).handleHTTPRequest(0xc0220ac1a0, 0xc02cad4000)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/gin-gonic/gin@v1.10.0/gin.go:633 +0x872
Jun 20 09:31:37 Cat server-dash[52333]: github.com/gin-gonic/gin.(*Engine).ServeHTTP(0xc0220ac1a0, {0x3597aa8, 0xc000000000}, 0xc000364780)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/gin-gonic/gin@v1.10.0/gin.go:589 +0x1aa
Jun 20 09:31:37 Cat server-dash[52333]: net/http.serverHandler.ServeHTTP({0xc01041ec30?}, {0x3597aa8?, 0xc000000000?}, 0x6?)
Jun 20 09:31:37 Cat server-dash[52333]:         net/http/server.go:3301 +0x8e
Jun 20 09:31:37 Cat server-dash[52333]: net/http.(*conn).serve(0xc00433d4d0, {0x3599a08, 0xc02cb7d890})
Jun 20 09:31:37 Cat server-dash[52333]:         net/http/server.go:2102 +0x625
Jun 20 09:31:37 Cat server-dash[52333]: created by net/http.(*Server).Serve in goroutine 1
Jun 20 09:31:37 Cat server-dash[52333]:         net/http/server.go:3454 +0x485
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 1 [IO wait, 2 minutes]:
Jun 20 09:31:37 Cat server-dash[52333]: internal/poll.runtime_pollWait(0x7ff2fe40fd98, 0x72)
Jun 20 09:31:37 Cat server-dash[52333]:         runtime/netpoll.go:351 +0x85
Jun 20 09:31:37 Cat server-dash[52333]: internal/poll.(*pollDesc).wait(0xc0001bb380?, 0x90041a87e?, 0x0)
Jun 20 09:31:37 Cat server-dash[52333]:         internal/poll/fd_poll_runtime.go:84 +0x27
Jun 20 09:31:37 Cat server-dash[52333]: internal/poll.(*pollDesc).waitRead(...)
Jun 20 09:31:37 Cat server-dash[52333]:         internal/poll/fd_poll_runtime.go:89
Jun 20 09:31:37 Cat server-dash[52333]: internal/poll.(*FD).Accept(0xc0001bb380)
Jun 20 09:31:37 Cat server-dash[52333]:         internal/poll/fd_unix.go:620 +0x295
Jun 20 09:31:37 Cat server-dash[52333]: net.(*netFD).accept(0xc0001bb380)
Jun 20 09:31:37 Cat server-dash[52333]:         net/fd_unix.go:172 +0x29
Jun 20 09:31:37 Cat server-dash[52333]: net.(*TCPListener).accept(0xc02cb7f100)
Jun 20 09:31:37 Cat server-dash[52333]:         net/tcpsock_posix.go:159 +0x1b
Jun 20 09:31:37 Cat server-dash[52333]: net.(*TCPListener).Accept(0xc02cb7f100)
Jun 20 09:31:37 Cat server-dash[52333]:         net/tcpsock.go:380 +0x30
Jun 20 09:31:37 Cat server-dash[52333]: net/http.(*Server).Serve(0xc02cad5800, {0x3597b68, 0xc02cb7f100})
Jun 20 09:31:37 Cat server-dash[52333]:         net/http/server.go:3424 +0x30c
Jun 20 09:31:37 Cat server-dash[52333]: net/http.(*Server).ListenAndServe(0xc02cad5800)
Jun 20 09:31:37 Cat server-dash[52333]:         net/http/server.go:3350 +0x71
Jun 20 09:31:37 Cat server-dash[52333]: main.main.func5()
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/xos/serverstatus/cmd/dashboard/main.go:190 +0x17
Jun 20 09:31:37 Cat server-dash[52333]: github.com/ory/graceful.Graceful(0xc000041ec8, 0xc02ca96cc0)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/ory/graceful@v0.1.3/http_graceful.go:76 +0xaf
Jun 20 09:31:37 Cat server-dash[52333]: main.main()
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/xos/serverstatus/cmd/dashboard/main.go:189 +0x90e
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 10 [select, 3 minutes]:
Jun 20 09:31:37 Cat server-dash[52333]: github.com/patrickmn/go-cache.(*janitor).Run(0xc00023cb70, 0xc00021ec00)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/patrickmn/go-cache@v2.1.0+incompatible/cache.go:1079 +0x7b
Jun 20 09:31:37 Cat server-dash[52333]: created by github.com/patrickmn/go-cache.runJanitor in goroutine 1
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/patrickmn/go-cache@v2.1.0+incompatible/cache.go:1099 +0xdb
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 11 [select, 26 minutes]:
Jun 20 09:31:37 Cat server-dash[52333]: github.com/patrickmn/go-cache.(*janitor).Run(0xc00023cb80, 0xc00021ec40)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/patrickmn/go-cache@v2.1.0+incompatible/cache.go:1079 +0x7b
Jun 20 09:31:37 Cat server-dash[52333]: created by github.com/patrickmn/go-cache.runJanitor in goroutine 1
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/patrickmn/go-cache@v2.1.0+incompatible/cache.go:1099 +0xdb
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 12 [chan receive, 26 minutes]:
Jun 20 09:31:37 Cat server-dash[52333]: github.com/xos/serverstatus/service/singleton.InitTimezoneAndCache.func1()
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/xos/serverstatus/service/singleton/singleton.go:58 +0xa5
Jun 20 09:31:37 Cat server-dash[52333]: created by github.com/xos/serverstatus/service/singleton.InitTimezoneAndCache in goroutine 1
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/xos/serverstatus/service/singleton/singleton.go:54 +0xf4
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 13 [select]:
Jun 20 09:31:37 Cat server-dash[52333]: github.com/dgraph-io/badger/v3/y.(*WaterMark).process(0xc0001c0e10, 0xc0001c0de0)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/badger/v3@v3.2103.5/y/watermark.go:214 +0x267
Jun 20 09:31:37 Cat server-dash[52333]: created by github.com/dgraph-io/badger/v3/y.(*WaterMark).Init in goroutine 1
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/badger/v3@v3.2103.5/y/watermark.go:72 +0xa7
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 14 [select]:
Jun 20 09:31:37 Cat server-dash[52333]: github.com/dgraph-io/badger/v3/y.(*WaterMark).process(0xc0001c0e40, 0xc0001c0de0)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/badger/v3@v3.2103.5/y/watermark.go:214 +0x267
Jun 20 09:31:37 Cat server-dash[52333]: created by github.com/dgraph-io/badger/v3/y.(*WaterMark).Init in goroutine 1
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/badger/v3@v3.2103.5/y/watermark.go:72 +0xa7
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 15 [select]:
Jun 20 09:31:37 Cat server-dash[52333]: github.com/dgraph-io/ristretto/z.(*AllocatorPool).freeupAllocators(0xc000311998)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/ristretto@v0.2.0/z/allocator.go:385 +0x113
Jun 20 09:31:37 Cat server-dash[52333]: created by github.com/dgraph-io/ristretto/z.NewAllocatorPool in goroutine 1
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/ristretto@v0.2.0/z/allocator.go:324 +0xb9
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 16 [select, 1 minutes]:
Jun 20 09:31:37 Cat server-dash[52333]: github.com/dgraph-io/ristretto.(*defaultPolicy).processItems(0xc00031a4c0)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/ristretto@v0.2.0/policy.go:101 +0x7f
Jun 20 09:31:37 Cat server-dash[52333]: created by github.com/dgraph-io/ristretto.newDefaultPolicy in goroutine 1
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/ristretto@v0.2.0/policy.go:85 +0x139
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 34 [select]:
Jun 20 09:31:37 Cat server-dash[52333]: github.com/dgraph-io/ristretto.(*Cache).processItems(0xc0001bb400)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/ristretto@v0.2.0/cache.go:459 +0x112
Jun 20 09:31:37 Cat server-dash[52333]: created by github.com/dgraph-io/ristretto.NewCache in goroutine 1
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/ristretto@v0.2.0/cache.go:211 +0x669
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 35 [select, 1 minutes]:
Jun 20 09:31:37 Cat server-dash[52333]: github.com/dgraph-io/badger/v3.(*DB).monitorCache(0xc00022c908, 0xc00033b7d0)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/badger/v3@v3.2103.5/db.go:469 +0x15d
Jun 20 09:31:37 Cat server-dash[52333]: created by github.com/dgraph-io/badger/v3.Open in goroutine 1
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/badger/v3@v3.2103.5/db.go:311 +0xc09
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 36 [select, 1 minutes]:
Jun 20 09:31:37 Cat server-dash[52333]: github.com/dgraph-io/badger/v3.(*DB).updateSize(0xc00022c908, 0xc00033b890)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/badger/v3@v3.2103.5/db.go:1171 +0x13e
Jun 20 09:31:37 Cat server-dash[52333]: created by github.com/dgraph-io/badger/v3.Open in goroutine 1
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/badger/v3@v3.2103.5/db.go:331 +0xe16
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 917984 [select]:
Jun 20 09:31:37 Cat server-dash[52333]: google.golang.org/grpc/internal/transport.(*controlBuffer).get(0xc01177a240, 0x1)
Jun 20 09:31:37 Cat server-dash[52333]:         google.golang.org/grpc@v1.72.0/internal/transport/controlbuf.go:412 +0x108
Jun 20 09:31:37 Cat server-dash[52333]: google.golang.org/grpc/internal/transport.(*loopyWriter).run(0xc007562680)
Jun 20 09:31:37 Cat server-dash[52333]:         google.golang.org/grpc@v1.72.0/internal/transport/controlbuf.go:575 +0x78
Jun 20 09:31:37 Cat server-dash[52333]: google.golang.org/grpc/internal/transport.NewServerTransport.func2()
Jun 20 09:31:37 Cat server-dash[52333]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:336 +0xde
Jun 20 09:31:37 Cat server-dash[52333]: created by google.golang.org/grpc/internal/transport.NewServerTransport in goroutine 917983
Jun 20 09:31:37 Cat server-dash[52333]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:334 +0x189b
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 915720 [select]:
Jun 20 09:31:37 Cat server-dash[52333]: google.golang.org/grpc/internal/transport.(*controlBuffer).get(0xc006606b80, 0x1)
Jun 20 09:31:37 Cat server-dash[52333]:         google.golang.org/grpc@v1.72.0/internal/transport/controlbuf.go:412 +0x108
Jun 20 09:31:37 Cat server-dash[52333]: google.golang.org/grpc/internal/transport.(*loopyWriter).run(0xc0027ad880)
Jun 20 09:31:37 Cat server-dash[52333]:         google.golang.org/grpc@v1.72.0/internal/transport/controlbuf.go:575 +0x78
Jun 20 09:31:37 Cat server-dash[52333]: google.golang.org/grpc/internal/transport.NewServerTransport.func2()
Jun 20 09:31:37 Cat server-dash[52333]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:336 +0xde
Jun 20 09:31:37 Cat server-dash[52333]: created by google.golang.org/grpc/internal/transport.NewServerTransport in goroutine 915719
Jun 20 09:31:37 Cat server-dash[52333]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:334 +0x189b
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 49 [select]:
Jun 20 09:31:37 Cat server-dash[52333]: github.com/dgraph-io/badger/v3.(*levelsController).runCompactor(0xc00021c460, 0x0, 0xc008d36b10)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/badger/v3@v3.2103.5/levels.go:508 +0x229
Jun 20 09:31:37 Cat server-dash[52333]: created by github.com/dgraph-io/badger/v3.(*levelsController).startCompact in goroutine 1
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/badger/v3@v3.2103.5/levels.go:354 +0x58
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 82 [select]:
Jun 20 09:31:37 Cat server-dash[52333]: github.com/dgraph-io/badger/v3.(*levelsController).runCompactor(0xc00021c460, 0x1, 0xc008d36b10)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/badger/v3@v3.2103.5/levels.go:508 +0x229
Jun 20 09:31:37 Cat server-dash[52333]: created by github.com/dgraph-io/badger/v3.(*levelsController).startCompact in goroutine 1
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/badger/v3@v3.2103.5/levels.go:354 +0x58
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 83 [select]:
Jun 20 09:31:37 Cat server-dash[52333]: github.com/dgraph-io/badger/v3.(*levelsController).runCompactor(0xc00021c460, 0x2, 0xc008d36b10)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/badger/v3@v3.2103.5/levels.go:508 +0x229
Jun 20 09:31:37 Cat server-dash[52333]: created by github.com/dgraph-io/badger/v3.(*levelsController).startCompact in goroutine 1
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/badger/v3@v3.2103.5/levels.go:354 +0x58
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 84 [select]:
Jun 20 09:31:37 Cat server-dash[52333]: github.com/dgraph-io/badger/v3.(*levelsController).runCompactor(0xc00021c460, 0x3, 0xc008d36b10)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/badger/v3@v3.2103.5/levels.go:508 +0x229
Jun 20 09:31:37 Cat server-dash[52333]: created by github.com/dgraph-io/badger/v3.(*levelsController).startCompact in goroutine 1
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/badger/v3@v3.2103.5/levels.go:354 +0x58
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 85 [chan receive, 26 minutes]:
Jun 20 09:31:37 Cat server-dash[52333]: github.com/dgraph-io/badger/v3.(*DB).flushMemtable(0xc00022c908, 0xc0001c0de0?)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/badger/v3@v3.2103.5/db.go:1078 +0x98
Jun 20 09:31:37 Cat server-dash[52333]: github.com/dgraph-io/badger/v3.Open.func5()
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/badger/v3@v3.2103.5/db.go:357 +0x1e
Jun 20 09:31:37 Cat server-dash[52333]: created by github.com/dgraph-io/badger/v3.Open in goroutine 1
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/badger/v3@v3.2103.5/db.go:356 +0x103d
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 922667 [IO wait]:
Jun 20 09:31:37 Cat server-dash[52333]: internal/poll.runtime_pollWait(0x7ff297050c78, 0x72)
Jun 20 09:31:37 Cat server-dash[52333]:         runtime/netpoll.go:351 +0x85
Jun 20 09:31:37 Cat server-dash[52333]: internal/poll.(*pollDesc).wait(0xc005759380?, 0xc00f970000?, 0x0)
Jun 20 09:31:37 Cat server-dash[52333]:         internal/poll/fd_poll_runtime.go:84 +0x27
Jun 20 09:31:37 Cat server-dash[52333]: internal/poll.(*pollDesc).waitRead(...)
Jun 20 09:31:37 Cat server-dash[52333]:         internal/poll/fd_poll_runtime.go:89
Jun 20 09:31:37 Cat server-dash[52333]: internal/poll.(*FD).Read(0xc005759380, {0xc00f970000, 0x8000, 0x8000})
Jun 20 09:31:37 Cat server-dash[52333]:         internal/poll/fd_unix.go:165 +0x27a
Jun 20 09:31:37 Cat server-dash[52333]: net.(*netFD).Read(0xc005759380, {0xc00f970000?, 0x7ff2fe6995c0?, 0x400000000000000?})
Jun 20 09:31:37 Cat server-dash[52333]:         net/fd_posix.go:55 +0x25
Jun 20 09:31:37 Cat server-dash[52333]: net.(*conn).Read(0xc00fc4ff28, {0xc00f970000?, 0x20?, 0x10001321ae0?})
Jun 20 09:31:37 Cat server-dash[52333]:         net/net.go:194 +0x45
Jun 20 09:31:37 Cat server-dash[52333]: bufio.(*Reader).Read(0xc00ff43aa0, {0xc00664a820, 0x9, 0x478bd9?})
Jun 20 09:31:37 Cat server-dash[52333]:         bufio/bufio.go:245 +0x197
Jun 20 09:31:37 Cat server-dash[52333]: io.ReadAtLeast({0x358e720, 0xc00ff43aa0}, {0xc00664a820, 0x9, 0x9}, 0x9)
Jun 20 09:31:37 Cat server-dash[52333]:         io/io.go:335 +0x91
Jun 20 09:31:37 Cat server-dash[52333]: io.ReadFull(...)
Jun 20 09:31:37 Cat server-dash[52333]:         io/io.go:354
Jun 20 09:31:37 Cat server-dash[52333]: golang.org/x/net/http2.readFrameHeader({0xc00664a820, 0x9, 0xc008d64cc0?}, {0x358e720?, 0xc00ff43aa0?})
Jun 20 09:31:37 Cat server-dash[52333]:         golang.org/x/net@v0.40.0/http2/frame.go:242 +0x65
Jun 20 09:31:37 Cat server-dash[52333]: golang.org/x/net/http2.(*Framer).ReadFrame(0xc00664a7e0)
Jun 20 09:31:37 Cat server-dash[52333]:         golang.org/x/net@v0.40.0/http2/frame.go:506 +0x7d
Jun 20 09:31:37 Cat server-dash[52333]: google.golang.org/grpc/internal/transport.(*http2Server).HandleStreams(0xc00eb48b60, {0x3599a08, 0xc00eaaf530}, 0xc00eaaf560)
Jun 20 09:31:37 Cat server-dash[52333]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:657 +0x105
Jun 20 09:31:37 Cat server-dash[52333]: google.golang.org/grpc.(*Server).serveStreams(0xc012d34000, {0x35999d0?, 0x3ea5f80?}, {0x359a4e8, 0xc00eb48b60}, {0x359fb58?, 0xc00fc4ff28?})
Jun 20 09:31:37 Cat server-dash[52333]:         google.golang.org/grpc@v1.72.0/server.go:1029 +0x396
Jun 20 09:31:37 Cat server-dash[52333]: google.golang.org/grpc.(*Server).handleRawConn.func1()
Jun 20 09:31:37 Cat server-dash[52333]:         google.golang.org/grpc@v1.72.0/server.go:964 +0x56
Jun 20 09:31:37 Cat server-dash[52333]: created by google.golang.org/grpc.(*Server).handleRawConn in goroutine 922664
Jun 20 09:31:37 Cat server-dash[52333]:         google.golang.org/grpc@v1.72.0/server.go:963 +0x1cb
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 918199 [select]:
Jun 20 09:31:37 Cat server-dash[52333]: google.golang.org/grpc/internal/transport.(*controlBuffer).get(0xc00fdee300, 0x1)
Jun 20 09:31:37 Cat server-dash[52333]:         google.golang.org/grpc@v1.72.0/internal/transport/controlbuf.go:412 +0x108
Jun 20 09:31:37 Cat server-dash[52333]: google.golang.org/grpc/internal/transport.(*loopyWriter).run(0xc00754cf00)
Jun 20 09:31:37 Cat server-dash[52333]:         google.golang.org/grpc@v1.72.0/internal/transport/controlbuf.go:575 +0x78
Jun 20 09:31:37 Cat server-dash[52333]: google.golang.org/grpc/internal/transport.NewServerTransport.func2()
Jun 20 09:31:37 Cat server-dash[52333]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:336 +0xde
Jun 20 09:31:37 Cat server-dash[52333]: created by google.golang.org/grpc/internal/transport.NewServerTransport in goroutine 918198
Jun 20 09:31:37 Cat server-dash[52333]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:334 +0x189b
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 923514 [select]:
Jun 20 09:31:37 Cat server-dash[52333]: google.golang.org/grpc/internal/transport.(*http2Server).keepalive(0xc0080b9d40)
Jun 20 09:31:37 Cat server-dash[52333]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:1197 +0x1ec
Jun 20 09:31:37 Cat server-dash[52333]: created by google.golang.org/grpc/internal/transport.NewServerTransport in goroutine 923512
Jun 20 09:31:37 Cat server-dash[52333]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:357 +0x18dd
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 86 [select]:
Jun 20 09:31:37 Cat server-dash[52333]: github.com/dgraph-io/badger/v3.(*vlogThreshold).listenForValueThresholdUpdate(0xc00031a440)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/badger/v3@v3.2103.5/value.go:1172 +0x105
Jun 20 09:31:37 Cat server-dash[52333]: created by github.com/dgraph-io/badger/v3.Open in goroutine 1
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/badger/v3@v3.2103.5/db.go:380 +0x1632
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 87 [select]:
Jun 20 09:31:37 Cat server-dash[52333]: github.com/dgraph-io/badger/v3.(*DB).doWrites(0xc00022c908, 0xc008d36c00)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/badger/v3@v3.2103.5/db.go:900 +0x226
Jun 20 09:31:37 Cat server-dash[52333]: created by github.com/dgraph-io/badger/v3.Open in goroutine 1
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/badger/v3@v3.2103.5/db.go:387 +0x16f3
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 88 [chan receive, 26 minutes]:
Jun 20 09:31:37 Cat server-dash[52333]: github.com/dgraph-io/badger/v3.(*valueLog).waitOnGC(0xc00022cb00, 0x0?)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/badger/v3@v3.2103.5/value.go:1079 +0x6c
Jun 20 09:31:37 Cat server-dash[52333]: created by github.com/dgraph-io/badger/v3.Open in goroutine 1
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/badger/v3@v3.2103.5/db.go:391 +0x17b7
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 89 [select, 26 minutes]:
Jun 20 09:31:37 Cat server-dash[52333]: github.com/dgraph-io/badger/v3.(*publisher).listenForUpdates(0xc0001c0f00, 0xc008d36c60)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/badger/v3@v3.2103.5/publisher.go:73 +0x125
Jun 20 09:31:37 Cat server-dash[52333]: created by github.com/dgraph-io/badger/v3.Open in goroutine 1
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/dgraph-io/badger/v3@v3.2103.5/db.go:395 +0x186c
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 90 [select, 11 minutes]:
Jun 20 09:31:37 Cat server-dash[52333]: github.com/xos/serverstatus/db.(*BadgerDB).startMaintenance.func1()
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/xos/serverstatus/db/badger.go:103 +0xe6
Jun 20 09:31:37 Cat server-dash[52333]: created by github.com/xos/serverstatus/db.(*BadgerDB).startMaintenance in goroutine 1
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/xos/serverstatus/db/badger.go:90 +0x4f
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 589 [sleep]:
Jun 20 09:31:37 Cat server-dash[52333]: time.Sleep(0xb1f1e025)
Jun 20 09:31:37 Cat server-dash[52333]:         runtime/time.go:338 +0x165
Jun 20 09:31:37 Cat server-dash[52333]: github.com/xos/serverstatus/service/singleton.AlertSentinelStart()
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/xos/serverstatus/service/singleton/alertsentinel.go:184 +0x785
Jun 20 09:31:37 Cat server-dash[52333]: main.main.func3()
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/xos/serverstatus/cmd/dashboard/main.go:167 +0x30
Jun 20 09:31:37 Cat server-dash[52333]: created by main.main in goroutine 1
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/xos/serverstatus/cmd/dashboard/main.go:161 +0x631
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 924053 [select]:
Jun 20 09:31:37 Cat server-dash[52333]: google.golang.org/grpc/internal/transport.(*controlBuffer).get(0xc02ca2ae00, 0x1)
Jun 20 09:31:37 Cat server-dash[52333]:         google.golang.org/grpc@v1.72.0/internal/transport/controlbuf.go:412 +0x108
Jun 20 09:31:37 Cat server-dash[52333]: google.golang.org/grpc/internal/transport.(*loopyWriter).run(0xc00f6da700)
Jun 20 09:31:37 Cat server-dash[52333]:         google.golang.org/grpc@v1.72.0/internal/transport/controlbuf.go:575 +0x78
Jun 20 09:31:37 Cat server-dash[52333]: google.golang.org/grpc/internal/transport.NewServerTransport.func2()
Jun 20 09:31:37 Cat server-dash[52333]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:336 +0xde
Jun 20 09:31:37 Cat server-dash[52333]: created by google.golang.org/grpc/internal/transport.NewServerTransport in goroutine 924052
Jun 20 09:31:37 Cat server-dash[52333]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:334 +0x189b
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 95 [chan receive, 26 minutes]:
Jun 20 09:31:37 Cat server-dash[52333]: github.com/xos/serverstatus/service/singleton.startBadgerDBMaintenanceTasks.func1()
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/xos/serverstatus/service/singleton/badger_adapter.go:338 +0x71
Jun 20 09:31:37 Cat server-dash[52333]: created by github.com/xos/serverstatus/service/singleton.startBadgerDBMaintenanceTasks in goroutine 1
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/xos/serverstatus/service/singleton/badger_adapter.go:332 +0x1a
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 62 [select, 1 minutes]:
Jun 20 09:31:37 Cat server-dash[52333]: github.com/xos/serverstatus/service/singleton.(*GoroutinePool).startWorker.func1()
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/xos/serverstatus/service/singleton/goroutine_pool.go:120 +0x150
Jun 20 09:31:37 Cat server-dash[52333]: created by github.com/xos/serverstatus/service/singleton.(*GoroutinePool).startWorker in goroutine 1
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/xos/serverstatus/service/singleton/goroutine_pool.go:104 +0xb0
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 63 [select, 1 minutes]:
Jun 20 09:31:37 Cat server-dash[52333]: github.com/xos/serverstatus/service/singleton.(*GoroutinePool).startWorker.func1()
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/xos/serverstatus/service/singleton/goroutine_pool.go:120 +0x150
Jun 20 09:31:37 Cat server-dash[52333]: created by github.com/xos/serverstatus/service/singleton.(*GoroutinePool).startWorker in goroutine 1
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/xos/serverstatus/service/singleton/goroutine_pool.go:104 +0xb0
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 64 [select, 1 minutes]:
Jun 20 09:31:37 Cat server-dash[52333]: github.com/xos/serverstatus/service/singleton.(*GoroutinePool).startWorker.func1()
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/xos/serverstatus/service/singleton/goroutine_pool.go:120 +0x150
Jun 20 09:31:37 Cat server-dash[52333]: created by github.com/xos/serverstatus/service/singleton.(*GoroutinePool).startWorker in goroutine 1
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/xos/serverstatus/service/singleton/goroutine_pool.go:104 +0xb0
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 65 [select, 1 minutes]:
Jun 20 09:31:37 Cat server-dash[52333]: github.com/xos/serverstatus/service/singleton.(*GoroutinePool).startWorker.func1()
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/xos/serverstatus/service/singleton/goroutine_pool.go:120 +0x150
Jun 20 09:31:37 Cat server-dash[52333]: created by github.com/xos/serverstatus/service/singleton.(*GoroutinePool).startWorker in goroutine 1
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/xos/serverstatus/service/singleton/goroutine_pool.go:104 +0xb0
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 98 [select, 1 minutes]:
Jun 20 09:31:37 Cat server-dash[52333]: github.com/xos/serverstatus/service/singleton.(*GoroutinePool).startWorker.func1()
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/xos/serverstatus/service/singleton/goroutine_pool.go:120 +0x150
Jun 20 09:31:37 Cat server-dash[52333]: created by github.com/xos/serverstatus/service/singleton.(*GoroutinePool).startWorker in goroutine 1
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/xos/serverstatus/service/singleton/goroutine_pool.go:104 +0xb0
Jun 20 09:31:37 Cat server-dash[52333]: goroutine 99 [select, 1 minutes]:
Jun 20 09:31:37 Cat server-dash[52333]: github.com/xos/serverstatus/service/singleton.(*GoroutinePool).monitor(0xc00008b3e0)
Jun 20 09:31:37 Cat server-dash[52333]:         github.com/xos/serverstatus/service/singleton/goroutine_pool.go:161 +0xe5
Jun 20 09:31:37 Cat server-dash[52333]: created by github.com/xos/serverstatus/service/singleton.(*GoroutinePool).Start in goroutine 1";

还有：
"Jun 20 16:39:57 Cat server-dash[1986]: [GIN] 2025/06/20 - 16:39:57 | 200 |     954.329µs | 2a01:4f8:c0c:83fa::1 | GET      "/"
Jun 20 16:39:57 Cat server-dash[1986]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:16906: write: broken pipe
Jun 20 16:40:00 Cat server-dash[1986]: 2025/06/20 16:40:00 BadgerDB模式：流量数据一致性检查完成
Jun 20 16:40:00 Cat server-dash[1986]: 2025/06/20 16:40:00 执行监控历史记录一致性检查...
Jun 20 16:40:00 Cat server-dash[1986]: 2025/06/20 16:40:00 BadgerDB模式：监控历史记录检查完成
Jun 20 16:40:00 Cat server-dash[1986]: 2025/06/20 16:40:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 20 16:40:00 Cat server-dash[1986]: 2025/06/20 16:40:00 BadgerDB: 成功保存 4 个用户的数据
Jun 20 16:40:00 Cat server-dash[1986]: 2025/06/20 16:40:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 20 16:40:00 Cat server-dash[1986]: 2025/06/20 16:40:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 20 16:40:00 Cat server-dash[1986]: 2025/06/20 16:40:00 BadgerDB: 成功保存 17 个服务器的累计流量数据
Jun 20 16:40:24 Cat server-dash[1986]: 2025/06/20 16:40:24 数据大小限制完成: 状态记录=54, 监控项=9
Jun 20 16:45:00 Cat server-dash[1986]: 2025/06/20 16:45:00 BadgerDB模式：流量数据一致性检查完成
Jun 20 16:45:00 Cat server-dash[1986]: 2025/06/20 16:45:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 20 16:45:00 Cat server-dash[1986]: 2025/06/20 16:45:00 BadgerDB: 成功保存 4 个用户的数据
Jun 20 16:45:00 Cat server-dash[1986]: 2025/06/20 16:45:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 20 16:45:00 Cat server-dash[1986]: 2025/06/20 16:45:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 20 16:45:02 Cat server-dash[1986]: [GIN] 2025/06/20 - 16:45:02 | 200 |     967.824µs |  ************** | GET      "/"
Jun 20 16:45:02 Cat server-dash[1986]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:5580: write: broken pipe
Jun 20 16:47:36 Cat server-dash[1986]: panic: runtime error: invalid memory address or nil pointer dereference
Jun 20 16:47:36 Cat server-dash[1986]: [signal SIGSEGV: segmentation violation code=0x1 addr=0x0 pc=0xc4ad3b]
Jun 20 16:47:36 Cat server-dash[1986]: goroutine 7524484 [running]:
Jun 20 16:47:36 Cat server-dash[1986]: gorm.io/gorm.(*DB).Session(0x0, 0xc013ae1d20)
Jun 20 16:47:36 Cat server-dash[1986]:         gorm.io/gorm@v1.26.0/gorm.go:235 +0x3b
Jun 20 16:47:36 Cat server-dash[1986]: gorm.io/gorm.(*DB).WithContext(...)
Jun 20 16:47:36 Cat server-dash[1986]:         gorm.io/gorm@v1.26.0/gorm.go:336
Jun 20 16:47:36 Cat server-dash[1986]: github.com/xos/serverstatus/service/singleton.executeDBWriteRequest.func1()
Jun 20 16:47:36 Cat server-dash[1986]:         github.com/xos/serverstatus/service/singleton/singleton.go:2162 +0xb0
Jun 20 16:47:36 Cat server-dash[1986]: github.com/xos/serverstatus/service/singleton.executeWithAdvancedRetry(0xc013ae1eb8, 0x5, 0x2faf080, 0x12a05f200)
Jun 20 16:47:36 Cat server-dash[1986]:         github.com/xos/serverstatus/service/singleton/singleton.go:599 +0x124
Jun 20 16:47:36 Cat server-dash[1986]: github.com/xos/serverstatus/service/singleton.ExecuteWithRetry(...)
Jun 20 16:47:36 Cat server-dash[1986]:         github.com/xos/serverstatus/service/singleton/singleton.go:593
Jun 20 16:47:36 Cat server-dash[1986]: github.com/xos/serverstatus/service/singleton.executeDBWriteRequest({0x8, {0x14272e5, 0x7}, 0xc0055747e0, 0xc00e8fa890})
Jun 20 16:47:36 Cat server-dash[1986]:         github.com/xos/serverstatus/service/singleton/singleton.go:2157 +0x88
Jun 20 16:47:36 Cat server-dash[1986]: github.com/xos/serverstatus/service/singleton.AsyncDBUpdate.func1()
Jun 20 16:47:36 Cat server-dash[1986]:         github.com/xos/serverstatus/service/singleton/singleton.go:2247 +0x14a
Jun 20 16:47:36 Cat server-dash[1986]: created by github.com/xos/serverstatus/service/singleton.AsyncDBUpdate in goroutine 7524483
Jun 20 16:47:36 Cat server-dash[1986]:         github.com/xos/serverstatus/service/singleton/singleton.go:2234 +0x118
Jun 20 16:47:36 Cat systemd[1]: server-dash.service: Main process exited, code=exited, status=2/INVALIDARGUMENT";


还有：
"Jun 20 17:05:16 Cat server-dash[15475]: [GIN] 2025/06/20 - 17:05:16 | 200 |     796.624µs |    ************ | GET      "/"
Jun 20 17:05:16 Cat server-dash[15475]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:5222: write: broken pipe
Jun 20 17:10:00 Cat server-dash[15475]: 2025/06/20 17:10:00 BadgerDB模式：流量数据一致性检查完成
Jun 20 17:10:00 Cat server-dash[15475]: 2025/06/20 17:10:00 执行监控历史记录一致性检查...
Jun 20 17:10:00 Cat server-dash[15475]: 2025/06/20 17:10:00 BadgerDB模式：监控历史记录检查完成
Jun 20 17:10:00 Cat server-dash[15475]: 2025/06/20 17:10:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 20 17:10:00 Cat server-dash[15475]: 2025/06/20 17:10:00 BadgerDB: 成功保存 4 个用户的数据
Jun 20 17:10:00 Cat server-dash[15475]: 2025/06/20 17:10:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 20 17:10:00 Cat server-dash[15475]: 2025/06/20 17:10:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 20 17:10:00 Cat server-dash[15475]: 2025/06/20 17:10:00 BadgerDB: 成功保存 17 个服务器的累计流量数据
Jun 20 17:10:02 Cat server-dash[15475]: fatal error: concurrent map iteration and map write
Jun 20 17:10:02 Cat server-dash[15475]: goroutine 911733 [running]:
Jun 20 17:10:02 Cat server-dash[15475]: internal/runtime/maps.fatal({0x1450cc9?, 0xc006452d30?})
Jun 20 17:10:02 Cat server-dash[15475]:         runtime/panic.go:1058 +0x18
Jun 20 17:10:02 Cat server-dash[15475]: internal/runtime/maps.(*Iter).Next(0xc00e2d1240?)
Jun 20 17:10:02 Cat server-dash[15475]:         internal/runtime/maps/table.go:683 +0x86
Jun 20 17:10:02 Cat server-dash[15475]: github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).getServerStat.func1()
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/xos/serverstatus/cmd/dashboard/controller/common_page.go:650 +0xd67
Jun 20 17:10:02 Cat server-dash[15475]: golang.org/x/sync/singleflight.(*Group).doCall.func2(0xc00e2d13a6, 0xc00e952b90, 0x478bb9?)
Jun 20 17:10:02 Cat server-dash[15475]:         golang.org/x/sync@v0.14.0/singleflight/singleflight.go:198 +0x55
Jun 20 17:10:02 Cat server-dash[15475]: golang.org/x/sync/singleflight.(*Group).doCall(0x12ba960?, 0xc00e7190b0?, {0xc00e5f28a0?, 0x12?}, 0x12?)
Jun 20 17:10:02 Cat server-dash[15475]:         golang.org/x/sync@v0.14.0/singleflight/singleflight.go:200 +0x7e
Jun 20 17:10:02 Cat server-dash[15475]: golang.org/x/sync/singleflight.(*Group).Do(0xc007834f68, {0xc00e5f28a0, 0x12}, 0xc00e2d1480)
Jun 20 17:10:02 Cat server-dash[15475]:         golang.org/x/sync@v0.14.0/singleflight/singleflight.go:113 +0x15b
Jun 20 17:10:02 Cat server-dash[15475]: github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).getServerStat(0xc007834f60, 0xc003b36200, 0x0)
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/xos/serverstatus/cmd/dashboard/controller/common_page.go:513 +0xf8
Jun 20 17:10:02 Cat server-dash[15475]: github.com/xos/serverstatus/cmd/dashboard/controller.(*commonPage).ws(0xc007834f60, 0xc003b36200)
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/xos/serverstatus/cmd/dashboard/controller/common_page.go:1038 +0x3d0
Jun 20 17:10:02 Cat server-dash[15475]: github.com/gin-gonic/gin.(*Context).Next(...)
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/gin-gonic/gin@v1.10.0/context.go:185
Jun 20 17:10:02 Cat server-dash[15475]: github.com/gin-gonic/gin.CustomRecoveryWithWriter.func1(0xc003b36200)
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/gin-gonic/gin@v1.10.0/recovery.go:102 +0x6f
Jun 20 17:10:02 Cat server-dash[15475]: github.com/gin-gonic/gin.(*Context).Next(...)
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/gin-gonic/gin@v1.10.0/context.go:185
Jun 20 17:10:02 Cat server-dash[15475]: github.com/gin-gonic/gin.LoggerWithConfig.func1(0xc003b36200)
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/gin-gonic/gin@v1.10.0/logger.go:249 +0xe5
Jun 20 17:10:02 Cat server-dash[15475]: github.com/gin-gonic/gin.(*Context).Next(...)
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/gin-gonic/gin@v1.10.0/context.go:185
Jun 20 17:10:02 Cat server-dash[15475]: github.com/gin-gonic/gin.(*Engine).handleHTTPRequest(0xc032bdb6c0, 0xc003b36200)
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/gin-gonic/gin@v1.10.0/gin.go:633 +0x872
Jun 20 17:10:02 Cat server-dash[15475]: github.com/gin-gonic/gin.(*Engine).ServeHTTP(0xc032bdb6c0, {0x3597aa8, 0xc000000700}, 0xc00029a280)
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/gin-gonic/gin@v1.10.0/gin.go:589 +0x1aa
Jun 20 17:10:02 Cat server-dash[15475]: net/http.serverHandler.ServeHTTP({0xc002b62930?}, {0x3597aa8?, 0xc000000700?}, 0x6?)
Jun 20 17:10:02 Cat server-dash[15475]:         net/http/server.go:3301 +0x8e
Jun 20 17:10:02 Cat server-dash[15475]: net/http.(*conn).serve(0xc006bd87e0, {0x3599a08, 0xc007c566c0})
Jun 20 17:10:02 Cat server-dash[15475]:         net/http/server.go:2102 +0x625
Jun 20 17:10:02 Cat server-dash[15475]: created by net/http.(*Server).Serve in goroutine 1
Jun 20 17:10:02 Cat server-dash[15475]:         net/http/server.go:3454 +0x485
Jun 20 17:10:02 Cat server-dash[15475]: goroutine 1 [IO wait, 4 minutes]:
Jun 20 17:10:02 Cat server-dash[15475]: internal/poll.runtime_pollWait(0x7fc485e1ed98, 0x72)
Jun 20 17:10:02 Cat server-dash[15475]:         runtime/netpoll.go:351 +0x85
Jun 20 17:10:02 Cat server-dash[15475]: internal/poll.(*pollDesc).wait(0xc002347300?, 0x900000036?, 0x0)
Jun 20 17:10:02 Cat server-dash[15475]:         internal/poll/fd_poll_runtime.go:84 +0x27
Jun 20 17:10:02 Cat server-dash[15475]: internal/poll.(*pollDesc).waitRead(...)
Jun 20 17:10:02 Cat server-dash[15475]:         internal/poll/fd_poll_runtime.go:89
Jun 20 17:10:02 Cat server-dash[15475]: internal/poll.(*FD).Accept(0xc002347300)
Jun 20 17:10:02 Cat server-dash[15475]:         internal/poll/fd_unix.go:620 +0x295
Jun 20 17:10:02 Cat server-dash[15475]: net.(*netFD).accept(0xc002347300)
Jun 20 17:10:02 Cat server-dash[15475]:         net/fd_unix.go:172 +0x29
Jun 20 17:10:02 Cat server-dash[15475]: net.(*TCPListener).accept(0xc007c664c0)
Jun 20 17:10:02 Cat server-dash[15475]:         net/tcpsock_posix.go:159 +0x1b
Jun 20 17:10:02 Cat server-dash[15475]: net.(*TCPListener).Accept(0xc007c664c0)
Jun 20 17:10:02 Cat server-dash[15475]:         net/tcpsock.go:380 +0x30
Jun 20 17:10:02 Cat server-dash[15475]: net/http.(*Server).Serve(0xc0079c1800, {0x3597b68, 0xc007c664c0})
Jun 20 17:10:02 Cat server-dash[15475]:         net/http/server.go:3424 +0x30c
Jun 20 17:10:02 Cat server-dash[15475]: net/http.(*Server).ListenAndServe(0xc0079c1800)
Jun 20 17:10:02 Cat server-dash[15475]:         net/http/server.go:3350 +0x71
Jun 20 17:10:02 Cat server-dash[15475]: main.main.func5()
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/xos/serverstatus/cmd/dashboard/main.go:190 +0x17
Jun 20 17:10:02 Cat server-dash[15475]: github.com/ory/graceful.Graceful(0xc000041ec8, 0xc007834f90)
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/ory/graceful@v0.1.3/http_graceful.go:76 +0xaf
Jun 20 17:10:02 Cat server-dash[15475]: main.main()
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/xos/serverstatus/cmd/dashboard/main.go:189 +0x90e
Jun 20 17:10:02 Cat server-dash[15475]: goroutine 8 [select, 10 minutes]:
Jun 20 17:10:02 Cat server-dash[15475]: github.com/patrickmn/go-cache.(*janitor).Run(0xc00023cb70, 0xc00039ab80)
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/patrickmn/go-cache@v2.1.0+incompatible/cache.go:1079 +0x7b
Jun 20 17:10:02 Cat server-dash[15475]: created by github.com/patrickmn/go-cache.runJanitor in goroutine 1
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/patrickmn/go-cache@v2.1.0+incompatible/cache.go:1099 +0xdb
Jun 20 17:10:02 Cat server-dash[15475]: goroutine 9 [select, 22 minutes]:
Jun 20 17:10:02 Cat server-dash[15475]: github.com/patrickmn/go-cache.(*janitor).Run(0xc00023cb80, 0xc00039abc0)
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/patrickmn/go-cache@v2.1.0+incompatible/cache.go:1079 +0x7b
Jun 20 17:10:02 Cat server-dash[15475]: created by github.com/patrickmn/go-cache.runJanitor in goroutine 1
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/patrickmn/go-cache@v2.1.0+incompatible/cache.go:1099 +0xdb
Jun 20 17:10:02 Cat server-dash[15475]: goroutine 10 [chan receive, 22 minutes]:
Jun 20 17:10:02 Cat server-dash[15475]: github.com/xos/serverstatus/service/singleton.InitTimezoneAndCache.func1()
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/xos/serverstatus/service/singleton/singleton.go:58 +0xa5
Jun 20 17:10:02 Cat server-dash[15475]: created by github.com/xos/serverstatus/service/singleton.InitTimezoneAndCache in goroutine 1
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/xos/serverstatus/service/singleton/singleton.go:54 +0xf4
Jun 20 17:10:02 Cat server-dash[15475]: goroutine 11 [select]:
Jun 20 17:10:02 Cat server-dash[15475]: github.com/dgraph-io/badger/v3/y.(*WaterMark).process(0xc000234870, 0xc000234840)
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/dgraph-io/badger/v3@v3.2103.5/y/watermark.go:214 +0x267
Jun 20 17:10:02 Cat server-dash[15475]: created by github.com/dgraph-io/badger/v3/y.(*WaterMark).Init in goroutine 1
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/dgraph-io/badger/v3@v3.2103.5/y/watermark.go:72 +0xa7
Jun 20 17:10:02 Cat server-dash[15475]: goroutine 12 [select]:
Jun 20 17:10:02 Cat server-dash[15475]: github.com/dgraph-io/badger/v3/y.(*WaterMark).process(0xc0002348a0, 0xc000234840)
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/dgraph-io/badger/v3@v3.2103.5/y/watermark.go:214 +0x267
Jun 20 17:10:02 Cat server-dash[15475]: created by github.com/dgraph-io/badger/v3/y.(*WaterMark).Init in goroutine 1
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/dgraph-io/badger/v3@v3.2103.5/y/watermark.go:72 +0xa7
Jun 20 17:10:02 Cat server-dash[15475]: goroutine 13 [select]:
Jun 20 17:10:02 Cat server-dash[15475]: github.com/dgraph-io/ristretto/z.(*AllocatorPool).freeupAllocators(0xc0002899e0)
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/dgraph-io/ristretto@v0.2.0/z/allocator.go:385 +0x113
Jun 20 17:10:02 Cat server-dash[15475]: created by github.com/dgraph-io/ristretto/z.NewAllocatorPool in goroutine 1
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/dgraph-io/ristretto@v0.2.0/z/allocator.go:324 +0xb9
Jun 20 17:10:02 Cat server-dash[15475]: goroutine 14 [select, 10 minutes]:
Jun 20 17:10:02 Cat server-dash[15475]: github.com/dgraph-io/ristretto.(*defaultPolicy).processItems(0xc00021e140)
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/dgraph-io/ristretto@v0.2.0/policy.go:101 +0x7f
Jun 20 17:10:02 Cat server-dash[15475]: created by github.com/dgraph-io/ristretto.newDefaultPolicy in goroutine 1
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/dgraph-io/ristretto@v0.2.0/policy.go:85 +0x139
Jun 20 17:10:02 Cat server-dash[15475]: goroutine 15 [select]:
Jun 20 17:10:02 Cat server-dash[15475]: github.com/dgraph-io/ristretto.(*Cache).processItems(0xc00036c680)
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/dgraph-io/ristretto@v0.2.0/cache.go:459 +0x112
Jun 20 17:10:02 Cat server-dash[15475]: created by github.com/dgraph-io/ristretto.NewCache in goroutine 1
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/dgraph-io/ristretto@v0.2.0/cache.go:211 +0x669
Jun 20 17:10:02 Cat server-dash[15475]: goroutine 16 [select]:
Jun 20 17:10:02 Cat server-dash[15475]: github.com/dgraph-io/badger/v3.(*DB).monitorCache(0xc0000ae908, 0xc002342c30)
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/dgraph-io/badger/v3@v3.2103.5/db.go:469 +0x15d
Jun 20 17:10:02 Cat server-dash[15475]: created by github.com/dgraph-io/badger/v3.Open in goroutine 1
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/dgraph-io/badger/v3@v3.2103.5/db.go:311 +0xc09
Jun 20 17:10:02 Cat server-dash[15475]: goroutine 50 [select]:
Jun 20 17:10:02 Cat server-dash[15475]: github.com/dgraph-io/badger/v3.(*DB).updateSize(0xc0000ae908, 0xc002342cf0)
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/dgraph-io/badger/v3@v3.2103.5/db.go:1171 +0x13e
Jun 20 17:10:02 Cat server-dash[15475]: created by github.com/dgraph-io/badger/v3.Open in goroutine 1
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/dgraph-io/badger/v3@v3.2103.5/db.go:331 +0xe16
Jun 20 17:10:02 Cat server-dash[15475]: goroutine 939452 [select]:
Jun 20 17:10:02 Cat server-dash[15475]: github.com/xos/serverstatus/service/rpc.(*ServerHandler).RequestTask(0xc008b3fe90, 0x13bcfa0?, {0x359e8b0, 0xc00e890650})
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/xos/serverstatus/service/rpc/server.go:304 +0x77f
Jun 20 17:10:02 Cat server-dash[15475]: github.com/xos/serverstatus/proto._ServerService_RequestTask_Handler({0x13bcfa0, 0xc008b3fe90}, {0x359d828, 0xc0179e4a80})
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/xos/serverstatus/proto/server_grpc.pb.go:254 +0x110
Jun 20 17:10:02 Cat server-dash[15475]: google.golang.org/grpc.(*Server).processStreamingRPC(0xc00263e600, {0x3599a08, 0xc00e4f7200}, 0xc00008bb00, 0xc0001362d0, 0x3dedf60, 0x0)
Jun 20 17:10:02 Cat server-dash[15475]:         google.golang.org/grpc@v1.72.0/server.go:1695 +0x1252
Jun 20 17:10:02 Cat server-dash[15475]: google.golang.org/grpc.(*Server).handleStream(0xc00263e600, {0x359a4e8, 0xc008874000}, 0xc00008bb00)
Jun 20 17:10:02 Cat server-dash[15475]:         google.golang.org/grpc@v1.72.0/server.go:1819 +0xb47
Jun 20 17:10:02 Cat server-dash[15475]: google.golang.org/grpc.(*Server).serveStreams.func2.1()
Jun 20 17:10:02 Cat server-dash[15475]:         google.golang.org/grpc@v1.72.0/server.go:1035 +0x7f
Jun 20 17:10:02 Cat server-dash[15475]: created by google.golang.org/grpc.(*Server).serveStreams.func2 in goroutine 938554
Jun 20 17:10:02 Cat server-dash[15475]:         google.golang.org/grpc@v1.72.0/server.go:1046 +0x11d
Jun 20 17:10:02 Cat server-dash[15475]: goroutine 939599 [select]:
Jun 20 17:10:02 Cat server-dash[15475]: google.golang.org/grpc/internal/transport.(*controlBuffer).get(0xc0079f2000, 0x1)
Jun 20 17:10:02 Cat server-dash[15475]:         google.golang.org/grpc@v1.72.0/internal/transport/controlbuf.go:412 +0x108
Jun 20 17:10:02 Cat server-dash[15475]: google.golang.org/grpc/internal/transport.(*loopyWriter).run(0xc0043fc100)
Jun 20 17:10:02 Cat server-dash[15475]:         google.golang.org/grpc@v1.72.0/internal/transport/controlbuf.go:575 +0x78
Jun 20 17:10:02 Cat server-dash[15475]: google.golang.org/grpc/internal/transport.NewServerTransport.func2()
Jun 20 17:10:02 Cat server-dash[15475]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:336 +0xde
Jun 20 17:10:02 Cat server-dash[15475]: created by google.golang.org/grpc/internal/transport.NewServerTransport in goroutine 939489
Jun 20 17:10:02 Cat server-dash[15475]:         google.golang.org/grpc@v1.72.0/internal/transport/http2_server.go:334 +0x189b
Jun 20 17:10:02 Cat server-dash[15475]: goroutine 30 [select]:
Jun 20 17:10:02 Cat server-dash[15475]: github.com/dgraph-io/badger/v3.(*levelsController).runCompactor(0xc000218460, 0x0, 0xc00e04e030)
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/dgraph-io/badger/v3@v3.2103.5/levels.go:508 +0x229
Jun 20 17:10:02 Cat server-dash[15475]: created by github.com/dgraph-io/badger/v3.(*levelsController).startCompact in goroutine 1
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/dgraph-io/badger/v3@v3.2103.5/levels.go:354 +0x58
Jun 20 17:10:02 Cat server-dash[15475]: goroutine 31 [select]:
Jun 20 17:10:02 Cat server-dash[15475]: github.com/dgraph-io/badger/v3.(*levelsController).runCompactor(0xc000218460, 0x1, 0xc00e04e030)
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/dgraph-io/badger/v3@v3.2103.5/levels.go:508 +0x229
Jun 20 17:10:02 Cat server-dash[15475]: created by github.com/dgraph-io/badger/v3.(*levelsController).startCompact in goroutine 1
Jun 20 17:10:02 Cat server-dash[15475]:         github.com/dgraph-io/badger/v3@v3.2103.5/levels.go:354 +0x58
Jun 20 17:10:02 Cat server-dash[15475]: goroutine 32 [select]:
Jun 20 17:10:02 Cat server-dash[15475]: github.com/dgraph-io/badger/v3.(*levelsController).runCompactor(0xc000218460, 0x2, 0xc00e04e030)";


