Jun 20 22:26:50 Cat server-dash[30097]: 2025/06/20 22:26:50 数据大小限制完成: 状态记录=54, 监控项=9
Jun 20 22:27:00 Cat server-dash[30097]: [GIN] 2025/06/20 - 22:27:00 | 200 |     838.342µs |   ************* | GET      "/"
Jun 20 22:27:00 Cat server-dash[30097]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:37018: write: broken pipe
Jun 20 22:30:00 Cat server-dash[30097]: 2025/06/20 22:30:00 执行监控历史记录一致性检查...
Jun 20 22:30:00 Cat server-dash[30097]: 2025/06/20 22:30:00 BadgerDB模式：监控历史记录检查完成
Jun 20 22:30:00 Cat server-dash[30097]: 2025/06/20 22:30:00 BadgerDB模式：流量数据一致性检查完成
Jun 20 22:30:00 Cat server-dash[30097]: 2025/06/20 22:30:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 20 22:30:00 Cat server-dash[30097]: 2025/06/20 22:30:00 BadgerDB: 成功保存 4 个用户的数据
Jun 20 22:30:00 Cat server-dash[30097]: 2025/06/20 22:30:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 20 22:30:00 Cat server-dash[30097]: 2025/06/20 22:30:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 20 22:30:00 Cat server-dash[30097]: 2025/06/20 22:30:00 BadgerDB: 成功保存 17 个服务器的累计流量数据
Jun 20 22:32:04 Cat server-dash[30097]: [GIN] 2025/06/20 - 22:32:04 | 200 |     968.376µs |  ************** | GET      "/"
Jun 20 22:32:04 Cat server-dash[30097]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:21380: write: broken pipe
Jun 20 22:35:00 Cat server-dash[30097]: 2025/06/20 22:35:00 BadgerDB模式：流量数据一致性检查完成
Jun 20 22:35:00 Cat server-dash[30097]: 2025/06/20 22:35:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 20 22:35:00 Cat server-dash[30097]: 2025/06/20 22:35:00 BadgerDB: 成功保存 4 个用户的数据
Jun 20 22:35:00 Cat server-dash[30097]: 2025/06/20 22:35:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 20 22:35:00 Cat server-dash[30097]: 2025/06/20 22:35:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 20 22:37:39 Cat server-dash[30097]: 2025/06/20 22:37:39 network: 进入网络页面处理函数
Jun 20 22:37:39 Cat server-dash[30097]: 2025/06/20 22:37:39 network: 使用BadgerDB模式，跳过GORM查询监控历史
Jun 20 22:37:39 Cat server-dash[30097]: 2025/06/20 22:37:39 network: 从监控配置构建监控服务器ID列表
Jun 20 22:37:40 Cat server-dash[30097]: [GIN] 2025/06/20 - 22:37:40 | 200 |  520.837479ms |  39.144.187.158 | GET      "/network"
Jun 20 22:37:41 Cat server-dash[30097]: [GIN] 2025/06/20 - 22:37:41 | 200 |     129.252µs |  39.144.187.158 | GET      "/api/v1/monitor/configs"
Jun 20 22:37:42 Cat server-dash[30097]: [GIN] 2025/06/20 - 22:37:42 | 200 |    1.201723ms |  39.144.187.158 | GET      "/"
Jun 20 22:37:43 Cat server-dash[30097]: [GIN] 2025/06/20 - 22:37:43 | 200 |      1.4026ms |  39.144.187.158 | GET      "/static/webfonts/fa-solid-900.woff2"
Jun 20 22:37:43 Cat server-dash[30097]: [GIN] 2025/06/20 - 22:37:43 | 200 |       31.77µs |  39.144.187.158 | GET      "/api/search-server?word="
Jun 20 22:40:00 Cat server-dash[30097]: 2025/06/20 22:40:00 执行监控历史记录一致性检查...
Jun 20 22:40:00 Cat server-dash[30097]: 2025/06/20 22:40:00 BadgerDB模式：监控历史记录检查完成
Jun 20 22:40:00 Cat server-dash[30097]: 2025/06/20 22:40:00 BadgerDB模式：流量数据一致性检查完成
Jun 20 22:40:00 Cat server-dash[30097]: 2025/06/20 22:40:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 20 22:40:00 Cat server-dash[30097]: 2025/06/20 22:40:00 BadgerDB: 成功保存 4 个用户的数据
Jun 20 22:40:00 Cat server-dash[30097]: 2025/06/20 22:40:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 20 22:40:00 Cat server-dash[30097]: 2025/06/20 22:40:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌
Jun 20 22:40:00 Cat server-dash[30097]: 2025/06/20 22:40:00 BadgerDB: 成功保存 17 个服务器的累计流量数据
Jun 20 22:41:02 Cat server-dash[30097]: [GIN] 2025/06/20 - 22:41:02 | 200 |     931.487µs |   ************* | GET      "/"
Jun 20 22:41:02 Cat server-dash[30097]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:60180: write: broken pipe
Jun 20 22:42:44 Cat server-dash[30097]: 2025/06/20 22:42:44 DispatchTask goroutine panic恢复: runtime error: invalid memory address or nil pointer dereference
Jun 20 22:45:00 Cat server-dash[30097]: 2025/06/20 22:45:00 BadgerDB模式：流量数据一致性检查完成
Jun 20 22:45:00 Cat server-dash[30097]: 2025/06/20 22:45:00 BadgerDB: 成功保存 17 个服务器的完整数据
Jun 20 22:45:00 Cat server-dash[30097]: 2025/06/20 22:45:00 BadgerDB: 成功保存 4 个用户的数据
Jun 20 22:45:00 Cat server-dash[30097]: 2025/06/20 22:45:00 BadgerDB: DDNS状态数据已在变更时保存
Jun 20 22:45:00 Cat server-dash[30097]: 2025/06/20 22:45:00 BadgerDB: 成功保存 0 个API令牌，跳过 2 个已存在的令牌，删除 0 个过期令牌