问题：
一、"/network" 页面打开很慢，切换仍旧很慢很卡; ✅ 已修复
二、所有监测点名称恢复至之前的统一命名名称； ✅ 已修复

前端输出:"=== parseMonitorInfo 开始 ===
network:549 接收到的monitorInfo: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]0: {ID: 0, CreatedAt: '2025-06-18T17:15:13.522854849+08:00', UpdatedAt: '2025-06-18T17:15:13.522855139+08:00', DeletedAt: null, MonitorID: 1, …}1: {ID: 0, CreatedAt: '2025-06-18T17:18:13.685301227+08:00', UpdatedAt: '2025-06-18T17:18:13.685301517+08:00', DeletedAt: null, MonitorID: 1, …}2: {ID: 0, CreatedAt: '2025-06-18T17:20:13.511189682+08:00', UpdatedAt: '2025-06-18T17:20:13.511189752+08:00', DeletedAt: null, MonitorID: 1, …}3: {ID: 0, CreatedAt: '2025-06-18T17:23:13.404040344+08:00', UpdatedAt: '2025-06-18T17:23:13.404040424+08:00', DeletedAt: null, MonitorID: 1, …}4: {ID: 0, CreatedAt: '2025-06-18T17:25:13.384363549+08:00', UpdatedAt: '2025-06-18T17:25:13.384363619+08:00', DeletedAt: null, MonitorID: 1, …}5: {ID: 0, CreatedAt: '2025-06-18T17:27:13.398978594+08:00', UpdatedAt: '2025-06-18T17:27:13.398978654+08:00', DeletedAt: null, MonitorID: 1, …}6: {ID: 0, CreatedAt: '2025-06-18T17:30:13.381865762+08:00', UpdatedAt: '2025-06-18T17:30:13.381865812+08:00', DeletedAt: null, MonitorID: 1, …}7: {ID: 0, CreatedAt: '2025-06-18T17:32:13.716256918+08:00', UpdatedAt: '2025-06-18T17:32:13.716256998+08:00', DeletedAt: null, MonitorID: 1, …}8: {ID: 0, CreatedAt: '2025-06-18T17:35:13.774445123+08:00', UpdatedAt: '2025-06-18T17:35:13.774445203+08:00', DeletedAt: null, MonitorID: 1, …}9: {ID: 0, CreatedAt: '2025-06-18T17:37:13.645830128+08:00', UpdatedAt: '2025-06-18T17:37:13.645830208+08:00', DeletedAt: null, MonitorID: 1, …}10: {ID: 0, CreatedAt: '2025-06-18T17:39:13.423119816+08:00', UpdatedAt: '2025-06-18T17:39:13.423119906+08:00', DeletedAt: null, MonitorID: 1, …}11: {ID: 0, CreatedAt: '2025-06-18T17:42:13.564303916+08:00', UpdatedAt: '2025-06-18T17:42:13.564303996+08:00', DeletedAt: null, MonitorID: 1, …}12: {ID: 0, CreatedAt: '2025-06-18T17:44:13.4700597+08:00', UpdatedAt: '2025-06-18T17:44:13.47005977+08:00', DeletedAt: null, MonitorID: 1, …}13: {ID: 0, CreatedAt: '2025-06-18T17:47:13.403514618+08:00', UpdatedAt: '2025-06-18T17:47:13.403514698+08:00', DeletedAt: null, MonitorID: 1, …}14: {ID: 0, CreatedAt: '2025-06-18T17:49:13.346610238+08:00', UpdatedAt: '2025-06-18T17:49:13.346610498+08:00', DeletedAt: null, MonitorID: 1, …}15: {ID: 0, CreatedAt: '2025-06-18T17:52:13.805334165+08:00', UpdatedAt: '2025-06-18T17:52:13.805334235+08:00', DeletedAt: null, MonitorID: 1, …}16: {ID: 0, CreatedAt: '2025-06-18T17:54:13.388448546+08:00', UpdatedAt: '2025-06-18T17:54:13.388448626+08:00', DeletedAt: null, MonitorID: 1, …}17: {ID: 0, CreatedAt: '2025-06-18T17:56:13.335278698+08:00', UpdatedAt: '2025-06-18T17:56:13.335278778+08:00', DeletedAt: null, MonitorID: 1, …}18: {ID: 0, CreatedAt: '2025-06-18T17:59:16.375888864+08:00', UpdatedAt: '2025-06-18T17:59:16.375888945+08:00', DeletedAt: null, MonitorID: 1, …}19: {ID: 0, CreatedAt: '2025-06-18T18:01:13.403803766+08:00', UpdatedAt: '2025-06-18T18:01:13.403803827+08:00', DeletedAt: null, MonitorID: 1, …}20: {ID: 0, CreatedAt: '2025-06-18T18:04:13.425292917+08:00', UpdatedAt: '2025-06-18T18:04:13.425293178+08:00', DeletedAt: null, MonitorID: 1, …}21: {ID: 0, CreatedAt: '2025-06-18T18:06:13.333260087+08:00', UpdatedAt: '2025-06-18T18:06:13.333260167+08:00', DeletedAt: null, MonitorID: 1, …}22: {ID: 0, CreatedAt: '2025-06-18T18:34:41.564978948+08:00', UpdatedAt: '2025-06-18T18:34:41.564979209+08:00', DeletedAt: null, MonitorID: 1, …}23: {ID: 0, CreatedAt: '2025-06-18T18:36:41.48230105+08:00', UpdatedAt: '2025-06-18T18:36:41.48230112+08:00', DeletedAt: null, MonitorID: 1, …}24: {ID: 0, CreatedAt: '2025-06-18T18:39:41.368670999+08:00', UpdatedAt: '2025-06-18T18:39:41.368671289+08:00', DeletedAt: null, MonitorID: 1, …}25: {ID: 0, CreatedAt: '2025-06-18T18:41:41.489169556+08:00', UpdatedAt: '2025-06-18T18:41:41.489169636+08:00', DeletedAt: null, MonitorID: 1, …}26: {ID: 0, CreatedAt: '2025-06-18T18:43:41.546402144+08:00', UpdatedAt: '2025-06-18T18:43:41.546402203+08:00', DeletedAt: null, MonitorID: 1, …}27: {ID: 0, CreatedAt: '2025-06-18T18:47:41.376059918+08:00', UpdatedAt: '2025-06-18T18:47:41.376059978+08:00', DeletedAt: null, MonitorID: 1, …}28: {ID: 0, CreatedAt: '2025-06-18T18:49:41.424860845+08:00', UpdatedAt: '2025-06-18T18:49:41.424860895+08:00', DeletedAt: null, MonitorID: 1, …}29: {ID: 0, CreatedAt: '2025-06-18T18:52:41.57470874+08:00', UpdatedAt: '2025-06-18T18:52:41.57470881+08:00', DeletedAt: null, MonitorID: 1, …}30: {ID: 0, CreatedAt: '2025-06-18T18:54:41.424776033+08:00', UpdatedAt: '2025-06-18T18:54:41.424776113+08:00', DeletedAt: null, MonitorID: 1, …}31: {ID: 0, CreatedAt: '2025-06-18T18:57:41.421033231+08:00', UpdatedAt: '2025-06-18T18:57:41.421033281+08:00', DeletedAt: null, MonitorID: 1, …}32: {ID: 0, CreatedAt: '2025-06-18T18:59:41.434012479+08:00', UpdatedAt: '2025-06-18T18:59:41.434012549+08:00', DeletedAt: null, MonitorID: 1, …}33: {ID: 0, CreatedAt: '2025-06-18T19:02:41.4248365+08:00', UpdatedAt: '2025-06-18T19:02:41.42483657+08:00', DeletedAt: null, MonitorID: 1, …}34: {ID: 0, CreatedAt: '2025-06-18T19:04:41.418893036+08:00', UpdatedAt: '2025-06-18T19:04:41.418893146+08:00', DeletedAt: null, MonitorID: 1, …}35: {ID: 0, CreatedAt: '2025-06-18T19:06:41.468874357+08:00', UpdatedAt: '2025-06-18T19:06:41.468874437+08:00', DeletedAt: null, MonitorID: 1, …}36: {ID: 0, CreatedAt: '2025-06-18T19:09:41.36018495+08:00', UpdatedAt: '2025-06-18T19:09:41.36018502+08:00', DeletedAt: null, MonitorID: 1, …}37: {ID: 0, CreatedAt: '2025-06-18T19:11:42.424101304+08:00', UpdatedAt: '2025-06-18T19:11:42.424101364+08:00', DeletedAt: null, MonitorID: 1, …}38: {ID: 0, CreatedAt: '2025-06-18T19:14:41.405751589+08:00', UpdatedAt: '2025-06-18T19:14:41.405751649+08:00', DeletedAt: null, MonitorID: 1, …}39: {ID: 0, CreatedAt: '2025-06-18T19:17:41.406329239+08:00', UpdatedAt: '2025-06-18T19:17:41.406329298+08:00', DeletedAt: null, MonitorID: 1, …}40: {ID: 0, CreatedAt: '2025-06-18T19:20:41.343295761+08:00', UpdatedAt: '2025-06-18T19:20:41.343295862+08:00', DeletedAt: null, MonitorID: 1, …}41: {ID: 0, CreatedAt: '2025-06-18T19:22:41.319661616+08:00', UpdatedAt: '2025-06-18T19:22:41.319661806+08:00', DeletedAt: null, MonitorID: 1, …}42: {ID: 0, CreatedAt: '2025-06-18T19:24:41.404777346+08:00', UpdatedAt: '2025-06-18T19:24:41.404777537+08:00', DeletedAt: null, MonitorID: 1, …}43: {ID: 0, CreatedAt: '2025-06-18T19:27:41.355654563+08:00', UpdatedAt: '2025-06-18T19:27:41.355654643+08:00', DeletedAt: null, MonitorID: 1, …}44: {ID: 0, CreatedAt: '2025-06-18T19:29:41.355609449+08:00', UpdatedAt: '2025-06-18T19:29:41.35560952+08:00', DeletedAt: null, MonitorID: 1, …}45: {ID: 0, CreatedAt: '2025-06-18T19:31:42.414917473+08:00', UpdatedAt: '2025-06-18T19:31:42.414917532+08:00', DeletedAt: null, MonitorID: 1, …}46: {ID: 0, CreatedAt: '2025-06-18T19:34:41.358688069+08:00', UpdatedAt: '2025-06-18T19:34:41.35868824+08:00', DeletedAt: null, MonitorID: 1, …}47: {ID: 0, CreatedAt: '2025-06-18T19:36:41.347565434+08:00', UpdatedAt: '2025-06-18T19:36:41.347565504+08:00', DeletedAt: null, MonitorID: 1, …}48: {ID: 0, CreatedAt: '2025-06-18T19:38:41.39224855+08:00', UpdatedAt: '2025-06-18T19:38:41.39224862+08:00', DeletedAt: null, MonitorID: 1, …}49: {ID: 0, CreatedAt: '2025-06-18T19:41:51.16280037+08:00', UpdatedAt: '2025-06-18T19:41:51.16280045+08:00', DeletedAt: null, MonitorID: 1, …}length: 50[[Prototype]]: Array(0)
network:550 monitorInfo类型: object
network:551 是否为数组: true
network:561 使用直接数组格式
network:573 最终dataArray长度: 50
network:332 === 开始处理监控历史数据 ===
network:333 原始数据数量: 50
network:334 原始数据: (50) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
network:360 处理记录 0: MonitorID=1, ServerID=41, UniqueKey=1_41
network:363 创建新的监控点分组: 1_41
network:360 处理记录 1: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 2: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 3: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 4: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 5: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 6: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 7: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 8: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 9: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 10: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 11: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 12: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 13: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 14: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 15: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 16: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 17: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 18: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 19: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 20: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 21: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 22: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 23: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 24: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 25: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 26: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 27: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 28: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 29: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 30: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 31: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 32: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 33: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 34: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 35: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 36: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 37: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 38: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 39: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 40: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 41: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 42: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 43: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 44: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 45: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 46: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 47: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 48: MonitorID=1, ServerID=41, UniqueKey=1_41
network:360 处理记录 49: MonitorID=1, ServerID=41, UniqueKey=1_41
network:534 获取监控名称 - ID: 1
network:535 监控配置: {1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}}
network:536 配置已加载: true
network:540 找到监控配置: 重庆电信
network:434 === 数据处理完成 ===
network:435 最终监控点数量: 1
network:436 监控点详情: [{…}]
network:443 ===================
content_script.js:1 [Violation] 'load' handler took 209ms
network:548 === parseMonitorInfo 开始 ===
network:549 接收到的monitorInfo: null
network:550 monitorInfo类型: object
network:551 是否为数组: false
network:573 最终dataArray长度: 0
network:548 === parseMonitorInfo 开始 ===
network:549 接收到的monitorInfo: null
network:550 monitorInfo类型: object
network:551 是否为数组: false
network:573 最终dataArray长度: 0
network:548 === parseMonitorInfo 开始 ===
network:549 接收到的monitorInfo: null
network:550 monitorInfo类型: object
network:551 是否为数组: false
network:573 最终dataArray长度: 0
content_script.js:1 [Violation] 'load' handler took 152ms
network:548 === parseMonitorInfo 开始 ===
network:549 接收到的monitorInfo: null
network:550 monitorInfo类型: object
network:551 是否为数组: false
network:573 最终dataArray长度: 0
network:548 === parseMonitorInfo 开始 ===
network:549 接收到的monitorInfo: null
network:550 monitorInfo类型: object
network:551 是否为数组: false
network:573 最终dataArray长度: 0
content_script.js:1 [Violation] 'load' handler took 167ms
content_script.js:1 [Violation] 'setTimeout' handler took 105ms
content_script.js:1 [Violation] 'load' handler took 186ms";

后端输出"Jun 19 17:13:47 Cat server-dash[63915]: 2025/06/19 17:13:47 network: 进入网络页面处理函数
Jun 19 17:13:47 Cat server-dash[63915]: 2025/06/19 17:13:47 network: 使用BadgerDB模式，跳过GORM查询监控历史
Jun 19 17:13:47 Cat server-dash[63915]: 2025/06/19 17:13:47 network: 从监控配置构建监控服务器ID列表
Jun 19 17:13:47 Cat server-dash[63915]: 2025/06/19 17:13:47 network: 找到 9 个监控配置
Jun 19 17:13:47 Cat server-dash[63915]: 2025/06/19 17:13:47 network: 处理监控配置 1: 重庆电信
Jun 19 17:13:47 Cat server-dash[63915]: 2025/06/19 17:13:47 network: 添加服务器 41 ([CC]纽约) 到监控列表 (Cover=1)
Jun 19 17:13:47 Cat server-dash[63915]: 2025/06/19 17:13:47 network: 添加服务器 43 ([LW]洛杉矶) 到监控列表 (Cover=1)
Jun 19 17:13:47 Cat server-dash[63915]: 2025/06/19 17:13:47 network: 添加服务器 9 ([MS]伦敦) 到监控列表 (Cover=1)
Jun 19 17:13:47 Cat server-dash[63915]: 2025/06/19 17:13:47 network: 添加服务器 40 ([HT]香港) 到监控列表 (Cover=1)
Jun 19 17:13:47 Cat server-dash[63915]: 2025/06/19 17:13:47 network: 添加服务器 5 ([MS]新加坡) 到监控列表 (Cover=1)
Jun 19 17:13:47 Cat server-dash[63915]: 2025/06/19 17:13:47 network: 添加服务器 22 ([GC]盐湖城) 到监控列表 (Cover=1)
Jun 19 17:13:47 Cat server-dash[63915]: 2025/06/19 17:13:47 network: 添加服务器 34 (北莱茵) 到监控列表 (Cover=1)
Jun 19 17:13:47 Cat server-dash[63915]: 2025/06/19 17:13:47 network: 添加服务器 4 ([AC]香港) 到监控列表 (Cover=1)
Jun 19 17:13:47 Cat server-dash[63915]: 2025/06/19 17:13:47 network: 添加服务器 6 ([MS]香港) 到监控列表 (Cover=1)
Jun 19 17:13:47 Cat server-dash[63915]: 2025/06/19 17:13:47 network: 添加服务器 8 ([MS]圣何塞) 到监控列表 (Cover=1)
Jun 19 17:13:47 Cat server-dash[63915]: 2025/06/19 17:13:47 network: 添加服务器 14 ([OC]春川②) 到监控列表 (Cover=1)
Jun 19 17:13:47 Cat server-dash[63915]: 2025/06/19 17:13:47 network: 添加服务器 23 ([MG]伦敦) 到监控列表 (Cover=1)
Jun 19 17:13:47 Cat server-dash[63915]: 2025/06/19 17:13:47 network: 处理监控配置 2: 重庆移动
Jun 19 17:13:47 Cat server-dash[63915]: 2025/06/19 17:13:47 network: 处理监控配置 3: 重庆联通
Jun 19 17:13:47 Cat server-dash[63915]: 2025/06/19 17:13:47 network: 处理监控配置 4: 上海联通
Jun 19 17:13:47 Cat server-dash[63915]: 2025/06/19 17:13:47 network: 处理监控配置 5: 上海移动
Jun 19 17:13:47 Cat server-dash[63915]: 2025/06/19 17:13:47 network: 处理监控配置 6: 上海电信
Jun 19 17:13:47 Cat server-dash[63915]: 2025/06/19 17:13:47 network: 处理监控配置 7: 河南联通
Jun 19 17:13:47 Cat server-dash[63915]: 2025/06/19 17:13:47 network: 处理监控配置 8: 河南移动
Jun 19 17:13:47 Cat server-dash[63915]: 2025/06/19 17:13:47 network: 处理监控配置 9: 河南电信
Jun 19 17:13:47 Cat server-dash[63915]: 2025/06/19 17:13:47 network: 从监控配置获取到 12 个有监控任务的服务器
Jun 19 17:13:47 Cat server-dash[63915]: 2025/06/19 17:13:47 network: BadgerDB模式，查询监控历史记录
Jun 19 17:13:51 Cat server-dash[63915]: 2025/06/19 17:13:51 network: 为服务器 41 找到 50 条ICMP/TCP监控历史记录
Jun 19 17:13:51 Cat server-dash[63915]: [GIN] 2025/06/19 - 17:13:51 | 200 |  4.151526253s |  ************** | GET      "/network"
Jun 19 17:13:52 Cat server-dash[63915]: 2025/06/19 17:13:52 API返回监控配置数量: 9
Jun 19 17:13:52 Cat server-dash[63915]: 2025/06/19 17:13:52 监控配置: ID=1, Name=重庆电信, Type=2
Jun 19 17:13:52 Cat server-dash[63915]: 2025/06/19 17:13:52 监控配置: ID=2, Name=重庆移动, Type=2
Jun 19 17:13:52 Cat server-dash[63915]: 2025/06/19 17:13:52 监控配置: ID=3, Name=重庆联通, Type=2
Jun 19 17:13:52 Cat server-dash[63915]: 2025/06/19 17:13:52 监控配置: ID=4, Name=上海联通, Type=2
Jun 19 17:13:52 Cat server-dash[63915]: 2025/06/19 17:13:52 监控配置: ID=5, Name=上海移动, Type=2
Jun 19 17:13:52 Cat server-dash[63915]: 2025/06/19 17:13:52 监控配置: ID=6, Name=上海电信, Type=2
Jun 19 17:13:52 Cat server-dash[63915]: 2025/06/19 17:13:52 监控配置: ID=7, Name=河南联通, Type=2
Jun 19 17:13:52 Cat server-dash[63915]: 2025/06/19 17:13:52 监控配置: ID=8, Name=河南移动, Type=2
Jun 19 17:13:52 Cat server-dash[63915]: 2025/06/19 17:13:52 监控配置: ID=9, Name=河南电信, Type=2
Jun 19 17:13:52 Cat server-dash[63915]: [GIN] 2025/06/19 - 17:13:52 | 200 |     217.527µs |  ************** | GET      "/api/v1/monitor/configs"
Jun 19 17:13:54 Cat server-dash[63915]: [GIN] 2025/06/19 - 17:13:54 | 200 |    1.419141ms | *************** | GET      "/"
Jun 19 17:13:54 Cat server-dash[63915]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:27280: write: broken pipe
Jun 19 17:14:12 Cat server-dash[63915]: 2025/06/19 17:14:12 服务器 6 的ICMP/TCP监控器ID列表: [1 2 3 4 5 6 7 8 9]
Jun 19 17:14:12 Cat server-dash[63915]: 2025/06/19 17:14:12 监控器 1 在服务器 6 上找到 0 条记录
Jun 19 17:14:12 Cat server-dash[63915]: 2025/06/19 17:14:12 监控器 2 在服务器 6 上找到 0 条记录
Jun 19 17:14:12 Cat server-dash[63915]: 2025/06/19 17:14:12 监控器 3 在服务器 6 上找到 0 条记录
Jun 19 17:14:12 Cat server-dash[63915]: 2025/06/19 17:14:12 监控器 4 在服务器 6 上找到 0 条记录
Jun 19 17:14:12 Cat server-dash[63915]: 2025/06/19 17:14:12 监控器 5 在服务器 6 上找到 0 条记录
Jun 19 17:14:12 Cat server-dash[63915]: 2025/06/19 17:14:12 监控器 6 在服务器 6 上找到 0 条记录
Jun 19 17:14:12 Cat server-dash[63915]: 2025/06/19 17:14:12 监控器 7 在服务器 6 上找到 0 条记录
Jun 19 17:14:12 Cat server-dash[63915]: 2025/06/19 17:14:12 监控器 8 在服务器 6 上找到 0 条记录
Jun 19 17:14:12 Cat server-dash[63915]: 2025/06/19 17:14:12 监控器 9 在服务器 6 上找到 0 条记录
Jun 19 17:14:12 Cat server-dash[63915]: 2025/06/19 17:14:12 服务器 6 最终返回 0 条ICMP/TCP监控记录
Jun 19 17:14:12 Cat server-dash[63915]: [GIN] 2025/06/19 - 17:14:12 | 200 |   13.641749ms |  ************** | GET      "/api/v1/monitor/6"
Jun 19 17:14:15 Cat server-dash[63915]: 2025/06/19 17:14:15 服务器 40 的ICMP/TCP监控器ID列表: [1 2 3 4 5 6 7 8 9]
Jun 19 17:14:15 Cat server-dash[63915]: 2025/06/19 17:14:15 监控器 1 在服务器 40 上找到 0 条记录
Jun 19 17:14:15 Cat server-dash[63915]: 2025/06/19 17:14:15 监控器 2 在服务器 40 上找到 0 条记录
Jun 19 17:14:15 Cat server-dash[63915]: 2025/06/19 17:14:15 监控器 3 在服务器 40 上找到 0 条记录
Jun 19 17:14:15 Cat server-dash[63915]: 2025/06/19 17:14:15 监控器 4 在服务器 40 上找到 0 条记录
Jun 19 17:14:15 Cat server-dash[63915]: 2025/06/19 17:14:15 监控器 5 在服务器 40 上找到 0 条记录
Jun 19 17:14:15 Cat server-dash[63915]: 2025/06/19 17:14:15 监控器 6 在服务器 40 上找到 0 条记录
Jun 19 17:14:15 Cat server-dash[63915]: 2025/06/19 17:14:15 监控器 7 在服务器 40 上找到 0 条记录
Jun 19 17:14:15 Cat server-dash[63915]: 2025/06/19 17:14:15 监控器 8 在服务器 40 上找到 0 条记录
Jun 19 17:14:15 Cat server-dash[63915]: 2025/06/19 17:14:15 监控器 9 在服务器 40 上找到 0 条记录
Jun 19 17:14:15 Cat server-dash[63915]: 2025/06/19 17:14:15 服务器 40 最终返回 0 条ICMP/TCP监控记录
Jun 19 17:14:15 Cat server-dash[63915]: [GIN] 2025/06/19 - 17:14:15 | 200 |   13.536482ms |  ************** | GET      "/api/v1/monitor/40"
Jun 19 17:14:16 Cat server-dash[63915]: 2025/06/19 17:14:16 服务器 4 的ICMP/TCP监控器ID列表: [1 2 3 4 5 6 7 8 9]
Jun 19 17:14:16 Cat server-dash[63915]: 2025/06/19 17:14:16 监控器 1 在服务器 4 上找到 0 条记录
Jun 19 17:14:16 Cat server-dash[63915]: 2025/06/19 17:14:16 监控器 2 在服务器 4 上找到 0 条记录
Jun 19 17:14:16 Cat server-dash[63915]: 2025/06/19 17:14:16 监控器 3 在服务器 4 上找到 0 条记录
Jun 19 17:14:16 Cat server-dash[63915]: 2025/06/19 17:14:16 监控器 4 在服务器 4 上找到 0 条记录
Jun 19 17:14:16 Cat server-dash[63915]: 2025/06/19 17:14:16 监控器 5 在服务器 4 上找到 0 条记录
Jun 19 17:14:16 Cat server-dash[63915]: 2025/06/19 17:14:16 监控器 6 在服务器 4 上找到 0 条记录
Jun 19 17:14:16 Cat server-dash[63915]: 2025/06/19 17:14:16 监控器 7 在服务器 4 上找到 0 条记录
Jun 19 17:14:16 Cat server-dash[63915]: 2025/06/19 17:14:16 监控器 8 在服务器 4 上找到 0 条记录
Jun 19 17:14:16 Cat server-dash[63915]: 2025/06/19 17:14:16 监控器 9 在服务器 4 上找到 0 条记录
Jun 19 17:14:16 Cat server-dash[63915]: 2025/06/19 17:14:16 服务器 4 最终返回 0 条ICMP/TCP监控记录
Jun 19 17:14:16 Cat server-dash[63915]: [GIN] 2025/06/19 - 17:14:16 | 200 |    9.787192ms |  ************** | GET      "/api/v1/monitor/4"
Jun 19 17:14:27 Cat server-dash[63915]: 2025/06/19 17:14:27 服务器 4 的ICMP/TCP监控器ID列表: [1 2 3 4 5 6 7 8 9]
Jun 19 17:14:27 Cat server-dash[63915]: 2025/06/19 17:14:27 监控器 1 在服务器 4 上找到 0 条记录
Jun 19 17:14:27 Cat server-dash[63915]: 2025/06/19 17:14:27 监控器 2 在服务器 4 上找到 0 条记录
Jun 19 17:14:27 Cat server-dash[63915]: 2025/06/19 17:14:27 监控器 3 在服务器 4 上找到 0 条记录
Jun 19 17:14:27 Cat server-dash[63915]: 2025/06/19 17:14:27 监控器 4 在服务器 4 上找到 0 条记录
Jun 19 17:14:27 Cat server-dash[63915]: 2025/06/19 17:14:27 监控器 5 在服务器 4 上找到 0 条记录
Jun 19 17:14:27 Cat server-dash[63915]: 2025/06/19 17:14:27 监控器 6 在服务器 4 上找到 0 条记录
Jun 19 17:14:27 Cat server-dash[63915]: 2025/06/19 17:14:27 监控器 7 在服务器 4 上找到 0 条记录
Jun 19 17:14:27 Cat server-dash[63915]: 2025/06/19 17:14:27 监控器 8 在服务器 4 上找到 0 条记录
Jun 19 17:14:27 Cat server-dash[63915]: 2025/06/19 17:14:27 监控器 9 在服务器 4 上找到 0 条记录
Jun 19 17:14:27 Cat server-dash[63915]: 2025/06/19 17:14:27 服务器 4 最终返回 0 条ICMP/TCP监控记录
Jun 19 17:14:27 Cat server-dash[63915]: [GIN] 2025/06/19 - 17:14:27 | 200 |    9.460479ms |  ************** | GET      "/api/v1/monitor/4"
Jun 19 17:14:28 Cat server-dash[63915]: 2025/06/19 17:14:28 服务器 9 的ICMP/TCP监控器ID列表: [1 2 3 4 5 6 7 8 9]
Jun 19 17:14:28 Cat server-dash[63915]: 2025/06/19 17:14:28 监控器 1 在服务器 9 上找到 0 条记录
Jun 19 17:14:28 Cat server-dash[63915]: 2025/06/19 17:14:28 监控器 2 在服务器 9 上找到 0 条记录
Jun 19 17:14:28 Cat server-dash[63915]: 2025/06/19 17:14:28 监控器 3 在服务器 9 上找到 0 条记录
Jun 19 17:14:28 Cat server-dash[63915]: 2025/06/19 17:14:28 监控器 4 在服务器 9 上找到 0 条记录
Jun 19 17:14:28 Cat server-dash[63915]: 2025/06/19 17:14:28 监控器 5 在服务器 9 上找到 0 条记录
Jun 19 17:14:28 Cat server-dash[63915]: 2025/06/19 17:14:28 监控器 6 在服务器 9 上找到 0 条记录
Jun 19 17:14:28 Cat server-dash[63915]: 2025/06/19 17:14:28 监控器 7 在服务器 9 上找到 0 条记录
Jun 19 17:14:28 Cat server-dash[63915]: 2025/06/19 17:14:28 监控器 8 在服务器 9 上找到 0 条记录
Jun 19 17:14:28 Cat server-dash[63915]: 2025/06/19 17:14:28 监控器 9 在服务器 9 上找到 0 条记录
Jun 19 17:14:28 Cat server-dash[63915]: 2025/06/19 17:14:28 服务器 9 最终返回 0 条ICMP/TCP监控记录
Jun 19 17:14:28 Cat server-dash[63915]: [GIN] 2025/06/19 - 17:14:28 | 200 |    9.819974ms |  ************** | GET      "/api/v1/monitor/9"";
