Jun 20 19:01:20 Cat server-dash[23054]: 2025/06/20 19:01:20 network: 进入网络页面处理函数
Jun 20 19:01:20 Cat server-dash[23054]: 2025/06/20 19:01:20 network: 使用BadgerDB模式，跳过GORM查询监控历史
Jun 20 19:01:20 Cat server-dash[23054]: 2025/06/20 19:01:20 network: 从监控配置构建监控服务器ID列表
Jun 20 19:01:20 Cat server-dash[23054]: 2025/06/20 19:01:20 NG>> 向  TG[佩佩]  发送通知成功：
Jun 20 19:01:24 Cat server-dash[23054]: [GIN] 2025/06/20 - 19:01:24 | 200 |  4.688098165s |  39.144.187.158 | GET      "/network"
Jun 20 19:01:25 Cat server-dash[23054]: [GIN] 2025/06/20 - 19:01:25 | 200 |    2.174978ms |  39.144.187.158 | GET      "/static/main.css?v2025060313"
Jun 20 19:01:25 Cat server-dash[23054]: [GIN] 2025/06/20 - 19:01:25 | 200 |    3.054728ms |  39.144.187.158 | GET      "/static/fontawesome.min.css?v2025032723"
Jun 20 19:01:25 Cat server-dash[23054]: [GIN] 2025/06/20 - 19:01:25 | 200 |     658.765µs |  39.144.187.158 | GET      "/static/logo.svg?v20220602"
Jun 20 19:01:26 Cat server-dash[23054]: [GIN] 2025/06/20 - 19:01:26 | 200 |    1.113048ms |  39.144.187.158 | GET      "/static/bg/pattern.svg"
Jun 20 19:01:27 Cat server-dash[23054]: [GIN] 2025/06/20 - 19:01:27 | 200 |     258.014µs |  39.144.187.158 | GET      "/api/v1/monitor/configs"
Jun 20 19:01:57 Cat server-dash[23054]: 2025/06/20 19:01:57 数据大小限制完成: 状态记录=54, 监控项=9
Jun 20 19:02:07 Cat server-dash[23054]: 2025/06/20 19:02:07 数据大小限制完成: 状态记录=54, 监控项=9
Jun 20 19:02:13 Cat server-dash[23054]: [GIN] 2025/06/20 - 19:02:13 | 200 | 25.593708281s |  39.144.187.158 | GET      "/api/v1/monitor/14"
Jun 20 19:02:17 Cat server-dash[23054]: 2025/06/20 19:02:17 数据大小限制完成: 状态记录=54, 监控项=9
Jun 20 19:02:57 Cat server-dash[23054]: 2025/06/20 19:02:57 数据大小限制完成: 状态记录=54, 监控项=9
Jun 20 19:03:00 Cat server-dash[23054]: [GIN] 2025/06/20 - 19:03:00 | 200 | 27.952646833s |  39.144.187.158 | GET      "/api/v1/monitor/4"
Jun 20 19:03:02 Cat server-dash[23054]: [GIN] 2025/06/20 - 19:03:02 | 200 | 28.605845862s |  39.144.187.158 | GET      "/api/v1/monitor/9"
