Jun 20 19:53:14 Cat server-dash[26109]: 2025/06/20 19:53:14 network: 进入网络页面处理函数
Jun 20 19:53:14 Cat server-dash[26109]: 2025/06/20 19:53:14 network: 使用BadgerDB模式，跳过GORM查询监控历史
Jun 20 19:53:14 Cat server-dash[26109]: 2025/06/20 19:53:14 network: 从监控配置构建监控服务器ID列表
Jun 20 19:53:20 Cat server-dash[26109]: [GIN] 2025/06/20 - 19:53:20 | 200 |   5.34121981s |  ************** | GET      "/network"
Jun 20 19:53:21 Cat server-dash[26109]: [GIN] 2025/06/20 - 19:53:21 | 200 |      154.96µs |  ************** | GET      "/api/v1/monitor/configs"
Jun 20 19:53:28 Cat server-dash[26109]: 2025/06/20 19:53:28 API /monitor/5 返回 15461 条记录（3天数据，所有监控器）
Jun 20 19:53:28 Cat server-dash[26109]: [GIN] 2025/06/20 - 19:53:28 | 200 |  368.344009ms |  ************** | GET      "/api/v1/monitor/5"
Jun 20 19:53:32 Cat server-dash[26109]: 2025/06/20 19:53:32 进入内存高压模式: 内存=538MB, Goroutines=663
Jun 20 19:53:32 Cat server-dash[26109]: 2025/06/20 19:53:32 执行适度内存清理...
Jun 20 19:53:32 Cat server-dash[26109]: 2025/06/20 19:53:32 内存清理完成，清理时间：2025-06-20 19:53:32.597668826 +0800 CST m=+140.048459960
Jun 20 19:53:32 Cat server-dash[26109]: 2025/06/20 19:53:32 报警系统内存清理完成: 清理了 0 个失效报警规则, 0 个服务器历史记录, 释放内存 142MB
Jun 20 19:53:32 Cat server-dash[26109]: 2025/06/20 19:53:32 API /monitor/43 返回 15725 条记录（3天数据，所有监控器）
Jun 20 19:53:32 Cat server-dash[26109]: 2025/06/20 19:53:32 适度内存清理完成
Jun 20 19:53:32 Cat server-dash[26109]: [GIN] 2025/06/20 - 19:53:32 | 200 |  435.876029ms |  ************** | GET      "/api/v1/monitor/43"
Jun 20 19:53:51 Cat server-dash[26109]: [GIN] 2025/06/20 - 19:53:51 | 200 |     1.67957ms |  ************** | GET      "/"
Jun 20 19:53:51 Cat server-dash[26109]: Error #01: write tcp 127.0.0.1:1001->127.0.0.1:53434: write: broken pipe
Jun 20 19:54:00 Cat server-dash[26109]: 2025/06/20 19:54:00 API /monitor/22 返回 15316 条记录（3天数据，所有监控器）
Jun 20 19:54:01 Cat server-dash[26109]: [GIN] 2025/06/20 - 19:54:01 | 200 |  378.200582ms |  ************** | GET      "/api/v1/monitor/22"
Jun 20 19:54:04 Cat server-dash[26109]: 2025/06/20 19:54:04 API /monitor/41 返回 15321 条记录（3天数据，所有监控器）
Jun 20 19:54:04 Cat server-dash[26109]: [GIN] 2025/06/20 - 19:54:04 | 200 |  437.203207ms |  ************** | GET      "/api/v1/monitor/41"
Jun 20 19:54:09 Cat server-dash[26109]: 2025/06/20 19:54:09 API /monitor/4 返回 15108 条记录（3天数据，所有监控器）
Jun 20 19:54:09 Cat server-dash[26109]: [GIN] 2025/06/20 - 19:54:09 | 200 |   410.85962ms |  ************** | GET      "/api/v1/monitor/4"
Jun 20 19:54:42 Cat server-dash[26109]: 2025/06/20 19:54:42 数据大小限制完成: 状态记录=54, 监控项=9
Jun 20 19:54:52 Cat server-dash[26109]: 2025/06/20 19:54:52 network: 进入网络页面处理函数
Jun 20 19:54:52 Cat server-dash[26109]: 2025/06/20 19:54:52 network: 使用BadgerDB模式，跳过GORM查询监控历史
Jun 20 19:54:52 Cat server-dash[26109]: 2025/06/20 19:54:52 network: 从监控配置构建监控服务器ID列表
Jun 20 19:54:57 Cat server-dash[26109]: [GIN] 2025/06/20 - 19:54:57 | 200 |   4.68418675s |  ************** | GET      "/network"
Jun 20 19:54:58 Cat server-dash[26109]: [GIN] 2025/06/20 - 19:54:58 | 200 |      149.28µs |  ************** | GET      "/api/v1/monitor/configs"
