package singleton

import (
	"context"
	"fmt"
	"log"
	"runtime"
	"sync"
	"time"
)

// MemoryLeakFixer 根本性内存泄漏修复器
type MemoryLeakFixer struct {
	ctx    context.Context
	cancel context.CancelFunc
	ticker *time.Ticker
	mu     sync.RWMutex

	// 跟踪各种资源
	activeChannels   map[string]chan struct{}
	activeTickers    map[string]*time.Ticker
	activeGoroutines map[string]context.CancelFunc

	// 内存统计
	lastMemStats     runtime.MemStats
	memoryGrowthRate float64

	// 配置
	maxMemoryMB     uint64
	maxGoroutines   int
	cleanupInterval time.Duration
}

// NewMemoryLeakFixer 创建内存泄漏修复器
func NewMemoryLeakFixer() *MemoryLeakFixer {
	ctx, cancel := context.WithCancel(context.Background())

	return &MemoryLeakFixer{
		ctx:              ctx,
		cancel:           cancel,
		activeChannels:   make(map[string]chan struct{}),
		activeTickers:    make(map[string]*time.Ticker),
		activeGoroutines: make(map[string]context.CancelFunc),
		maxMemoryMB:      800,             // 800MB硬限制
		maxGoroutines:    200,             // 200个goroutine硬限制
		cleanupInterval:  time.Minute * 2, // 每2分钟检查一次
	}
}

// Start 启动内存泄漏修复器
func (mlf *MemoryLeakFixer) Start() {
	mlf.ticker = time.NewTicker(mlf.cleanupInterval)

	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Printf("MemoryLeakFixer panic恢复: %v", r)
			}
		}()

		for {
			select {
			case <-mlf.ticker.C:
				mlf.performDeepCleanup()
			case <-mlf.ctx.Done():
				return
			}
		}
	}()

	log.Printf("内存泄漏修复器已启动")
}

// Stop 停止内存泄漏修复器
func (mlf *MemoryLeakFixer) Stop() {
	if mlf.cancel != nil {
		mlf.cancel()
	}
	if mlf.ticker != nil {
		mlf.ticker.Stop()
	}

	// 清理所有跟踪的资源
	mlf.cleanupAllResources()
}

// RegisterChannel 注册需要跟踪的channel
func (mlf *MemoryLeakFixer) RegisterChannel(name string, ch chan struct{}) {
	mlf.mu.Lock()
	defer mlf.mu.Unlock()

	mlf.activeChannels[name] = ch
}

// UnregisterChannel 注销channel
func (mlf *MemoryLeakFixer) UnregisterChannel(name string) {
	mlf.mu.Lock()
	defer mlf.mu.Unlock()

	if ch, exists := mlf.activeChannels[name]; exists {
		// 安全关闭channel
		select {
		case <-ch:
			// channel已经关闭
		default:
			close(ch)
		}
		delete(mlf.activeChannels, name)
	}
}

// RegisterTicker 注册需要跟踪的ticker
func (mlf *MemoryLeakFixer) RegisterTicker(name string, ticker *time.Ticker) {
	mlf.mu.Lock()
	defer mlf.mu.Unlock()

	mlf.activeTickers[name] = ticker
}

// UnregisterTicker 注销ticker
func (mlf *MemoryLeakFixer) UnregisterTicker(name string) {
	mlf.mu.Lock()
	defer mlf.mu.Unlock()

	if ticker, exists := mlf.activeTickers[name]; exists {
		ticker.Stop()
		delete(mlf.activeTickers, name)
	}
}

// RegisterGoroutine 注册需要跟踪的goroutine
func (mlf *MemoryLeakFixer) RegisterGoroutine(name string, cancel context.CancelFunc) {
	mlf.mu.Lock()
	defer mlf.mu.Unlock()

	mlf.activeGoroutines[name] = cancel
}

// UnregisterGoroutine 注销goroutine
func (mlf *MemoryLeakFixer) UnregisterGoroutine(name string) {
	mlf.mu.Lock()
	defer mlf.mu.Unlock()

	if cancel, exists := mlf.activeGoroutines[name]; exists {
		cancel()
		delete(mlf.activeGoroutines, name)
	}
}

// performDeepCleanup 执行深度清理
func (mlf *MemoryLeakFixer) performDeepCleanup() {
	var currentMem runtime.MemStats
	runtime.ReadMemStats(&currentMem)

	currentGoroutines := runtime.NumGoroutine()
	currentMemMB := currentMem.Alloc / 1024 / 1024

	// 计算内存增长率
	if mlf.lastMemStats.Alloc > 0 {
		mlf.memoryGrowthRate = float64(currentMem.Alloc-mlf.lastMemStats.Alloc) / float64(mlf.lastMemStats.Alloc) * 100
	}
	mlf.lastMemStats = currentMem

	log.Printf("内存检查: %dMB, Goroutines: %d, 增长率: %.2f%%",
		currentMemMB, currentGoroutines, mlf.memoryGrowthRate)

	// 检查是否需要紧急清理
	needEmergencyCleanup := currentMemMB > mlf.maxMemoryMB ||
		currentGoroutines > mlf.maxGoroutines ||
		mlf.memoryGrowthRate > 50 // 增长率超过50%

	if needEmergencyCleanup {
		log.Printf("触发紧急清理: 内存=%dMB, Goroutines=%d, 增长率=%.2f%%",
			currentMemMB, currentGoroutines, mlf.memoryGrowthRate)
		mlf.emergencyCleanup()
	} else {
		mlf.routineCleanup()
	}
}

// emergencyCleanup 紧急清理
func (mlf *MemoryLeakFixer) emergencyCleanup() {
	log.Printf("开始紧急内存清理...")

	// 1. 清理全局数据结构
	mlf.cleanupGlobalDataStructures()

	// 2. 强制清理所有缓存
	mlf.forceCleanupCaches()

	// 3. 清理僵尸连接
	mlf.cleanupZombieConnections()

	// 4. 清理过期的跟踪资源
	mlf.cleanupExpiredResources()

	// 5. 强制垃圾回收
	runtime.GC()
	runtime.GC() // 执行两次确保彻底清理

	var afterMem runtime.MemStats
	runtime.ReadMemStats(&afterMem)

	log.Printf("紧急清理完成: 内存从 %dMB 降至 %dMB, Goroutines: %d",
		mlf.lastMemStats.Alloc/1024/1024, afterMem.Alloc/1024/1024, runtime.NumGoroutine())
}

// routineCleanup 常规清理
func (mlf *MemoryLeakFixer) routineCleanup() {
	// 轻量级清理
	if Cache != nil {
		Cache.DeleteExpired()
	}

	// 清理过期的跟踪资源
	mlf.cleanupExpiredResources()
}

// cleanupGlobalDataStructures 清理全局数据结构
func (mlf *MemoryLeakFixer) cleanupGlobalDataStructures() {
	// 清理报警数据
	AlertsLock.Lock()
	for alertID, serverData := range alertsStore {
		for serverID, dataPoints := range serverData {
			// 只保留最新的5个数据点
			if len(dataPoints) > 5 {
				alertsStore[alertID][serverID] = dataPoints[len(dataPoints)-5:]
			}
		}
	}
	AlertsLock.Unlock()

	// 清理ServiceSentinel数据
	if ServiceSentinelShared != nil {
		ServiceSentinelShared.emergencyCleanup()
	}
}

// forceCleanupCaches 强制清理所有缓存
func (mlf *MemoryLeakFixer) forceCleanupCaches() {
	if Cache != nil {
		// 清理所有缓存项，不只是过期的
		Cache.Flush()
	}
}

// cleanupZombieConnections 清理僵尸连接
func (mlf *MemoryLeakFixer) cleanupZombieConnections() {
	ServerLock.Lock()
	defer ServerLock.Unlock()

	now := time.Now()
	cleaned := 0

	for _, server := range ServerList {
		if server != nil && server.TaskClose != nil {
			// 更激进的清理：1分钟无活动就清理
			if now.Sub(server.LastActive) > time.Minute {
				server.TaskCloseLock.Lock()
				if server.TaskClose != nil {
					select {
					case server.TaskClose <- fmt.Errorf("emergency cleanup"):
					default:
					}
					server.TaskClose = nil
					server.TaskStream = nil
					cleaned++
				}
				server.TaskCloseLock.Unlock()
			}
		}
	}

	if cleaned > 0 {
		log.Printf("紧急清理了 %d 个僵尸连接", cleaned)
	}
}

// cleanupExpiredResources 清理过期的跟踪资源
func (mlf *MemoryLeakFixer) cleanupExpiredResources() {
	mlf.mu.Lock()
	defer mlf.mu.Unlock()

	// 清理可能泄漏的tickers
	for name, ticker := range mlf.activeTickers {
		// 这里可以添加更复杂的过期逻辑
		_ = name
		_ = ticker
	}
}

// cleanupAllResources 清理所有跟踪的资源
func (mlf *MemoryLeakFixer) cleanupAllResources() {
	mlf.mu.Lock()
	defer mlf.mu.Unlock()

	// 关闭所有channels
	for name, ch := range mlf.activeChannels {
		select {
		case <-ch:
		default:
			close(ch)
		}
		log.Printf("清理channel: %s", name)
	}

	// 停止所有tickers
	for name, ticker := range mlf.activeTickers {
		ticker.Stop()
		log.Printf("清理ticker: %s", name)
	}

	// 取消所有goroutines
	for name, cancel := range mlf.activeGoroutines {
		cancel()
		log.Printf("清理goroutine: %s", name)
	}

	// 清空所有映射
	mlf.activeChannels = make(map[string]chan struct{})
	mlf.activeTickers = make(map[string]*time.Ticker)
	mlf.activeGoroutines = make(map[string]context.CancelFunc)
}

// GetStats 获取内存泄漏修复器统计信息
func (mlf *MemoryLeakFixer) GetStats() map[string]interface{} {
	mlf.mu.RLock()
	defer mlf.mu.RUnlock()

	var currentMem runtime.MemStats
	runtime.ReadMemStats(&currentMem)

	return map[string]interface{}{
		"memory_mb":          currentMem.Alloc / 1024 / 1024,
		"goroutines":         runtime.NumGoroutine(),
		"growth_rate":        mlf.memoryGrowthRate,
		"tracked_channels":   len(mlf.activeChannels),
		"tracked_tickers":    len(mlf.activeTickers),
		"tracked_goroutines": len(mlf.activeGoroutines),
	}
}

// 全局内存泄漏修复器实例
var GlobalMemoryLeakFixer *MemoryLeakFixer
