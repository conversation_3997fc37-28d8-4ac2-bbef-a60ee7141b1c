version: 2
before:
  hooks:
    - go mod tidy -v
builds:
  - id: linux_arm64
    env:
      - CGO_ENABLED=1
      - CC=aarch64-linux-gnu-gcc
    ldflags:
      - -s -w
      - -X github.com/xOS/ServerStatus/service/singleton.Version={{.Version}}
      - -extldflags "-static -fpic"
    flags:
      - -trimpath
    goos:
      - linux
    goarch:
      - arm64
    main: ./cmd/dashboard
    binary: server-dash-{{ .Os }}-{{ .Arch }}
  - id: linux_amd64
    env:
      - CGO_ENABLED=1
      - CC=x86_64-linux-gnu-gcc
    ldflags:
      - -s -w
      - -X github.com/xOS/ServerStatus/service/singleton.Version={{.Version}}
      - -extldflags "-static -fpic"
    flags:
      - -trimpath
    goos:
      - linux
    goarch:
      - amd64
    main: ./cmd/dashboard
    binary: server-dash-{{ .Os }}-{{ .Arch }}
  - id: linux_s390x
    env:
      - CGO_ENABLED=1
      - CC=s390x-linux-gnu-gcc
    ldflags:
      - -s -w
      - -X github.com/xOS/ServerStatus/service/singleton.Version={{.Version}}
      - -extldflags "-static -fpic"
    flags:
      - -trimpath
    goos:
      - linux
    goarch:
      - s390x
    main: ./cmd/dashboard
    binary: server-dash-{{ .Os }}-{{ .Arch }}
  - id: windows_amd64
    env:
      - CGO_ENABLED=1
      - CC=x86_64-w64-mingw32-gcc
    ldflags:
      - -s -w
      - -X github.com/xOS/ServerStatus/service/singleton.Version={{.Version}}
      - -extldflags "-static -fpic"
    flags:
      - -trimpath
    goos:
      - windows
    goarch:
      - amd64
    main: ./cmd/dashboard
    binary: server-dash-{{ .Os }}-{{ .Arch }}
snapshot:
  version_template: "dashboard"
